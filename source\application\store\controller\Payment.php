<?php

namespace app\store\controller;

use app\store\model\Member as MemberModel;
use app\api\model\UserBindStore as UserBindStoreModel;
use app\store\model\Payment as PaymentModel;
use think\Db;

//<!--[meiye_07]-->
class Payment extends Controller
{
    public function index()
    {
        return $this->outPut();
    }

    public function getPaymentList()
    {
        $post = input("post.");
        $user = session("userinfo");
        $storeid=$user['storeid'];
        if(isset($post['storeid'])){
            $storeid=$post['storeid'];
        }


        $Model = new PaymentModel;
        if ($post['order_type'] != 0) {
            $where['order_type'] = $post['order_type'];
        }

        if (isset($post['keyword'])&&$post['keyword'] != '') {
            $uwhere['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['keyword'] . '%');
            if ($storeid != 0) {
                $ubswhere['storeid']=$storeid;
                $ubsfield="userid";
                $UserBindStoreModel=new UserBindStoreModel;
                $userlist=$UserBindStoreModel->getfieldList($ubswhere, $ubsfield);
                $uwhere['id'] = ['in', $userlist];//
            }

            $ubsfield="id";
            $MemberModel =new MemberModel;
            $memberlist=$MemberModel->getfieldList($uwhere, $ubsfield);
            $where['buyer_id'] = ['in', $memberlist];//
        }
        if ($post['startTime'] > $post['endTime']) {
            return $this->ajaxFail("开始时间必须小于结束时间");
        }
        if (!empty($post['startTime']) && !empty($post['endTime'])) {
            $startTime = substr($post['startTime'], 0, -3);
            $endTime = substr($post['endTime'], 0, -3);
            $str_time = $startTime . "," . $endTime;
            $where['tradedate'] = array("between", $str_time);
        }
        if ($storeid != 0) {
            $where['storeid'] = $storeid;
        }
        $where['merchantid'] = $user['merchantid'];
        if(isset($post['payment_method']) && $post['payment_method']!=0){
            $where['pay_type'] = intval($post['payment_method']);
        }
        $count = $Model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $ord = "cdate desc";
        $field = array(
            "id",
            "source_type",
            "trade_amount",
            "out_trade_no",
            "cdate",
            "storeid",
            "adminid",
            "order_state",
            "source_id",
            "tradedate",
            'pay_type as payTypeName',
            'buyer_id'
            
        );
        $data = $Model->getPayTypeArr(array('merchantid'=>$user['merchantid']))->getPaymentList($where, $page, $ord, $field);
        foreach ($data as &$item) {
            $item['memberInfo'] =$this->getmemberInfo($item);
            if($item['tradedate']){
                $item['tradedate'] = date("Y-m-d H:i:s", $item['tradedate']);
            }else{
                $item['tradedate'] = '--';
            }
            $item['cdate'] = date("Y-m-d H:i:s", $item['cdate']);
            if ($item['order_state'] == "SUCCESS") {
                $item['order_state'] = "<p style='color: #008573'>交易成功</p>";
            } elseif ($item['order_state'] == "REFUND") {
                $item['order_state'] = "<p style='color: #E5484D'>退款成功</p>";
            } elseif ($item['order_state'] == "WAIT_BUYER_PAY") {
                $item['order_state'] = "<p style='color: #b27310'>等待支付</p>";
            } elseif ($item['order_state'] == "REVOKED") {
                $item['order_state'] = "<p style='color: #9ca3af'>取消支付</p>";
            }
        }
        if($post['page']==1){
            // 统计当前 金额之和
            $map = $where;
            $map['order_state'] = "SUCCESS";
            $successPay = $Model->sumField($map);

            $map = $where;
            $map['order_state'] = "REFUND";
            $refundPay = $Model->sumField($map);

            $map = $where;
            $map['order_state'] = "WAIT_BUYER_PAY";
            $waitPay = $Model->sumField($map);
            $arr = array(
                'sPay'=>  $successPay,
                'rPay'=>  $refundPay,
                'wPay'=>  $waitPay,
            );
        }else{
            $arr = array(
                'sPay'=>  0,
                'rPay'=>  0,
                'wPay'=>  0,
            );
        }
        return $this->ajaxTable($data, $count, json_encode($arr));
    }

    // 获取会员信息
    public function getmemberInfo($data)
    {
        if (isset($data['buyer_id'])) {
            $where['id'] = $data['buyer_id'];
            $field = array(
                'id',
                'member_name',
                'phone',
                'pic'
            );
            $MemberModel =new MemberModel;
            $res = $MemberModel->getFinds($where,$field);
            return $res;
        } else {
            return array();
        }
    }
    public function paymentsee()
    {
        return $this->outPut("PaymentSee");
    }

    //获取时间
    public function GetTime()
    {
        $num = input("post.num");
        if($num == -1){
            $data['StartTime'] = strtotime(date("Y-m") . '-1 00:00:00') * 1000;
            $data['EndTime'] = time() * 1000;
        }else{
            $data['StartTime'] = strtotime(date("Y-m-d 00:00:00", strtotime("-" . $num . " day"))) * 1000;
            $data['EndTime'] = time() * 1000;
        }
        return $this->ajaxSuccess($data, "ok");
    }

    //导出订单列表
    public function importOrderList()
    {
        ini_set('max_execution_time','600');
        $post = input("get.");
        $user = session("userinfo");
        $Model = new PaymentModel;
        if ($post['order_type'] != 0) {
            $where['order_type'] = $post['order_type'];
        }
        if ($post['startTime'] > $post['endTime']) {
            return $this->ajaxFail("开始时间必须小于结束时间");
        }
        if (!empty($post['startTime']) && !empty($post['endTime'])) {
            $startTime = substr($post['startTime'], 0, -3);
            $endTime = substr($post['endTime'], 0, -3);
            $str_time = $startTime . "," . $endTime;
            $where['tradedate'] = array("between", $str_time);
        }
        if ($user['storeid'] != 0) {
            $where['storeid'] = $user['storeid'];
        }
        $where['merchantid'] = $user['merchantid'];
        if(isset($post['payment_method']) && $post['payment_method']!=0){
            $where['pay_type'] = intval($post['payment_method']);
        }
        $field = array(
            "id",
            "source_type",
            "trade_amount",
            "out_trade_no",
            "cdate",
            "storeid",
            "adminid",
            "order_state",
            "source_id",
            "tradedate",
            'pay_type as payTypeName',
            'trade_amount as price',
            'source_type as source',
            'storeid as storetag',
            'adminid as Cashier',
        );
        $dataMap = array(
            'map'=>$where,
            'field'=>$field
        );
        $data = $Model->getPayTypeArr(array('merchantid'=>$user['merchantid']))->getAllData($dataMap);
        if(is_object($data)){
            $data = $data->toArray();
        }
        foreach ($data as &$item) {
            if($item['tradedate']){
                $item['tradedate'] = date("Y-m-d H:i:s", $item['tradedate']);
            }else{
                $item['tradedate'] = '--';
            }
            $item['cdate'] = date("Y-m-d H:i:s", $item['cdate']);
            if ($item['order_state'] == "SUCCESS") {
                $item['order_state'] = "交易成功";
            } elseif ($item['order_state'] == "REFUND") {
                $item['order_state'] = "退款成功";
            } elseif ($item['order_state'] == "WAIT_BUYER_PAY") {
                $item['order_state'] = "等待支付";
            } elseif ($item['order_state'] == "REVOKED") {
                $item['order_state'] = "取消支付";
            }else{
                $item['order_state'] = "未知状态";
            }
        }
        $title = "收款流水明细表";
        $cellName = array(
            array(
                'out_trade_no', '单据号', '24',1
            ),
            array(
                'source', '流水来源', '24',1
            ),
            array(
                'price', '金额', '24',1
            ),
            array(
                'payTypeName', '支付方式', '24',1
            ),
            array(
                'storetag', '店铺', '24',1
            ),
            array(
                'Cashier', '收银员', '24',1
            ),
            array(
                'cdate', '创建时间', '24',1
            ),
            array(
                'tradedate', '支付时间', '24',1
            ),
            array(
                'order_state', '订单状态', '24',1
            ),
        );
        $this->exportOrderExcel($title, $cellName, $data);
    }

    public function exportOrderExcel($title, $cellName, $data)
    {
        //引入核心文件
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        //定义配置
        $topNumber = 2;//表头有几行占用
        $xlsTitle = iconv('utf-8', 'gb2312', $title);//文件名称
        $fileName = $title . date('_YmdHis');//文件名称
        $cellKey = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
            'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM',
            'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ'
        );

        //写在处理的前面（了解表格基本知识，已测试）
//     $objPHPExcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(20);//所有单元格（行）默认高度
//     $objPHPExcel->getActiveSheet()->getDefaultColumnDimension()->setWidth(20);//所有单元格（列）默认宽度
//     $objPHPExcel->getActiveSheet()->getRowDimension('1')->setRowHeight(30);//设置行高度
//     $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(30);//设置列宽度
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);//设置文字大小
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);//设置是否加粗
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);// 设置文字颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//设置文字居左（HORIZONTAL_LEFT，默认值）中（HORIZONTAL_CENTER）右（HORIZONTAL_RIGHT）
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);//设置填充颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->getStartColor()->setARGB('FF7F24');//设置填充颜色

        //处理表头标题
        $objPHPExcel->getActiveSheet()->mergeCells('A1:' . $cellKey[count($cellName) - 1] . '1');//合并单元格（如果要拆分单元格是需要先合并再拆分的，否则程序会报错）
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A1', '订单信息');
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        //处理表头
        foreach ($cellName as $k => $v) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue($cellKey[$k] . $topNumber, $v[1]);//设置表头数据
//            $objPHPExcel->getActiveSheet()->freezePane($cellKey[$k] . ($topNumber + 1));//冻结窗口
            $objPHPExcel->getActiveSheet()->getStyle($cellKey[$k] . $topNumber)->getFont()->setBold(true);//设置是否加粗
            $objPHPExcel->getActiveSheet()->getStyle($cellKey[$k] . $topNumber)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
            if (isset($v[2]) && $v[2] > 0)//大于0表示需要设置宽度
            {
                $objPHPExcel->getActiveSheet()->getColumnDimension($cellKey[$k])->setWidth($v[2]);//设置列宽度
            }
        }
        //处理数据
        foreach ($data as $k => $v) {
            foreach ($cellName as $k1 => $v1) {
                $objPHPExcel->getActiveSheet()->setCellValue($cellKey[$k1] . ($k + 1 + $topNumber), $v[$v1[0]]);
            }
        }
        //导出execl
        header('pragma:public');
        header('Content-type:application/vnd.ms-excel;charset=utf-8;name="' . $xlsTitle . '.xls"');
        header("Content-Disposition:attachment;filename=$fileName.xls");//attachment新窗口打印inline本窗口打印
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output');
        exit;
    }
}