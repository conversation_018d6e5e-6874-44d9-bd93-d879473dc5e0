<!--[meiye_09_01]-->
<div class="layui-fluid" id="serviceIndex" v-cloak>
  <div v-loading="loading2">
    <el-card
      class="box-card"
      style="min-height: 100%"
      shadow="never"
      v-if="user.storeid == 0"
    >
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="总部服务" name="srevice">
          <template>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div style="margin: 0 0 10px 10px">
                    <el-button type="primary" size="small" @click="add"
                      >添加服务</el-button
                    >
                  </div>
                  <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="dialog">分类管理</el-button>
                  </div>
                  <!-- <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="labelMa"
                      >标签管理</el-button
                    >
                  </div> -->
                  <!-- <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="adjustPriceDialog=true"
                      >调价管理
                    </el-button>
                  </div> -->
                </div>
              </el-col>
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    v-model="name"
                    placeholder="请输入服务名称"
                    size="small"
                  >
                    <el-button
                      type="primary"
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
            <el-row type="flex" style="margin-bottom: 20px">
              <el-col :span="16">
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>选择分类</span>
                    </div>
                    <el-select
                      v-model="classification"
                      placeholder="请选择分类"
                      size="small"
                      @change="IfiQuery"
                    >
                      <el-option value="" label="全部分类"></el-option>
                      <el-option
                        v-for="item in classificationlist"
                        :key="item.id"
                        :label="item.classification_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>产品状态</span>
                    </div>
                    <el-select
                      v-model="ZStatus"
                      placeholder="请选择状态"
                      size="small"
                      @change="ZStatusSee"
                    >
                      <el-option value="" label="全部状态"></el-option>
                      <el-option value="1" label="已上架"></el-option>
                      <el-option value="2" label="未上架"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div>
              <el-table
                @selection-change="HQSelection"
                :key="activeName+'_1'"
                :data="tableData"
                style="width: 100%"
              >
                <!-- <template slot="header" slot-scope="scope">
                    <span style="float: right">
                      <el-popover placement="left" width="100" trigger="click">
                        <div>
                          <template
                            v-for="(item,key) in serviceTableColumnSrevice"
                          >
                            <div style="margin-top: 10px">
                              <el-checkbox
                                v-model="item.checked"
                                :true-label="1"
                                :disabled="item.disabled"
                                :false-label="0"
                              >
                                <span v-text="item.name"></span>
                              </el-checkbox>
                            </div>
                          </template>
                        </div>
                        <i class="el-icon-setting" slot="reference"></i>
                      </el-popover>
                    </span>
                  </template> -->
                <el-table-column type="selection" width="35"> </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnSrevice[0]['checked']"
                  prop="service_name"
                  label="服务"
                  min-width="200"
                >
                  <template slot-scope="scope">
                    <div class="flex items-center space-x-2">
                      <div
                        style="
                          border-radius: 4px;
                          width: 60px;
                          overflow: hidden;
                          flex-shrink: 0;
                          display: flex;
                          flex-direction: column;
                        "
                      >
                        <el-image
                          :preview-src-list="scope.row.img_arr"
                          style="width: 60px; height: 60px"
                          fit="cover"
                          :src="scope.row.img"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline text-gray"></i>
                          </div>
                        </el-image>
                        <template v-if="scope.row.is_sku==1">
                          <el-tag size="mini">有规格</el-tag>
                        </template>
                      </div>
                      <div>
                        <el-tooltip placement="top-start" effect="light">
                          <div slot="content">
                            <div class="font-bold">
                              {{scope.row.service_name}}
                            </div>
                          </div>
                          <div class="font-bold line-clamp-1">
                            {{scope.row.service_name}}
                          </div>
                        </el-tooltip>
                        <div class="text-gray text-xs">
                          {{scope.row.bar_code}}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[1]['checked']"
                  prop="duration"
                  label="时长(分)"
                  width="auto"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[1]['checked']"
                  prop="price"
                  label="价格"
                  width="100"
                  align="right"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[2]['checked']"
                  prop="classification_id"
                  label="分类"
                  min-width="90"
                  align="center"
                >
                </el-table-column>
                <!-- <el-table-column
                  v-if="serviceTableColumnSrevice[3]['checked']"
                  prop="label_id"
                  label="标签"
                >
                </el-table-column> -->
                <el-table-column
                  v-if="serviceTableColumnSrevice[4]['checked']"
                  prop="selling_stores"
                  label="在售门店"
                  align="center"
                >
                  <template slot-scope="scope">
                    <span
                      class="text-primary cursor-pointer underline px-2"
                      @click="showhasAddStore(scope.row.selling_stores)"
                      v-if="scope.row.selling_stores && scope.row.selling_stores.length>0"
                      >{{scope.row.selling_stores.length}}</span
                    >
                    <span
                      v-if="!scope.row.selling_stores || scope.row.selling_stores.length==0"
                      >0</span
                    >
                  </template>
                </el-table-column>
                <!-- <el-table-column
                      v-if="serviceTableColumnSrevice[5]['checked']"
                      prop="consumables"
                      label="耗材"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="mini"
                          @click="toShowRelevanceConsumables(scope.row,1)"
                        >
                          <div v-if="scope.row.consumables">查看耗材</div>
                          <div
                            style="color: rgb(245, 108, 108)"
                            v-if="!scope.row.consumables"
                          >
                            前往设置
                          </div>
                        </el-button>
                      </template>
                    </el-table-column> -->
                <el-table-column
                  align="center"
                  v-if="serviceTableColumnSrevice[6]['checked']"
                  prop="status"
                  label="状态"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.status==2?'danger':''"
                    >
                      {{ scope.row.status==2?'已下架':'已上架' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnSrevice[7]['checked']"
                  prop="status"
                  label="操作"
                  align="center"
                  width="160"
                >
                  <template slot-scope="scope">
                    <!--<el-button-->
                    <!--type="primary"-->
                    <!--size="mini"-->
                    <!--@click="setMemberPrice(scope.row,1)">会员价-->
                    <!--</el-button>-->
                    <el-button
                      type="primary"
                      size="mini"
                      plain
                      @click="Edit(scope.row.id)"
                      >编辑
                    </el-button>
                    <el-dropdown
                      @command="handleCommand"
                      style="margin-right: 10px"
                    >
                      <el-button plain size="mini"> 更多 </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          @click.native.stop="Servicetotal(scope.row.id)"
                        >
                          详情
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="scope.row.status == 2"
                          :command="'toPutaway-'+scope.row.id"
                        >
                          上架
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="scope.row.status == 1"
                          :command="'toSoldOut-'+scope.row.id"
                        >
                          下架
                        </el-dropdown-item>
                        <el-dropdown-item :command="'Del-'+scope.row.id">
                          删除
                        </el-dropdown-item>
                        <!-- <el-dropdown-item
                          @click.native="setMemberPrice(scope.row,1)"
                        >
                          会员价
                        </el-dropdown-item> -->
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
              <div style="overflow: hidden">
                <div style="float: left; margin-top: 10px">
                  <el-dropdown>
                    <el-button size="small"> 批量操作 </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="HQbatch(1)"
                        >上架</el-dropdown-item
                      >
                      <el-dropdown-item @click.native="HQbatch(2)"
                        >下架</el-dropdown-item
                      >
                      <el-dropdown-item
                        v-if="isMerchant==1"
                        @click.native="HQbatch(3)"
                        >删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-dropdown>
                    <el-button size="small"> 批量设置 </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="HQbatch(4)"
                        >调价范围</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="size_change"
                  @current-change="current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                >
                </el-pagination>
              </div>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="门店服务" name="store">
          <template>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <el-select
                  v-model="storeid"
                  placeholder="请选择门店"
                  size="small"
                  @change="StoreQuery"
                >
                  <el-option
                    v-for="item in storeList"
                    :key="item.id"
                    :label="item.storetag"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    v-model="name"
                    placeholder="请输入服务名称"
                    size="small"
                  >
                    <el-button
                      type="primary"
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>选择分类</span>
                    </div>
                    <el-select
                      v-model="Storeclassification"
                      placeholder="请选择分类"
                      size="small"
                      @change="StoIfiQuery"
                    >
                      <el-option value="" label="全部分类"></el-option>
                      <el-option
                        v-for="item in classificationlist"
                        :key="item.id"
                        :label="item.classification_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>请选择状态</span>
                    </div>
                    <el-select
                      v-model="status"
                      placeholder="请选择状态"
                      size="small"
                      @change="StatusQuery"
                    >
                      <el-option value="" label="全部状态"></el-option>
                      <el-option value="1" label="已上架"></el-option>
                      <el-option value="2" label="未上架"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div>
              <el-table
                :key="activeName+'_2'"
                :data="stotableData"
                style="width: 100%"
              >
                <!-- <template slot="header" slot-scope="scope">
                    <span style="float: right">
                      <el-popover placement="left" width="100" trigger="click">
                        <div>
                          <template
                            v-for="(item,key) in serviceTableColumnStore"
                          >
                            <div style="margin-top: 10px">
                              <el-checkbox
                                v-model="item.checked"
                                :true-label="1"
                                :disabled="item.disabled"
                                :false-label="0"
                              >
                                <span v-text="item.name"></span>
                              </el-checkbox>
                            </div>
                          </template>
                        </div>
                        <i class="el-icon-setting" slot="reference"></i>
                      </el-popover>
                    </span>
                  </template> -->
                <el-table-column
                  v-if="serviceTableColumnStore[0]['checked']"
                  prop="name"
                  label="服务"
                  min-width="200"
                >
                  <template slot-scope="scope">
                    <div class="flex items-center space-x-2">
                      <div
                        style="
                          border-radius: 4px;
                          width: 60px;
                          overflow: hidden;
                          flex-shrink: 0;
                          display: flex;
                          flex-direction: column;
                        "
                      >
                        <el-image
                          :preview-src-list="scope.row.img_arr"
                          style="width: 60px; height: 60px"
                          fit="cover"
                          :src="scope.row.img"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline text-gray"></i>
                          </div>
                        </el-image>
                        <template v-if="scope.row.is_sku==1">
                          <el-tag size="mini">有规格</el-tag>
                        </template>
                      </div>
                      <div class="flex-1">
                        <el-tooltip placement="top-start" effect="light">
                          <div slot="content">
                            <div>
                              <div class="font-bold">
                                {{scope.row.service_name}}
                              </div>
                              <div
                                v-if="scope.row.service_name!=scope.row.originalName"
                              >
                                总部命名：{{scope.row.originalName}}
                              </div>
                            </div>
                          </div>
                          <div>
                            <div class="font-bold line-clamp-1">
                              {{scope.row.service_name}}
                            </div>
                            <div
                              class="text-gray text-xs line-clamp-1"
                              v-if="scope.row.service_name!=scope.row.originalName"
                            >
                              {{scope.row.originalName}}
                            </div>
                          </div>
                        </el-tooltip>
                        <div class="text-gray text-xs">
                          {{scope.row.bar_code}}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnStore[1]['checked']"
                  prop="duration"
                  label="时长(分)"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnStore[1]['checked']"
                  prop="price"
                  label="价格"
                  width="100"
                  align="right"
                >
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnStore[1]['checked']"
                  prop="sellnum"
                  label="销量"
                  width="auto"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnStore[2]['checked']"
                  prop="classification_id"
                  label="分类"
                  min-width="90"
                  align="center"
                >
                </el-table-column>
                <!-- <el-table-column
                  v-if="serviceTableColumnStore[3]['checked']"
                  prop="label_id"
                  label="标签"
                >
                </el-table-column> -->
                <el-table-column
                  v-if="serviceTableColumnStore[4]['checked']"
                  prop="shop_display"
                  label="网店展示"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.shop_display=='是'?'':'info'"
                    >
                      {{ scope.row.shop_display }}
                    </el-tag>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                      align="center"
                      v-if="serviceTableColumnStore[5]['checked']"
                      prop="consumables"
                      label="耗材"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="mini"
                          @click="toShowRelevanceConsumables(scope.row,2)"
                        >
                          <div v-if="scope.row.consumables">查看耗材</div>
                          <div
                            style="color: rgb(245, 108, 108)"
                            v-if="!scope.row.consumables"
                          >
                            未设置
                          </div>
                        </el-button>
                      </template>
                    </el-table-column> -->
                <el-table-column
                  align="center"
                  v-if="serviceTableColumnStore[6]['checked']"
                  prop="state"
                  label="状态"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.status==2?'danger':''"
                    >
                      {{ scope.row.status==2?'已下架':'已上架' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="form_store" label="所属">
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.form_store==1?'':'info'"
                    >
                      {{ scope.row.form_store==1?'门店创建':'来自总部' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="serviceTableColumnStore[7]['checked']"
                  prop=""
                  label="操作"
                  align="center"
                  width="80"
                >
                  <template slot-scope="scope">
                    <!-- <el-popover placement="right" width="320" trigger="click">
                      <div>
                        <p style="text-align: center; margin-top: 16px">
                          扫一扫，在手机上查看并分享
                        </p>
                        <div style="text-align: center">
                          <el-image :src="serviceCode" alt="" width="240px">
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                        </div>
                        <div style="text-align: center; margin: 10px 0px">
                          <el-link
                            :href="serviceCode"
                            :download="'【服务】'+scope.row.service_name"
                            style="color: #3e63dd"
                            :disabled="serviceCode==''"
                            :underline="false"
                            >下载图片
                          </el-link>
                        </div>
                      </div>
                      <el-button
                        slot="reference"
                        @click="getCode(scope.row.service_id,scope.row.store_id)"
                        type="primary"
                        size="mini"
                        plain
                        >推广
                      </el-button>
                    </el-popover> -->
                    <el-button
                      type="primary"
                      size="mini"
                      plain
                      @click.native="SeeService(scope.row.id)"
                      >详情</el-button
                    >
                    <!-- <el-dropdown>
                      <el-button plain size="mini"> 更多 </el-button>
                      <el-dropdown-menu slot="dropdown">
                       <el-dropdown-item
                          @click.native="setMemberPrice(scope.row,3)"
                        >
                          会员价
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click.native="associateStaff(scope.row)"
                          >关联{$storeCraftsmanName}</el-dropdown-item
                        >
                        <el-dropdown-item
                          @click.native="SeeService(scope.row.id)"
                          >详情</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown> -->
                  </template>
                </el-table-column>
              </el-table>
              <div style="overflow: hidden">
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="sto_size_change"
                  @current-change="sto_current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="stototal"
                >
                </el-pagination>
              </div>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-card class="box-card" style="min-height: 100%" shadow="never" v-else>
      <el-tabs v-model="activeName" type="card" @tab-click="stohandleClick">
        <el-tab-pane label="门店服务" name="store">
          <template>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col
                ><el-button type="primary" size="small" @click="add"
                  >添加服务</el-button
                ></el-col
              >
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    v-model="name"
                    placeholder="请输入服务名称"
                    size="small"
                  >
                    <el-button
                      type="primary"
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>选择分类</span>
                    </div>
                    <el-select
                      v-model="Storeclassification"
                      placeholder="请选择分类"
                      size="small"
                      @change="StoIfiQuery"
                    >
                      <el-option value="" label="全部分类"></el-option>
                      <el-option
                        v-for="item in classificationlist"
                        :key="item.id"
                        :label="item.classification_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>请选择状态</span>
                    </div>
                    <el-select
                      v-model="status"
                      placeholder="请选择状态"
                      size="small"
                      @change="StatusQuery"
                    >
                      <el-option value="" label="全部状态"></el-option>
                      <el-option value="1" label="已上架"></el-option>
                      <el-option value="2" label="未上架"></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div>
              <el-table
                @selection-change="STSelection"
                :key="activeName+'_3'"
                :data="stotableData"
                style="width: 100%"
              >
                <!-- <template slot="header" slot-scope="scope">
                    <span style="float: right">
                      <el-popover placement="left" width="100" trigger="click">
                        <div>
                          <template
                            v-for="(item,key) in storeServiceTableColumnStore"
                          >
                            <div style="margin-top: 10px">
                              <el-checkbox
                                v-model="item.checked"
                                :true-label="1"
                                :disabled="item.disabled"
                                :false-label="0"
                              >
                                <span v-text="item.name"></span>
                              </el-checkbox>
                            </div>
                          </template>
                        </div>
                        <i class="el-icon-setting" slot="reference"></i>
                      </el-popover>
                    </span>
                  </template> -->
                <el-table-column type="selection" width="35"> </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[0]['checked']"
                  prop="name"
                  label="服务"
                  min-width="200"
                >
                  <template slot-scope="scope">
                    <div class="flex items-center space-x-2">
                      <div
                        style="
                          border-radius: 4px;
                          width: 60px;
                          overflow: hidden;
                          flex-shrink: 0;
                          display: flex;
                          flex-direction: column;
                        "
                      >
                        <el-image
                          :preview-src-list="scope.row.img_arr"
                          style="width: 60px; height: 60px"
                          fit="cover"
                          :src="scope.row.img"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline text-gray"></i>
                          </div>
                        </el-image>
                        <template v-if="scope.row.is_sku==1">
                          <el-tag size="mini">有规格</el-tag>
                        </template>
                      </div>
                      <div class="flex-1">
                        <el-tooltip placement="top-start" effect="light">
                          <div slot="content">
                            <div>
                              <div class="font-bold">
                                {{scope.row.service_name}}
                              </div>
                              <div
                                v-if="scope.row.service_name!=scope.row.originalName"
                              >
                                总部命名：{{scope.row.originalName}}
                              </div>
                            </div>
                          </div>
                          <div>
                            <div class="font-bold line-clamp-1">
                              {{scope.row.service_name}}
                            </div>
                            <div
                              class="text-gray text-xs line-clamp-1"
                              v-if="scope.row.service_name!=scope.row.originalName"
                            >
                              {{scope.row.originalName}}
                            </div>
                          </div>
                        </el-tooltip>
                        <div class="text-gray text-xs">
                          {{scope.row.bar_code}}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[1]['checked']"
                  prop="duration"
                  label="时长(分)"
                  width="auto"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[1]['checked']"
                  prop="price"
                  label="价格"
                  width="100"
                  align="right"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[5]['checked']"
                  prop="sellnum"
                  label="销量"
                  width="auto"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[2]['checked']"
                  prop="classification_id"
                  label="分类"
                  min-width="90"
                  align="center"
                >
                </el-table-column>
                <!-- <el-table-column
                  v-if="storeServiceTableColumnStore[3]['checked']"
                  prop="label_id"
                  label="标签"
                >
                </el-table-column> -->
                <el-table-column
                  v-if="storeServiceTableColumnStore[4]['checked']"
                  prop="shop_display"
                  label="网店展示"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.shop_display=='是'?'':'info'"
                    >
                      {{ scope.row.shop_display }}
                    </el-tag>
                  </template>
                </el-table-column>

                <!-- <el-table-column
                      v-if="storeServiceTableColumnStore[6]['checked']"
                      prop="consumables"
                      label="耗材"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="mini"
                          @click="toShowRelevanceConsumables(scope.row,2)"
                        >
                          <div v-if="scope.row.consumables">查看耗材</div>
                          <div
                            style="color: rgb(245, 108, 108)"
                            v-if="!scope.row.consumables"
                          >
                            未设置
                          </div>
                        </el-button>
                      </template>
                    </el-table-column> -->
                <el-table-column
                  align="center"
                  v-if="storeServiceTableColumnStore[7]['checked']"
                  prop="state"
                  label="状态"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.status==2?'danger':''"
                    >
                      {{ scope.row.status==2?'已下架':'已上架' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="form_store" label="所属">
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.form_store==1?'':'info'"
                    >
                      {{ scope.row.form_store==1?'门店创建':'来自总部' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="sort"
                  width="100"
                  label="排序"
                >
                  <template slot="header">
                    <el-tooltip class="item" effect="light" placement="right">
                      <div slot="content">
                        <p>排序数值越大越靠前；</p>
                        <p>数值需设置为整数；</p>
                        <p>数值最大9999；</p>
                        <p>
                          商品排序可应用于商城小程序【商城页面】和收银机点单页面；
                        </p>
                        <p>回车保存排序数值。</p>
                      </div>
                      <span>排序 <i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <el-input
                      @keyup.enter.native="saveSort(scope.row)"
                      maxlength="4"
                      show-word-limit
                      v-model="scope.row.sort"
                      size="mini"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnStore[8]['checked']"
                  prop=""
                  label="操作"
                  align="center"
                  width="160"
                >
                  <template slot-scope="scope">
                    <el-button
                      type="primary"
                      plain
                      size="mini"
                      @click="EditStoSer(scope.row.id,scope.row.form_store)"
                      >编辑
                    </el-button>
                    <!-- <el-popover placement="right" width="320" trigger="click">
                      <div>
                        <p style="text-align: center; margin-top: 16px">
                          扫一扫，在手机上查看并分享
                        </p>
                        <div style="text-align: center">
                          <el-image :src="serviceCode" alt="" width="240px">
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                        </div>
                        <div style="text-align: center; margin: 10px 0px">
                          <el-link
                            :href="serviceCode"
                            :download="'【服务】'+scope.row.service_name"
                            style="color: #3e63dd"
                            :disabled="serviceCode==''"
                            :underline="false"
                            >下载图片
                          </el-link>
                        </div>
                      </div>
                      <el-button
                        slot="reference"
                        @click="getCode(scope.row.service_id,scope.row.store_id)"
                        type="primary"
                        size="mini"
                        plain
                        >推广
                      </el-button>
                    </el-popover> -->
                    <el-dropdown
                      @commandss="handleCommand"
                      style="margin-right: 10px"
                    >
                      <el-button plain size="mini"> 更多 </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          @click.native.stop="SeeStoService(scope.row.id)"
                        >
                          详情
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="scope.row.status == 2"
                          @click.native.stop="StotoPutaway(scope.row.id,scope.row.service_id)"
                        >
                          上架
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click.native.stop="StotoSoldOut(scope.row.id)"
                          v-if="scope.row.status == 1"
                        >
                          下架
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click.native.stop="associateStaff(scope.row)"
                        >
                          关联{$storeCraftsmanName}
                        </el-dropdown-item>
                        <!-- <el-dropdown-item
                              @click.native="setMemberPrice(scope.row,3)"
                            >
                              会员价
                            </el-dropdown-item> -->
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
              <div style="overflow: hidden">
                <div style="float: left; margin-top: 10px">
                  <el-dropdown>
                    <el-button size="small"> 批量操作 </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="STbatch(1)"
                        >上架</el-dropdown-item
                      >
                      <el-dropdown-item @click.native="STbatch(2)"
                        >下架</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="sto_size_change"
                  @current-change="sto_current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="stototal"
                >
                </el-pagination>
              </div>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="总部服务" name="srevice">
          <template>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col> </el-col>
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    v-model="name"
                    placeholder="请输入服务名称"
                    size="small"
                  >
                    <el-button
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col :span="16">
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div>
                    <div
                      class="el-button el-button--default is-plain"
                      style="border: none; color: #606266 !important"
                    >
                      <span>选择分类</span>
                    </div>
                    <el-select
                      v-model="classification"
                      placeholder="请选择分类"
                      size="small"
                      @change="IfiQuery"
                    >
                      <el-option value="" label="全部分类"></el-option>
                      <el-option
                        v-for="item in classificationlist"
                        :key="item.id"
                        :label="item.classification_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
              <el-col :span="8" align="right">
                <el-button
                  type="primary"
                  plain
                  @click="AddstoSer(0)"
                  size="mini"
                  >批量添加至本店
                </el-button>
              </el-col>
            </el-row>
            <div>
              <el-table
                :data="tableData"
                :key="activeName+'_4'"
                @selection-change="handleSelectionChange"
                style="width: 100%"
              >
                <!-- <template slot="header" slot-scope="scope">
                    <span style="float: right">
                      <el-popover placement="left" width="100" trigger="click">
                        <div>
                          <template
                            v-for="(item,key) in storeServiceTableColumnSrevice"
                          >
                            <div style="margin-top: 10px">
                              <el-checkbox
                                v-model="item.checked"
                                :true-label="1"
                                :disabled="item.disabled"
                                :false-label="0"
                              >
                                <span v-text="item.name"></span>
                              </el-checkbox>
                            </div>
                          </template>
                        </div>
                        <i class="el-icon-setting" slot="reference"></i>
                      </el-popover>
                    </span>
                  </template> -->
                <el-table-column type="selection" width="35"></el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[0]['checked']"
                  prop="service_name"
                  label="服务"
                  width="200"
                >
                  <template slot-scope="scope">
                    <div class="flex items-center space-x-2">
                      <div
                        style="
                          border-radius: 4px;
                          width: 60px;
                          overflow: hidden;
                          flex-shrink: 0;
                          display: flex;
                          flex-direction: column;
                        "
                      >
                        <el-image
                          :preview-src-list="scope.row.img_arr"
                          style="width: 60px; height: 60px"
                          fit="cover"
                          :src="scope.row.img"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline text-gray"></i>
                          </div>
                        </el-image>
                        <template v-if="scope.row.is_sku==1">
                          <el-tag size="mini">有规格</el-tag>
                        </template>
                      </div>
                      <div>
                        <el-tooltip placement="top-start" effect="light">
                          <div slot="content">
                            <div class="font-bold">
                              {{scope.row.service_name}}
                            </div>
                          </div>
                          <div class="font-bold line-clamp-1">
                            {{scope.row.service_name}}
                          </div>
                        </el-tooltip>
                        <div class="text-gray text-xs">
                          {{scope.row.bar_code}}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[1]['checked']"
                  prop="duration"
                  label="时长(分)"
                  width="auto"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[1]['checked']"
                  prop="price"
                  label="价格"
                  width="100"
                  align="right"
                >
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[2]['checked']"
                  prop="classification_id"
                  label="分类"
                  min-width="90"
                  align="center"
                >
                </el-table-column>
                <!-- <el-table-column
                  v-if="storeServiceTableColumnSrevice[3]['checked']"
                  prop="label_id"
                  label="标签"
                >
                </el-table-column> -->
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[4]['checked']"
                  prop="shop_display"
                  label="网店展示"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.shop_display=='是'?'':'info'"
                    >
                      {{ scope.row.shop_display }}
                    </el-tag>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                      v-if="storeServiceTableColumnSrevice[5]['checked']"
                      prop="consumables"
                      label="耗材"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="mini"
                          @click="toShowRelevanceConsumables(scope.row,3)"
                        >
                          <div v-if="scope.row.consumables">查看耗材</div>
                          <div
                            style="color: rgb(245, 108, 108)"
                            v-if="!scope.row.consumables"
                          >
                            未设置
                          </div>
                        </el-button>
                      </template>
                    </el-table-column> -->
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[6]['checked']"
                  align="center"
                  prop="state"
                  label="状态"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="medium"
                      :type="scope.row.status==2?'danger':''"
                    >
                      {{ scope.row.status==2?'已下架':'已上架' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="storeServiceTableColumnSrevice[7]['checked']"
                  prop="status"
                  label="操作"
                  align="center"
                  width="160"
                >
                  <template slot-scope="scope">
                    <!-- <el-button
                          type="primary"
                          size="mini"
                          @click="setMemberPrice(scope.row,1)"
                        >
                          会员价
                        </el-button> -->
                    <el-button
                      type="primary"
                      plain
                      size="mini"
                      @click="Servicetotal(scope.row.id)"
                    >
                      详情
                    </el-button>
                    <el-button
                      type="primary"
                      size="mini"
                      v-if="scope.row.is_Add_store == true"
                      >已加入
                    </el-button>
                    <el-button
                      type="primary"
                      plain
                      @click="AddstoSer(scope.row.id)"
                      size="mini"
                      v-else
                      >加&nbsp;&nbsp;入
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div style="overflow: hidden">
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="size_change"
                  @current-change="current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                >
                </el-pagination>
              </div>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
  <el-dialog
    title="分类管理"
    :visible.sync="dialogTableVisible"
    append-to-body
    width="900px"
  >
    <div class="layui-form-item">
      <div class="layui-inline">
        <el-popover placement="right" width="200" trigger="click" v-model="clo">
          <el-form
            @submit.native.prevent
            ref="form"
            :model="ifi"
            label-width=""
            size="small"
          >
            <el-form-item label="">
              <el-col :span="24">
                <el-input placeholder="输入名称" v-model="ifi.name"> </el-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="Adifi"
                >确定</el-button
              >
              <el-button size="small" @click="clo = false">取消 </el-button>
            </el-form-item>
          </el-form>
          <el-button
            type="primary"
            size="small"
            slot="reference"
            @click="ifi.name=''"
            >添加分类</el-button
          >
        </el-popover>
        <el-button v-if="shw" type="primary" size="small" @click="hid"
          >完成</el-button
        >
        <el-button v-else type="primary" size="small" @click="Shows"
          >编辑</el-button
        >
      </div>
    </div>
    <div>
      <el-table :data="ifiData" style="width: 100%">
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column prop="classification_name" label="分类">
          <template slot-scope="scope">
            <el-input
              v-if="shw"
              v-model="scope.row.classification_name"
              size="medium"
            ></el-input>
            <span v-else>{{scope.row.classification_name}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="addtime" label="创建时间"> </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="Delifi(scope.row.id)"
              >删除
            </el-button>
            <el-button
              v-if="shw"
              type="text"
              size="mini"
              @click="Editifi(scope.row.classification_name,scope.row.id)"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          background
          @size-change="ifi_size_change"
          @current-change="ifi_current_change"
          :pager-count="5"
          :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="ifitotal"
        >
        </el-pagination>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    title="标签管理"
    :visible.sync="dialogTablelabel"
    append-to-body
    width="900px"
  >
    <div class="layui-form-item">
      <div class="layui-inline">
        <el-popover
          placement="right"
          width="200"
          trigger="click"
          v-model="labclo"
        >
          <el-form
            @submit.native.prevent
            ref="form"
            :model="lab"
            label-width=""
            size="small"
          >
            <el-form-item label="">
              <el-col :span="24">
                <el-input placeholder="输入名称" v-model="lab.name"> </el-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="Adlab"
                >确定</el-button
              >
              <el-button size="small" @click="labclo = false">取消 </el-button>
            </el-form-item>
            <!--el-icon-circle-close-->
          </el-form>
          <el-button size="small" slot="reference" @click="lab.name=''"
            >添加标签</el-button
          >
        </el-popover>
        <el-button v-if="labshw" type="primary" size="small" @click="labhid"
          >完成
        </el-button>
        <el-button v-else type="primary" size="small" @click="labShows"
          >编辑</el-button
        >
      </div>
    </div>
    <div>
      <el-table :data="labelData" style="width: 100%">
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column prop="label_name" label="标签">
          <template slot-scope="scope">
            <el-input
              v-if="labshw"
              v-model="scope.row.label_name"
              size="medium"
            ></el-input>
            <span v-else>{{scope.row.label_name}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="addtime" label="创建时间"> </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="Dellab(scope.row.id)"
              >删除
            </el-button>
            <el-button
              v-if="labshw"
              type="text"
              size="mini"
              @click="Editlab(scope.row.label_name,scope.row.id)"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          background
          @size-change="lab_size_change"
          @current-change="lab_current_change"
          :pager-count="5"
          :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="labtotal"
        >
        </el-pagination>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    title="调价管理"
    :visible.sync="adjustPriceDialog"
    append-to-body
    width="900px"
  >
    <el-popover
      placement="right"
      width="300"
      v-model="adjustPriceEdit"
      trigger="click"
    >
      <el-form
        ref="adjustPriceForm"
        :model="adjustPriceData"
        :rules="adjustPriceRules"
        size="small"
        label-width="60px"
      >
        <el-form-item label="">
          <el-col :span="24">
            <span style="font-weight: bolder">方案设置</span>
          </el-col>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-col :span="24">
            <el-input placeholder="方案名称" v-model="adjustPriceData.name">
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-col :span="24">
            <el-radio
              v-model="adjustPriceData.type"
              :label="1"
              :disabled="adjustPriceData.id>0"
              >数值调整
            </el-radio>
            <el-radio
              v-model="adjustPriceData.type"
              :label="2"
              :disabled="adjustPriceData.id>0"
              >比例调整
            </el-radio>
          </el-col>
        </el-form-item>
        <el-form-item label="上调" prop="increase">
          <el-col :span="24">
            <el-input v-model="adjustPriceData.increase">
              <template slot="prepend">上调</template>
              <template slot="append"
                ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
              </template>
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="下调" prop="decrease">
          <el-col :span="24">
            <el-input v-model="adjustPriceData.decrease">
              <template slot="prepend">下调</template>
              <template slot="append"
                ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
              </template>
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            :loading="addnow"
            @click="confirmAdjustPrice"
            >确定
          </el-button>
          <el-button size="small" @click="adjustPriceEdit = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
      <el-button
        @click="toAddAdjustPrice"
        type="primary"
        size="small"
        slot="reference"
      >
        <span v-text="'设置调价方案'"></span>
      </el-button>
    </el-popover>
    <el-table
      :data="adjustPriceTableData"
      style="width: 100%; margin-top: 15px"
    >
      <el-table-column show-overflow-tooltip prop="name" label="名称">
      </el-table-column>
      <el-table-column prop="typeName" label="类型"> </el-table-column>
      <el-table-column prop="increase" label="上调"> </el-table-column>
      <el-table-column prop="decrease" label="下调"> </el-table-column>
      <el-table-column label="使用次数">
        <el-table-column prop="productNum" label="产品"> </el-table-column>
        <el-table-column prop="serviceNum" label="服务"> </el-table-column>
      </el-table-column>
      <el-table-column prop="status" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="delAdjustPrice(scope.row.id)"
            >删除
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click.stop="adjustPriceData=scope.row;adjustPriceEdit=true;"
          >
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="overflow: hidden">
      <el-pagination
        style="margin-top: 10px; float: right"
        v-if="adjustPricePageObj.total>0"
        background
        :current-page="adjustPricePageObj.page"
        @current-change="adjustPriceCurrentChange"
        :page-size="adjustPricePageObj.limit"
        layout="total, prev, pager, next"
        :total="adjustPricePageObj.total"
      >
      </el-pagination>
    </div>
  </el-dialog>
  <el-dialog
    title="调价设置"
    :visible.sync="adjustPriceSetDialog"
    append-to-body
    width="900px"
  >
    <el-table
      :data="adjustPriceTableData"
      style="width: 100%; margin-top: 15px"
    >
      <el-table-column show-overflow-tooltip prop="name" label="名称">
      </el-table-column>
      <el-table-column prop="typeName" label="类型"> </el-table-column>
      <el-table-column prop="increase" label="上调"> </el-table-column>
      <el-table-column prop="decrease" label="下调"> </el-table-column>
      <el-table-column label="使用次数">
        <el-table-column prop="productNum" label="产品"> </el-table-column>
        <el-table-column prop="serviceNum" label="服务"> </el-table-column>
      </el-table-column>
      <el-table-column prop="status" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="dobatchAdjustPrice(scope.row.id)"
            >确定
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="overflow: hidden">
      <el-pagination
        style="margin-top: 10px; float: right"
        v-if="adjustPricePageObj.total>0"
        background
        :current-page="adjustPricePageObj.page"
        @current-change="adjustPriceCurrentChange"
        :page-size="adjustPricePageObj.limit"
        layout="total, prev, pager, next"
        :total="adjustPricePageObj.total"
      >
      </el-pagination>
    </div>
  </el-dialog>
  <el-dialog
    title="在售店铺"
    :visible.sync="showAddStore"
    append-to-body
    width="900px"
  >
    <el-table :data="showAddStoreArr" style="width: 100%">
      <el-table-column label="门店">
        <template slot-scope="scope">
          <el-row type="flex" align="middle">
            <div style="flex-shrink: 0">
              <el-image
                style="width: 50px; height: 50px"
                :src="scope.row.logoImg"
                fit="contain"
              >
                <div
                  slot="error"
                  class="image-slot"
                  style="
                    width: 50px;
                    height: 50px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                >
                  <i
                    class="el-icon-picture-outline"
                    style="font-size: 20px"
                  ></i>
                </div>
              </el-image>
            </div>
            <div
              class="el-row--flex"
              style="
                padding-left: 10px;
                flex-direction: column;
                line-height: 14px;
              "
            >
              <p v-text="scope.row.storetag"></p>
              <p v-text="scope.row.alias"></p>
            </div>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址"> </el-table-column>
      <el-table-column label="负责人" prop="dutyman_name"> </el-table-column>
      <el-table-column label="负责人联系方式" prop="dutyman_phone">
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="showAddStore = false"
        >确 定</el-button
      >
    </div>
  </el-dialog>
  <el-dialog
    title="关联服务耗材"
    top="0"
    :visible.sync="showRelevanceConsumables"
    class="consumable-dialog"
    center
    lock-scroll
    append-to-body
    ref="ConsumablesData"
    id="consumablesBody"
    width="calc(100% - 200px)"
  >
    <div slot="title" style="text-align: left" class="el-dialog__title">
      关联服务耗材
      <el-alert
        style="margin-top: 10px"
        show-icon
        type="warning"
        :closable="false"
      >
        <template slot="title">
          有规格服务的耗材分为公共耗材（所有规格都需要的耗材）和规格耗材（各规格独特耗材），其中规格耗材需针对规格单独设置
        </template>
      </el-alert>
    </div>

    <div style="margin-top: 10px">
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <span style="margin-right: 20px">所需服务耗材</span>
          <el-button
            v-if="consumableServiceInfo.type==1"
            type="primary"
            plain
            size="mini"
            @click="toCheckProduct(2)"
          >
            添加服务耗材
          </el-button>
        </div>
        <el-table
          :span-method="consumablesDataSpanMethod"
          :data="consumablesData"
          style="width: 100%"
        >
          <el-table-column
            v-if="consumablesSkuData.length>0"
            align="left"
            prop="sku"
            label="服务规格"
          >
            <template slot-scope="scope">
              <span
                v-text="scope.row.skuInfo.sku"
                v-if="scope.row.skuInfo"
              ></span>
              <span v-text="'公共耗材'" v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="耗材名称" width="250">
            <template slot-scope="scope">
              <el-row type="flex" align="middle">
                <div style="flex-shrink: 0">
                  <template v-if="scope.row.productinfo.imgarr">
                    <template>
                      <el-image
                        :preview-src-list="scope.row.productinfo.imgarr"
                        style="width: 50px; height: 50px"
                        :src="scope.row.productinfo.imgarr[0]"
                        fit="contain"
                      >
                        <div
                          slot="error"
                          class="image-slot"
                          style="
                            width: 50px;
                            height: 50px;
                            font-size: 20px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                          "
                        >
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </template>
                  </template>
                </div>
                <div
                  class="el-row--flex"
                  style="
                    padding-left: 10px;
                    flex-direction: column;
                    line-height: 14px;
                  "
                >
                  <div style="white-space: nowrap; overflow: hidden">
                    <p v-text="scope.row.productinfo.product_name"></p>
                    <p
                      v-text="scope.row.productinfo.product_barcode"
                      class="text-gray"
                    ></p>
                  </div>
                </div>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="cost_price" label="成本">
            <template slot-scope="scope">
              <span
                v-text="'￥'+toMoney(scope.row.productinfo.cost_price)"
              ></span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="unit" label="基本单位">
            <template slot-scope="scope">
              <span v-text="scope.row.productinfo.unit"></span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="small_content" label="含量">
            <template slot="header" slot-scope="scope">
              <span>含量 </span>
              <el-tooltip class="item" effect="light" placement="bottom">
                <div slot="content">
                  <p>1个基本单位中包含的耗用单位数量，仅支持整数</p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span v-text="scope.row.productinfo.small_content"></span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="num" label="数量">
            <template slot="header" slot-scope="scope">
              <span>耗用数量 </span>
              <el-tooltip class="item" effect="light" placement="bottom">
                <div slot="content" style="width: 300px">
                  <p style="line-height: 20px">1.这里的数量对应耗用单位；</p>
                  <p style="line-height: 20px">
                    2.例：耗材A基本单位为瓶，含量500，耗用单位为ml；<br />服务用量为50ml时，请填写50；<br />服务用量为500时，请填写500(不要填写1)；
                  </p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <template v-if="consumableServiceInfo.type==1">
                <div
                  :class="consumablesInputOK(scope.row.num)?'':'el-form-item is-error'"
                  style="margin: 0"
                >
                  <el-input
                    v-model="scope.row.num"
                    size="mini"
                    placeholder="请输入数量"
                  ></el-input>
                </div>
              </template>
              <template v-else>
                <span v-text="scope.row.num"></span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="small_unit" label="消耗单位">
            <template slot="header" slot-scope="scope">
              <span>耗用单位 </span>
              <el-tooltip class="item" effect="light" placement="bottom">
                <div slot="content">
                  <p>耗材在消耗时使用的单位（仅用于耗材消耗）</p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span
                v-if="scope.row.productinfo.small_unit"
                v-text="scope.row.productinfo.small_unit"
              ></span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column align="right" width="250" label="操作">
            <template slot-scope="scope">
              <template v-if="consumableServiceInfo.type==1">
                <el-button
                  size="mini"
                  type="text"
                  @click="toDelConsumables(scope.$index)"
                >
                  &nbsp;&nbsp;&nbsp;&nbsp;删除&nbsp;&nbsp;&nbsp;&nbsp;
                </el-button>
              </template>
              <template v-else> -- </template>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-backtop target="#consumablesBody .el-dialog__body"></el-backtop>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="showRelevanceConsumables=false"
        >取 消</el-button
      >
      <el-button type="primary" size="small" @click="saveConsumablesData"
        >确 定</el-button
      >
    </div>
  </el-dialog>
  <el-dialog
    title="选择耗材"
    :before-close="cancelCheck"
    :visible.sync="checkProduct"
    width="950px"
    append-to-body
    ref="checkProduct"
  >
    <el-row type="flex" justify="end">
      <el-select
        v-if="consumablesSkuData.length>0"
        v-model="consumableServiceInfo.sku_id"
        size="mini"
        placeholder="公共耗材"
        style="margin-left: 20px; width: 120px"
      >
        <el-option :key="0" label="公共耗材" :value="0"> </el-option>
        <el-option
          v-for="item in consumablesSkuData"
          :key="item.id"
          :label="item.sku"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        v-model="keyword"
        size="mini"
        placeholder="产品名称"
        style="margin-left: 20px; max-width: 220px"
      >
      </el-input>
      <el-select
        v-model="classKeyword"
        size="mini"
        placeholder="全部分类"
        style="margin-left: 20px; width: 120px"
      >
        <el-option :key="0" label="全部分类" :value="0"> </el-option>
        <el-option
          v-for="item in classifiData"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-button
        style="margin-left: 20px"
        type="primary"
        size="mini"
        plain
        @click="getProductList"
        >搜索
      </el-button>
    </el-row>
    <el-row>
      <div style="width: 100%; border: 1px solid #ebeef5; margin-top: 10px">
        <el-checkbox-group v-model="selectionData">
          <el-table
            max-height="500"
            ref="multipleTable"
            :data="productData"
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column width="55">
              <!-- <template slot="header" slot-scope="scope">
                <p
                  class="el-checkbox"
                  :class="allcheck?'is-checked':''"
                  @click="checkedAll"
                >
                  <span
                    aria-checked="mixed"
                    class="el-checkbox__input"
                    :class="allcheck==1?'is-checked':(allcheck==2?'is-indeterminate':'')"
                  >
                    <span class="el-checkbox__inner"></span>
                  </span>
                </p>
              </template> -->
              <template slot-scope="scope">
                <template v-if="hasAddToData(scope.row)">
                  <el-checkbox :label="scope.row" :indeterminate="true" disabled
                    ><span></span>
                  </el-checkbox>
                </template>
                <template v-else>
                  <el-checkbox :label="scope.row"><span></span></el-checkbox>
                </template>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="产品">
              <template slot-scope="scope">
                <el-row type="flex" justify="start" align="middle">
                  <div style="flex-shrink: 0">
                    <el-image
                      :preview-src-list="scope.row.imgarr"
                      style="width: 60px; height: 60px"
                      fit="contain"
                      :src="scope.row.imgarr[0]?scope.row.imgarr[0]:''"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                  <div style="padding: 5px 10px">
                    <el-row
                      type="flex"
                      justify="space-around"
                      align="top"
                      style="flex-direction: column"
                    >
                      <span
                        class="ellipsis"
                        :title="scope.row.product_name"
                        v-text="scope.row.product_name"
                      ></span>
                      <span
                        class="ellipsis"
                        :title="scope.row.product_barcode"
                        v-text="scope.row.product_barcode"
                        style="color: #999"
                      ></span>
                      <span
                        class="ellipsis"
                        :title="scope.row.sku"
                        v-if="scope.row.sku"
                        v-text="'('+scope.row.sku+')'"
                        style="color: #999"
                      ></span>
                    </el-row>
                  </div>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="unit" label="单位">
            </el-table-column>
            <el-table-column align="center" prop="s_class" label="分类">
            </el-table-column>
            <el-table-column align="center" prop="cost_price" label="成本价">
              <template slot-scope="scope">
                <span v-text="toMoney(scope.row.cost_price)"></span>
              </template>
            </el-table-column>
          </el-table>
        </el-checkbox-group>
      </div>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 18px; float: right"
          background
          @size-change="productSizeChange"
          @current-change="productCurrentChange"
          :current-page="checkpage"
          :page-sizes="[7,14,21,28,35,70]"
          :page-size="checklimit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="checktotal"
        >
        </el-pagination>
      </div>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancelCheck()" size="small">取 消</el-button>
      <el-button
        type="primary"
        @click="confrimCheckContinue()"
        :loading="continueLoading"
        size="small"
        >继续选择
      </el-button>
      <el-button type="primary" @click="confrimCheck()" size="small"
        >确定</el-button
      >
    </div>
  </el-dialog>
  <el-dialog
    title="关联{$storeCraftsmanName}"
    :visible.sync="associateStaffDialog"
    width="950px"
    append-to-body
    ref="checkProduct"
  >
    <div
      style="max-height: 500px; overflow-y: auto"
      id="staffItem"
      v-loading="associateStaffDialogLoading"
    >
      <div v-if="associateServiceInfo.isSku==1">
        <span>选择规格</span>
        <el-select
          size="mini"
          v-model="associateSkuId"
          placeholder="请选择规格"
        >
          <el-option
            v-for="item in associateSkuData"
            :key="item.id"
            :label="item.sku"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <el-checkbox-group v-model="staffCheckList">
        <div
          style="
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-items: center;
          "
        >
          <template v-for="item in storeStaffData">
            <div style="min-width: 150px">
              <el-tooltip
                v-if="item.nickname.length>7"
                class="item"
                effect="light"
                :content="item.nickname"
                placement="top"
              >
                <el-checkbox
                  :label="item.id"
                  border
                  style="margin: 4px"
                  size="mini"
                >
                  <span class="staffName">{{ item.nickname }}</span>
                </el-checkbox>
              </el-tooltip>
              <el-checkbox
                v-else
                :label="item.id"
                border
                style="margin: 4px"
                size="mini"
              >
                <span class="staffName">{{ item.nickname }}</span>
              </el-checkbox>
            </div>
          </template>
        </div>
      </el-checkbox-group>
      <div
        class="dialog-footer"
        style="display: flex; flex-direction: row; justify-content: flex-end"
      >
        <el-button @click="associateStaffDialog = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="confrimAssociateStaff()" size="small"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
  <el-dialog
    title="会员价"
    :visible.sync="memberGoodsInfo.memberPriceDialog"
    append-to-body
    width="950px"
  >
    <div>
      <el-table
        :data="memberGoodsInfo.MemberPriceData"
        max-height="550"
        :span-method="objectSpanMethod"
        style="width: 100%"
      >
        <el-table-column prop="goodsName" label="名称" width="200">
          <template slot-scope="scope">
            <div v-text="scope.row.goodsName"></div>
            <el-tag size="small" v-if="scope.row.skuName"
              >{{scope.row.skuName}}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column prop="levelName" label="会员等级" width="100">
          <template slot-scope="scope">
            <div v-html="scope.row.levelName"></div>
          </template>
        </el-table-column>
        <el-table-column prop="originalPrice" label="原价" width="200">
        </el-table-column>
        <el-table-column prop="price" label="会员价" min-width="300">
          <template slot-scope="scope">
            <el-row type="flex" v-if="scope.row.set">
              <el-input
                size="small"
                placeholder="请输入会员价"
                :readonly="scope.row.loading"
                v-model="scope.row.price"
                @keyup.enter.native="submitMemberPrice(scope.row)"
              ></el-input>
              <el-button
                size="small"
                style="margin-left: 10px"
                type="primary"
                :loading="scope.row.loading"
                @click="submitMemberPrice(scope.row)"
                >保存</el-button
              >
            </el-row>
            <template v-else>
              <el-input
                size="small"
                placeholder="会员价"
                :readonly="true"
                v-model="scope.row.price"
              ></el-input>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        size="small"
        type="primary"
        @click="memberGoodsInfo.memberPriceDialog = false"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</div>
<script>
  (function () {
    new Vue({
      el: "#serviceIndex",
      data() {
        var money = (rule, value, callback) => {
          if (value === "") {
            callback();
          } else if (isNaN(Number(value))) {
            callback(new Error("必须是数字"));
          } else {
            if (
              /^(([1-9]\d*)(\.\d{1,2})?)$|^(0\.0([1-9]))$|^(0\.([1-9])\d?)$/.test(
                value
              ) ||
              value == 0
            ) {
              callback();
            } else {
              callback(new Error("数值最多保留2位小数"));
            }
          }
        };
        return {
          serviceTableColumnStore: [
            {checked: 1, name: "服务", disabled: true},
            {checked: 1, name: "时长"},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "网店"},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "操作", disabled: true},
          ],
          serviceTableColumnSrevice: [
            {checked: 1, name: "服务", disabled: true},
            {checked: 1, name: "时长"},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "在售门店"},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "操作", disabled: true},
          ],
          storeServiceTableColumnStore: [
            {checked: 1, name: "服务", disabled: true},
            {checked: 1, name: "时长"},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "网店"},
            {checked: 1, name: "销量"},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "操作", disabled: true},
          ],
          storeServiceTableColumnSrevice: [
            {checked: 1, name: "服务", disabled: true},
            {checked: 1, name: "时长"},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "网店"},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "操作", disabled: true},
          ],
          // 调价管理
          adjustPriceDialog: false,
          adjustPriceData: {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          },
          adjustPriceRules: {
            increase: [
              {required: true, message: "上调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            decrease: [
              {required: true, message: "下调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            name: [
              {required: true, message: "方案名称不能为空", trigger: "blur"},
              {max: 20, message: "长度在20个字符以内", trigger: "blur"},
            ],
          },
          adjustPriceSet: true,
          adjustPriceEdit: false,
          adjustPriceTableData: [],
          adjustPricePageObj: {
            page: 1,
            limit: 10,
            total: 0,
          },
          addnow: false,
          // 调价管理
          user: {$user},
          loading2: true,
          ShowTh: 0,
          activeName: "",
          ZStatus: "",
          tableData: [],
          promotionloading: false,
          page: 1,
          limit: 10,
          total: 0, //服务页面的数据

          ifiData: [], //分类信息
          ifipage: 1,
          ifilimit: 10,
          ifitotal: 0, //分类数据
          ifi: {
            name: "",
          },
          shw: false,
          clo: false,

          labelData: [],
          labpage: 1,
          lablimit: 10,
          labtotal: 0, //分类数据
          lab: {
            name: "",
          },
          labshw: false,
          labclo: false,
          classification: "",
          classificationlist: {$classificationlist},
          name: "",
          dialogTableVisible: false,
          dialogTablelabel: false,
          formLabelWidth: "120px",
          storeid: "",
          storeList: [],
          status: "",
          Storeclassification: "",
          stotableData: [],
          stopage: 1,
          stolimit: 10,
          stototal: 0,
          serviceids: [],
          showAddStore: false,
          showAddStoreArr: [],
          // 关联耗材
          showRelevanceConsumables: false,
          // 选择产品
          checkProduct: false, // 服务选择弹框控制
          checkClassification: "", // 当前分类
          label: "", // 当前标签
          classifiData: [], // 分类数据
          checkpage: 1, // 页码
          checklimit: 7, // 页数量
          checktotal: 0, // 总数
          productData: [], // 当前耗材选择数据
          consumablesData: [], // 当前已添加的耗材
          consumablesSkuData: [], // 规格数据
          selectionData: [], // 当前选中的数据
          rows: [], //  当前页上一次选中的数据
          keyword: "",
          hasgetProductList: false, // 判断是否加载过产品数据
          isSkuProduct: 0, // 是否是规格产品
          continueLoading: false,
          classKeyword: 0,
          allcheck: 0,
          consumableServiceInfo: {
            id: 0,
            type: 1, // 1,编辑，2，仅查看
            sku_id: 0,
          },
          getConsumablesDataLoading: false,
          allConsumablesInputOk: true,
          // 关联
          associateStaffDialog: false,
          associateStaffDialogLoading: false,
          allStaff: [],
          storeStaffData: [],
          chooseStaffDialog: false,
          staffCheckList: [],
          associateSkuData: [],
          associateServiceInfo: {
            serviceId: 0,
            storeId: 0,
            isSku: 2,
            sku_id: 0,
          },
          associateSkuId: 0,
          serviceCode: "", //服务推广码
          HQSelectionData: [],
          STSelectionData: [],
          isMerchant: "{$isMerchant??0}",
          adjustPriceSetDialog: false,
          /*设置会员价*/
          memberGoodsInfo: {
            id: "0",
            goodsName: "",
            isSku: "", // 1,规格商品，2，非规格商品
            type: 1, // 商品类型 1 服务 2产品 3卡项
            memberLevelData: [],
            memberPriceData: [],
            memberPriceDialog: false,
          },
          /*设置会员价结束*/
        };
      },
      methods: {
        /*设置排序*/
        saveSort(row) {
          console.log(row);
          if (row.sort != parseInt(row.sort)) {
            return this.$message.error("排序数值有误");
          }
          $.ajax({
            url: "{:url('setSort')}",
            data: {
              sort: parseInt(row.sort),
              id: row.id,
            },
            type: "post",
            success: (res) => {
              if (res.code == 1) {
                return this.$message.success(res.msg);
              }
              return this.$message.error(res.msg);
            },
          });
        },
        /*设置会员价*/
        setMemberPrice(row, type) {
          console.log(row);
          if (type == 1) {
            this.memberGoodsInfo = {
              id: row.id,
              goodsName: row.service_name,
              isSku: row.is_sku, // 1,规格商品，2，非规格商品
              type: 1, // 商品类型 1 服务 2产品 3卡项
              memberLevelData: [],
              memberPriceData: [],
              skuData: [],
              memberPriceDialog: false,
              skuid: 0,
              originalPrice: row.price,
              set: this.user.storeid == 0,
            };
          } else if (type == 3) {
            this.memberGoodsInfo = {
              id: row.service_id,
              goodsName: row.service_name,
              isSku: row.is_sku, // 1,规格商品，2，非规格商品
              type: 1, // 商品类型 1 服务 2产品 3卡项
              memberLevelData: [],
              memberPriceData: [],
              skuData: [],
              memberPriceDialog: false,
              skuid: 0,
              originalPrice: row.price,
              set: false,
            };
          }
          this.getMemberPriceData();
        },
        getMemberPriceData() {
          $.ajax({
            url: "{:url('Memberprice/getMemberPriceData')}",
            data: this.memberGoodsInfo,
            type: "POST",
            success: (res) => {
              console.log(res);
              let LevelData = res.data.levelData;
              let priceData = res.data.data;
              let skuData = res.data.skuData;
              let MemberPriceData = [];
              let goods_id = this.memberGoodsInfo["id"];
              let goodsName = this.memberGoodsInfo["goodsName"];
              let type = this.memberGoodsInfo["type"];
              if (this.memberGoodsInfo.isSku == 1) {
                if (res.data.skuData.length > 0) {
                  this.memberGoodsInfo.skuData = skuData;
                  this.memberGoodsInfo.skuid = res.data.skuData[0]["id"];
                } else {
                  return this.$message.error(
                    "当前商品设置为规格商品，但没有具体规格，请确认"
                  );
                }
                skuData.forEach((sku) => {
                  let sku_id = sku["id"];
                  let skuName = sku["skuName"];
                  let price = sku["price"];
                  let originalPrice = sku["originalPrice"];
                  LevelData.forEach((level) => {
                    let item = {
                      goods_id: goods_id,
                      sku_id: sku_id,
                      member_level_id: level["id"],
                      levelName: level["name"],
                      goodsName: goodsName,
                      skuName: skuName,
                      id: 0,
                      type: type,
                      price: "",
                      originalPrice: originalPrice,
                      loading: false,
                      set: this.memberGoodsInfo.set,
                    };
                    priceData.some((mgp) => {
                      if (
                        mgp["goods_id"] == item["goods_id"] &&
                        mgp["sku_id"] == item["sku_id"] &&
                        mgp["member_level_id"] == item["member_level_id"]
                      ) {
                        item["id"] = mgp["id"];
                        item["price"] = mgp["price"];
                        return true;
                      }
                      return false;
                    });
                    MemberPriceData.push(item);
                  });
                });
              } else {
                this.memberGoodsInfo.skuData = [];
                let sku_id = 0;
                let skuName = "";
                let price = "";
                LevelData.forEach((level) => {
                  let item = {
                    goods_id: goods_id,
                    sku_id: sku_id,
                    member_level_id: level["id"],
                    levelName: level["name"],
                    goodsName: goodsName,
                    skuName: skuName,
                    id: 0,
                    type: type,
                    price: price,
                    originalPrice: this.memberGoodsInfo.originalPrice,
                    loading: false,
                    set: this.memberGoodsInfo.set,
                  };
                  priceData.some((mgp) => {
                    if (
                      mgp["goods_id"] == item["goods_id"] &&
                      mgp["sku_id"] == item["sku_id"] &&
                      mgp["member_level_id"] == item["member_level_id"]
                    ) {
                      item["id"] = mgp["id"];
                      item["price"] = mgp["price"];
                      return true;
                    }
                    return false;
                  });
                  MemberPriceData.push(item);
                });
              }
              console.log(MemberPriceData);
              this.memberGoodsInfo.MemberPriceData = MemberPriceData;
              this.memberGoodsInfo.memberLevelData = LevelData;
              this.memberGoodsInfo.skuData = skuData;
              this.memberGoodsInfo.memberPriceDialog = true;
            },
          });
        },
        submitMemberPrice(row) {
          console.log(row);
          let value = row.price;
          if (
            /^(([1-9]\d*)(\.\d{1,2})?)$|^(0\.0([1-9]))$|^(0\.([1-9])\d?)$/.test(
              value
            )
          ) {
          } else {
            return this.$message.error("会员价格式有误，请确认");
          }
          row.loading = true;
          $.ajax({
            url: "{:url('Memberprice/setMemberPriceData')}",
            data: row,
            type: "POST",
            success: (res) => {
              row.loading = false;
              if (res.code == 1) {
                row.id = res.data.id;
                this.$message.success(res.msg);
              } else {
                this.$message.error(res.msg);
              }
            },
          });
        },
        objectSpanMethod({row, column, rowIndex, columnIndex}) {
          if (columnIndex === 0) {
            let num = this.memberGoodsInfo.memberLevelData.length;
            if (rowIndex % num === 0) {
              return {
                rowspan: num,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        },
        /*设置会员价结束*/
        dobatchAdjustPrice(val) {
          var vm = this;
          const data = vm.HQSelectionData.map(function (item) {
            return item["id"];
          });
          this.$confirm("确定使用该调价范围吗", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              vm.adjustPriceSetDialog = false;
              vm.productlistloading = true;
              $.ajax({
                url: "{:url('batchAdjustPrice')}",
                data: {
                  val: val,
                  data: data.join(","),
                },
                type: "POST",
                success(res) {
                  vm.productlistloading = false;
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消批量操作",
              });
            });
        },
        STbatch(num) {
          if (this.STSelectionData.length == 0) {
            return this.$message({
              type: "warning",
              message: "请先选择",
            });
          } else {
            var str = "确定将选中服务上架(其中总部下架的服务无法操作上架)";
            if (num == 2) {
              str = "确定将选中服务下架";
            } else if (num == 3) {
              str = "确定将选中服务删除，删除后无法恢复";
            }
            const data = this.STSelectionData.map(function (item) {
              return item["id"];
            });
            var vm = this;
            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                vm.loading2 = true;
                $.ajax({
                  url: "{:url('STbatchSet')}",
                  data: {
                    type: num,
                    data: data.join(","),
                  },
                  type: "POST",
                  success(res) {
                    // console.log(res);
                    vm.loading2 = false;
                    if (res.code == 1) {
                      vm.$message({
                        type: "success",
                        message: res.msg,
                      });
                      vm.getstotableData();
                    } else {
                      vm.$message({
                        type: "error",
                        message: res.msg,
                      });
                    }
                  },
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消批量操作",
                });
              });
          }
        },
        STSelection(val) {
          this.STSelectionData = val;
        },
        HQbatch(num) {
          if (num === 4) {
            if (this.HQSelectionData.length == 0) {
              return this.$message({
                type: "warning",
                message: "请先选择",
              });
            } else {
              this.adjustPriceSetDialog = true;
            }
            return;
          }
          if (this.HQSelectionData.length == 0) {
            return this.$message({
              type: "warning",
              message: "请先选择",
            });
          } else {
            var str = "确定将选中服务上架";
            if (num == 2) {
              str = "确定将选中服务下架(店铺服务同步下架)";
            } else if (num == 3) {
              str = "确定将选中服务删除，删除后无法恢复";
            }
            const data = this.HQSelectionData.map(function (item) {
              return item["id"];
            });
            var vm = this;
            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                vm.loading2 = true;
                $.ajax({
                  url: "{:url('HQbatchSet')}",
                  data: {
                    type: num,
                    data: data.join(","),
                  },
                  type: "POST",
                  success(res) {
                    console.log(res);
                    vm.loading2 = false;
                    if (res.code == 1) {
                      vm.$message({
                        type: "success",
                        message: res.msg,
                      });
                      vm.GetServiceList();
                    } else {
                      vm.$message({
                        type: "error",
                        message: res.msg,
                      });
                    }
                  },
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消批量操作",
                });
              });
          }
        },
        // 总部批量操作
        HQSelection(val) {
          console.log(val);
          this.HQSelectionData = val;
        },
        // 关联
        associateStaff(row) {
          // console.log(row);
          this.getStoreStaff(row.store_id);
          // 非规格
          this.associateServiceInfo = {
            serviceId: row.service_id,
            storeId: row.store_id,
            isSku: row.is_sku,
            sku_id: 0,
          };
          this.associateSkuId = 0;
          this.associateSkuData = [];
          if (row.is_sku == 2) {
            this.getAssociateStaffData();
          } else {
            this.getSkuItemArr();
          }
        },
        getSkuItemArr() {
          const vm = this;
          const row = this.associateServiceInfo;
          $.ajax({
            url: "{:url('staff/getSkuItemArr')}",
            data: {
              serviceId: row.serviceId,
              storeId: row.storeId,
              isSku: row.isSku,
            },
            type: "POST",
            success(res) {
              // console.log(res);
              if (res.code == 1) {
                vm.associateSkuData = res.data;
                vm.associateStaffDialog = true;
                vm.associateSkuId = res.data[0]["id"];
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
            },
          });
        },
        getAssociateStaffData() {
          var vm = this;
          const row = this.associateServiceInfo;
          $.ajax({
            url: "{:url('staff/associateStaff')}",
            data: {
              serviceId: row.serviceId,
              storeId: row.storeId,
              isSku: row.isSku,
              sku_id: row.sku_id,
            },
            type: "POST",
            success(res) {
              console.log(res);
              if (res.code == 1) {
                var staffCheckList = [];
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i]["checked"] == 1) {
                    staffCheckList.push(res.data[i]["staff_id"]);
                  }
                }
                vm.staffCheckList = staffCheckList;
                vm.associateStaffDialog = true;
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
            },
          });
        },
        getStoreStaff(storeid) {
          var vm = this;
          for (var i = 0; i < vm.allStaff.length; i++) {
            if ((vm.allStaff[i]["storeid"] = storeid)) {
              vm.storeStaffData = vm.allStaff[i]["data"];
              return;
            }
          }
          $.ajax({
            url: "{:url('order/getStaff')}",
            data: {
              storeid: storeid,
              isTech: 2,
            },
            type: "POST",
            success(res) {
              // console.log(res,111);
              if (res.code == 1) {
                vm.allStaff.push({
                  storeid: storeid,
                  data: res.data,
                });
                vm.storeStaffData = res.data;
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
            },
          });
        },
        confrimAssociateStaff() {
          var data = this.associateServiceInfo;
          data["staffCheckList"] = this.staffCheckList;
          var vm = this;
          vm.associateStaffDialogLoading = true;
          $.ajax({
            url: "{:url('staff/confrimAssociateStaff')}",
            data: data,
            type: "POST",
            success(res) {
              var type = res.code == 1 ? "success" : "error";
              vm.$message({
                type: type,
                message: res.msg,
              });
              vm.associateStaffDialogLoading = false;
            },
          });
        },
        // 关联服务
        toShowRelevanceConsumables(item, num) {
          if (num == 1) {
            this.consumableServiceInfo = {
              id: item.id,
              type: 1, // 1,编辑，2，仅查看  consumables
              sku_id: 0,
            };
          } else if (num == 2) {
            this.consumableServiceInfo = {
              id: item.service_id,
              type: num, // 1,编辑，2，仅查看s
              sku_id: 0,
            };
          } else if (num == 3) {
            this.consumableServiceInfo = {
              id: item.id,
              type: 2, // 1,编辑，2，仅查看s
              sku_id: 0,
            };
          }
          this.showRelevanceConsumables = true;
          this.getConsumablesData();
        },
        consumablesInputOK(num) {
          if (num * 1 === parseInt(num)) {
            return true;
          }
          this.allConsumablesInputOk = false;
          return false;
        },
        toDelConsumables(index) {
          this.consumablesData.splice(index, 1);
        },
        // 选择产品
        hasAddToData(item) {
          var hasAdd = false;
          var sku_id = this.consumableServiceInfo.sku_id;
          try {
            this.consumablesData.forEach((row) => {
              if (
                item["id"] == row["consumables_id"] &&
                row["sku_id"] == sku_id
              ) {
                hasAdd = true;
                throw Error();
              }
            });
          } catch (e) {}
          return hasAdd;
        },
        /*----------选择产品--------*/
        // 获取产品分类数据
        toMoney(num) {
          if (isNaN(Number(num))) return "";
          return (num / 100).toFixed(2);
        },
        getClassifiData() {
          var vm = this;
          $.ajax({
            url: "{:url('Product/getLabelAndClassData')}",
            data: {},
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.classifiData = res.data["classData"];
              }
            },
          });
        },
        toCheckProduct(num) {
          var vm = this;
          this.isSkuProduct = num;
          this.checkProduct = true;
          this.classKeyword = 0;
          this.keyword = "";
          this.allcheck = 0;
          this.getProductList();
        },
        //获取产品信息
        getProductList: function () {
          var vm = this;
          var obj = {
            target: vm.$refs.checkProduct.$refs.dialog,
            background: "rgba(255,255,255,0.5)",
            duration: 0,
          };
          top.layui.vueCommon.showLoading(obj);
          $.ajax({
            url: "{:url('Product/getProductList')}",
            data: {
              cardType: 0,
              page: vm.checkpage,
              limit: vm.checklimit,
              storeid: 0,
              keyword: vm.keyword,
              classKeyword: vm.classKeyword,
              labelKeyword: -1,
              statusKeyword: 0,
              isConsumables: 1,
              consumableType: 1,
              serviceBind: 1, // 服务绑定耗材
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.checktotal = res.count;
                vm.productData = [];
                vm.productData = res.data;
              }
              top.layui.vueCommon.hideLoading();
            },
          });
        },
        productSizeChange(val) {
          var that = this;
          that.checklimit = val;
          that.checkpage = 1;
          that.getProductList();
        },
        /* 切换页数 */
        productCurrentChange(val) {
          var that = this;
          that.checkpage = val;
          that.getProductList();
        },
        //确定选择
        confrimCheck() {
          var arr = JSON.parse(JSON.stringify(this.selectionData), true);
          var brr = JSON.parse(JSON.stringify(this.consumablesData), true);
          var skuInfo = "";
          if (this.consumableServiceInfo.sku_id && this.consumablesSkuData) {
            for (var j = 0; j < this.consumablesSkuData.length; j++) {
              if (
                this.consumableServiceInfo.sku_id ==
                this.consumablesSkuData[j]["id"]
              ) {
                skuInfo = this.consumablesSkuData[j];
                break;
              }
            }
          }
          // 将取消的数据从选中数据中删除
          var length = arr.length;
          for (var i = 0; i < length; i++) {
            var item = {};
            item["productinfo"] = JSON.parse(JSON.stringify(arr[i]));
            item["skuInfo"] = JSON.parse(JSON.stringify(skuInfo));
            item["consumables_id"] = item["productinfo"]["id"];
            item["service_id"] = this.consumableServiceInfo.id;
            item["sku_id"] = this.consumableServiceInfo.sku_id;
            item["id"] = 0;
            item["num"] = "";
            brr.push(item);
          }
          this.consumablesData = [];
          this.consumablesData = brr;
          this.checkProduct = false;
          this.selectionData = [];
        },
        confrimCheckContinue() {
          var arr = JSON.parse(JSON.stringify(this.selectionData), true);
          var brr = JSON.parse(JSON.stringify(this.consumablesData), true);
          var skuInfo = "";
          if (this.consumableServiceInfo.sku_id && this.consumablesSkuData) {
            for (var j = 0; j < this.consumablesSkuData.length; j++) {
              if (
                this.consumableServiceInfo.sku_id ==
                this.consumablesSkuData[j]["id"]
              ) {
                skuInfo = this.consumablesSkuData[j];
                break;
              }
            }
          }
          // 上一次选中 但此次取消的数据
          // 将取消的数据从选中数据中删除
          var length = arr.length;
          for (var i = 0; i < length; i++) {
            var item = {};
            item["productinfo"] = JSON.parse(JSON.stringify(arr[i]));
            item["skuInfo"] = JSON.parse(JSON.stringify(skuInfo));
            item["consumables_id"] = item["productinfo"]["id"];
            item["service_id"] = this.consumableServiceInfo.id;
            item["sku_id"] = this.consumableServiceInfo.sku_id;
            item["id"] = 0;
            item["num"] = "";
            brr.push(item);
          }
          this.consumablesData = [];
          this.consumablesData = brr;
          this.selectionData = [];
        },
        // 取消选择
        cancelCheck() {
          this.$refs.multipleTable.clearSelection();
          this.checkProduct = false;
          this.checkProductType = 0;
          this.selectionData = [];
        },
        /*全选点击*/
        checkedAll() {
          if (this.allcheck == 0 || this.allcheck == 2) {
            this.allcheck = 1;
            var arr = [];
            // 已选数据
            var brr = JSON.parse(JSON.stringify(this.consumablesData), true);
            // 待选数据
            var rows = this.productData;
            var sku_id = this.consumableServiceInfo.sku_id;
            for (let i = 0; i < rows.length; i++) {
              let item = rows[i];
              var isfind = false;
              for (let j = 0; j < brr.length; j++) {
                let items = brr[j];
                // 对比成功 删除掉相同的数据。
                if (
                  item["id"] == items["consumables_id"] &&
                  items["sku_id"] == sku_id
                ) {
                  brr.splice(j, 1);
                  isfind = true;
                  break;
                }
              }
              if (!isfind) {
                arr.push(item);
              }
            }
            this.selectionData = arr;
          } else {
            this.allcheck = 0;
            this.selectionData = [];
          }
        },
        /*获取已设置数据*/
        getConsumablesData() {
          var vm = this;
          //ConsumablesData
          var obj = {
            target: vm.$refs.ConsumablesData.$refs.dialog,
            background: "rgba(255,255,255,0.5)",
            duration: 0,
          };
          top.layui.vueCommon.showLoading(obj);
          $.ajax({
            url: "{:url('Consumables/getConsumablesData')}",
            data: {
              id: vm.consumableServiceInfo.id,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.consumablesData = res.data["data"];
                vm.consumablesSkuData = res.data["skuData"];
              } else {
              }
              top.layui.vueCommon.hideLoading();
            },
          });
        },
        /*保存设置*/
        saveConsumablesData() {
          var vm = this;
          if (vm.consumableServiceInfo.type == 1) {
            if (!this.allConsumablesInputOk) {
              return this.$message({
                type: "error",
                message: "耗材数量设置不正确",
              });
            }
            //ConsumablesData
            var obj = {
              target: vm.$refs.ConsumablesData.$refs.dialog,
              background: "rgba(255,255,255,0.5)",
              duration: 0,
            };
            var data = this.consumablesData;
            var postData = {
              id: vm.consumableServiceInfo.id,
              data: [],
            };
            for (var i = 0; i < data.length; i++) {
              var item = data[i];
              postData["data"].push({
                service_id: item["service_id"],
                sku_id: item["sku_id"],
                consumables_id: item["consumables_id"],
                num: item["num"],
                id: item["id"],
              });
            }
            top.layui.vueCommon.showLoading(obj);
            $.ajax({
              url: "{:url('Consumables/saveConsumablesData')}",
              data: postData,
              type: "POST",
              success: function (res) {
                //console.log(res);
                if (res.code == 1) {
                  vm.$message({
                    type: "success",
                    message: res.msg,
                  });
                  vm.GetServiceList();
                  vm.showRelevanceConsumables = false;
                } else {
                  vm.$message({
                    type: "error",
                    message: res.msg,
                  });
                }
                top.layui.vueCommon.hideLoading();
              },
            });
          } else {
            vm.showRelevanceConsumables = false;
          }
        },
        /*合并表格*/
        consumablesDataSpanMethod({row, column, rowIndex, columnIndex}) {
          // console.log(row, column, rowIndex);
          if (columnIndex === 0 && this.consumablesSkuData.length > 0) {
            var num = 0;
            if (
              rowIndex > 0 &&
              this.consumablesData[rowIndex - 1]["sku_id"] == row["sku_id"]
            ) {
              num--;
            } else {
              for (var i = rowIndex + 1; i < this.consumablesData.length; i++) {
                if (this.consumablesData[i]["sku_id"] == row["sku_id"]) {
                  num++;
                }
              }
            }
            // console.log(num);
            if (num > 0) {
              return {
                rowspan: num + 1,
                colspan: 1,
              };
            } else if (num < 0) {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        },
        /*----------选择耗材-----------*/
        // 调价管理
        confirmAdjustPrice() {
          var vm = this;
          this.$refs["adjustPriceForm"].validate(function (bol) {
            if (!bol) {
              vm.$message({
                showClose: true,
                message: "请完善您的设置信息，带*号为必填或者必选",
                type: "warning",
                duration: 3000,
              });
            } else {
              $.ajax({
                url: "{:url('Adjustprice/save')}",
                data: vm.adjustPriceData,
                type: "POST",
                success(res) {
                  console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            }
          });
        },
        toAddAdjustPrice() {
          this.adjustPriceData = {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          };
        },
        getAdjustPriceData() {
          var vm = this;
          $.ajax({
            url: "{:url('Adjustprice/getPage')}",
            data: {
              page: vm.adjustPricePageObj.page,
              limit: vm.adjustPricePageObj.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.adjustPriceTableData = res.data;
                vm.adjustPricePageObj.total = res.count;
              } else {
                vm.adjustPriceTableData = [];
              }
            },
          });
        },
        adjustPriceCurrentChange(val) {
          this.adjustPricePageObj.page = val;
          this.getAdjustPriceData();
        },
        delAdjustPrice(id) {
          var vm = this;
          this.$confirm("确定要删除调价方案?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('Adjustprice/del')}",
                data: {id: id},
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        // 调价管理
        showhasAddStore(arr) {
          var storeList = this.storeList;
          var showAddStoreArr = [];
          for (let i = 0; i < storeList.length; i++) {
            if ($.inArray(storeList[i]["id"], arr) != -1) {
              showAddStoreArr.push(storeList[i]);
            }
          }
          this.showAddStore = true;
          this.showAddStoreArr = showAddStoreArr;
        },
        getClassificationList() {
          var that = this;
          $.ajax({
            url: "{:url('getClassificationList')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.classificationlist = res.data;
              }
            },
          });
        },
        imgError(e) {
          e.target.src = "/assets/common/images/img_load_error.png";
        },
        handleSelectionChange(val) {
          var that = this;
          var ids = [];
          for (let i = 0; i < val.length; i++) {
            ids.push(val[i]["id"]);
          }
          that.serviceids = ids;
        },
        handleCommand(command) {
          if (command) {
            var c = command.split("-");
            this[c[0]](c[1], c[2]);
          }
        },
        //顶部切换
        handleClick(tab) {
          // console.log(tab['index']);
          var that = this;
          that.ShowTh = tab["index"];
          if (this.activeName == "srevice") {
            that.GetServiceList();
          } else {
            that.getstotableData();
          }
        },
        //添加页面
        add() {
          var that = this;
          location.hash = "/Service/servicead";
        },
        //打开管理分类页面
        dialog() {
          var that = this;
          that.Getifi();
          that.dialogTableVisible = true;
        },
        //打开标签管理
        labelMa() {
          var that = this;
          that.Getlab();
          that.dialogTablelabel = true;
        },
        IfiQuery() {
          var that = this;
          that.GetServiceList();
        },
        ZStatusSee() {
          var that = this;
          that.GetServiceList();
        },
        StoreQuery() {
          var that = this;
          that.getstotableData();
        },
        StatusQuery() {
          var that = this;
          that.getstotableData();
        },
        StoIfiQuery() {
          var that = this;
          that.getstotableData();
        },
        //总部服务搜索
        See() {
          var that = this;
          that.GetServiceList();
        },
        //启用
        toPutaway(id) {
          var that = this;
          var status = 1;
          $.ajax({
            url: "{:url('EditStatus')}",
            data: {
              status: status,
              id: id,
            },
            type: "post",
            success: function (res) {
              console.log(res);
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
              that.GetServiceList();
            },
          });
        },
        //禁用
        toSoldOut(id) {
          var that = this;
          var status = 2;
          $.ajax({
            url: "{:url('EditStatus')}",
            data: {
              status: status,
              id: id,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
              that.GetServiceList();
            },
          });
        },
        //编辑页面
        Edit(id) {
          var _this = this;
          location.hash = "/Service/servicead/id=" + id;
          // window.open("#/Service/servicead/id=" + id);
        },
        //删除
        Del(id) {
          var that = this;
          that
            .$confirm("是否删除该服务", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('Del')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    that.GetServiceList();
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        //总部服务
        size_change(val) {
          var that = this;
          that.limit = val;
          that.page = 1;
          that.GetServiceList();
        },
        /* 切换页数 */
        current_change(val) {
          var that = this;
          that.page = val;
          that.GetServiceList();
        },

        // 添加分类
        Adifi() {
          var that = this;
          var name = that.ifi.name;
          if ($.trim(name) == "") {
            return that.$message({
              type: "info",
              message: "请输入名称",
            });
          }
          var id = "";
          that.Addifi(name, id);
          that.clo = false;
        },
        //分类编辑
        Shows() {
          var that = this;
          that.shw = true;
        },
        //分类编辑完成
        hid() {
          var that = this;
          that.shw = false;
        },
        //修改分类
        Editifi(name, id) {
          var that = this;
          var name = name;
          var id = id;
          that.Addifi(name, id);
        },
        //删除分类
        Delifi(id) {
          var that = this;
          that
            .$confirm("是否删除该分类", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('Delifi')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                      onClose: function () {},
                    });
                    that.Getifi();
                    that.getClassificationList();
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        //分类管理分页
        ifi_size_change(val) {
          var that = this;
          that.ifilimit = val;
          that.ifipage = 1;
          that.Getifi();
        },
        /* 切换页数 */
        ifi_current_change(val) {
          var that = this;
          that.ifipage = val;
          that.Getifi();
        },

        //添加标签
        Adlab() {
          var that = this;
          var name = that.lab.name;
          if ($.trim(name) == "") {
            return that.$message({
              type: "info",
              message: "请输入名称",
            });
          }
          var id = "";
          that.Addlab(name, id);
          that.labclo = false;
        },
        //编辑标签
        labShows() {
          var that = this;
          that.labshw = true;
        },
        //完成编辑
        labhid() {
          var that = this;
          that.labshw = false;
        },
        //修改分类
        Editlab(name, id) {
          var that = this;
          var name = name;
          var id = id;
          that.Addlab(name, id);
        },
        //删除分类
        Dellab(id) {
          var that = this;
          that
            .$confirm("是否删除该标签", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('Dellab')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                      onClose: function () {
                        that.dialogTablelabel = false;
                      },
                    });
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        //执行添加分类
        Addlab: function (name, id) {
          var that = this;
          var id = id;
          $.ajax({
            url: "{:url('Adlab')}",
            data: {
              name: name,
              id: id,
            },
            type: "post",
            success: function (res) {
              that.ifi.name = "";
              if (res.status == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.Getlab();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //获取分类信息
        Getlab: function () {
          var vm = this;
          vm.promotionloading = true;
          $.ajax({
            url: "{:url('Getlab')}",
            data: {
              page: vm.labpage,
              limit: vm.lablimit,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.labelData = res.data;
                vm.labtotal = res.count;
              }
              vm.promotionloading = false;
            },
          });
        },
        //标签管理分页
        lab_size_change(val) {
          var that = this;
          that.lablimit = val;
          that.labpage = 1;
          that.Getlab();
        },
        /* 切换页数 */
        lab_current_change(val) {
          var that = this;
          that.labpage = val;
          that.Getlab();
        },
        //门店信息
        getStoreList() {
          var that = this;
          $.ajax({
            url: "{:url('GetStoreList')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.storeList = res.data;
              }
              if (that.storeList) {
                that.storeid = that.storeList[0]["id"];
              }
            },
          });
        },
        //门店服务信息
        getstotableData() {
          var that = this;
          that.loading2 = true;
          that.promotionloading = true;
          if (that.user.storeid == 0) {
            var storeid = that.storeid;
          } else {
            var storeid = that.user.storeid;
          }
          var status = that.status ? that.status : "";
          var Storeclassification = that.Storeclassification
            ? that.Storeclassification
            : "";
          $.ajax({
            url: "{:url('GetStoServiceList')}",
            data: {
              page: that.stopage,
              limit: that.stolimit,
              storeid: storeid,
              status: status,
              Storeclassification: Storeclassification,
              name: that.name,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                that.stotableData = res.data;
                that.stototal = res.count;
              }
              that.stopromotionloading = false;
              that.loading2 = false;
            },
          });
        },
        //门店服务 详情
        SeeService(id) {
          var that = this;
          location.hash = "/Service/servicedetails/id=" + id;
        },
        // 总部门店分页
        sto_size_change(val) {
          var that = this;
          that.stolimit = val;
          that.stopage = 1;
          that.getstotableData();
        },
        /* 切换页数 */
        sto_current_change(val) {
          var that = this;
          that.stopage = val;
          that.getstotableData();
        },

        //门店顶部切换
        stohandleClick(tab) {
          // console.log(tab['index']);
          var that = this;
          that.ShowTh = tab["index"];
          if (this.activeName == "srevice") {
            that.GetServiceList();
          } else {
            that.getstotableData();
          }
        },
        //门店修改服务状态上架
        StotoPutaway(id, serviceid) {
          var that = this;
          var status = 1;
          $.ajax({
            url: "{:url('EditStoStatus')}",
            data: {
              status: status,
              id: id,
              serviceid: serviceid,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
              } else {
                that.$message({
                  type: "error",
                  message: res.msg,
                  duration: 3000,
                });
              }
              that.getstotableData();
            },
          });
        },
        //门店修改服务状态下架
        StotoSoldOut(id) {
          var that = this;
          var status = 2;
          $.ajax({
            url: "{:url('EditStoStatus')}",
            data: {
              status: status,
              id: id,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
              that.getstotableData();
            },
          });
        },
        // 门店查看服务详情
        SeeStoService(id) {
          var storeid = this.user.storeid;
          // location.hash = "/Service/servicedetails/id=" + id + "/storeid=" + storeid;
          /* window.open(
            "#/Service/servicedetails/id=" + id + "/storeid=" + storeid
          ); */
          location.hash =
            "/Service/servicedetails/id=" + id + "/storeid=" + storeid;
        },
        //门店服务编辑
        EditStoSer(id, fromStore) {
          // window.open("#/Service/storeserviceed/id=" + id);
          if (fromStore == 1) {
            // 门店创建的服务，不限制修改
            location.hash = "/Service/servicead/id=" + id + "&fromStore=1";
          } else {
            // 总部创建的服务，有限修改
            location.hash = "/Service/storeserviceed/id=" + id;
          }
        },
        //门店服务后台总部服务详情页面
        Servicetotal(id) {
          // window.open("#/Service/servicetotal/id=" + id);
          location.hash = "/Service/servicetotal/id=" + id;
        },
        //添加之本店
        AddstoSer(id) {
          var that = this;
          that.loading2 = true;
          var storeid = that.user.storeid;
          if (id == 0) {
            if (that.serviceids.length == 0) {
              return that.$message({
                type: "error",
                message: "批量添加是请选择服务",
                onClose: function () {
                  that.loading2 = false;
                },
              });
            }
            var serviceid = that.serviceids;
          } else {
            var serviceid = id;
          }
          $.ajax({
            url: "{:url('AddstoSer')}",
            type: "post",
            data: {
              serviceid: serviceid,
              storeid: storeid,
            },
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                  onClose: function () {
                    that.GetServiceList();
                  },
                });
              } else {
                that.$message({
                  type: "error",
                  message: res.msg,
                });
              }
              that.loading2 = false;
            },
          });
        },

        //执行添加分类
        Addifi: function (name, id) {
          var that = this;
          var id = id;
          $.ajax({
            url: "{:url('Adifi')}",
            data: {
              name: name,
              id: id,
            },
            type: "post",
            success: function (res) {
              that.ifi.name = "";
              if (res.status == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.Getifi();
                that.getClassificationList();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //获取分类信息
        Getifi: function () {
          var vm = this;
          vm.promotionloading = true;
          $.ajax({
            url: "{:url('Getifi')}",
            data: {
              page: vm.ifipage,
              limit: vm.ifilimit,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.ifiData = res.data;
                vm.ifitotal = res.count;
              }
              vm.promotionloading = false;
            },
          });
        },
        //获取商家所有服务信息
        GetServiceList: function () {
          var vm = this;
          vm.loading2 = true;
          vm.promotionloading = true;
          var classification = vm.classification ? vm.classification : "";
          var name = vm.name ? vm.name : "";
          var status = vm.ZStatus;
          $.ajax({
            url: "{:url('GetServiceList')}",
            data: {
              page: vm.page,
              limit: vm.limit,
              classification: classification,
              name: name,
              status: status,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.tableData = res.data;
                vm.total = res.count;
              }
              vm.promotionloading = false;
              vm.loading2 = false;
            },
          });
        },
        // 生成二维码
        getCode(id, storeid) {
          var vm = this;
          $.ajax({
            url: "{:url('getServiceCode')}",
            data: {
              id: id,
              storeid: storeid,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.serviceCode = res.data;
              }
            },
          });
        },
      },
      created: function () {
        var that = this;
        that.GetServiceList();
        that.getStoreList();
        if (that.user.storeid == 0) {
          that.activeName = "srevice";
          this.getClassifiData();
        } else {
          that.activeName = "store";
          that.getstotableData();
        }
      },
      watch: {
        // 调价管理
        adjustPriceDialog(n) {
          if (n) {
            this.getAdjustPriceData();
          }
        },
        adjustPriceSetDialog(n) {
          if (n) {
            this.getAdjustPriceData();
          }
        },
        consumablesData: {
          deep: true,
          handler(n) {
            this.allConsumablesInputOk = true;
            var oldJson = JSON.stringify(n);
            var data = JSON.parse(oldJson, true);
            data.sort(function (a, b) {
              return a.sku_id - b.sku_id;
            });
            var newJson = JSON.stringify(data);
            if (oldJson != newJson) {
              this.consumablesData = data;
            }
          },
        },
        consumableServiceInfo: {
          deep: true,
          handler(n) {
            this.selectionData = [];
          },
        },
        associateSkuId(n) {
          if (n) {
            this.associateServiceInfo.sku_id = n;
            this.getAssociateStaffData();
          }
        },
      },
    });
  })();
</script>
<style>
  .el-select {
    max-width: 125px;
  }

  .consumable-dialog {
    width: 100%;
    overflow: hidden;
  }

  .consumable-dialog > div.el-dialog {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    height: 100%;
  }

  .consumable-dialog .el-dialog__footer {
    position: absolute;
    width: 100%;
    bottom: 10px;
  }

  .consumable-dialog .el-dialog__body {
    overflow-y: auto;
    height: calc(100% - 280px);
    padding-bottom: 62px;
  }

  .staffName {
    display: flex;
    flex-direction: row;
    align-items: center;
    white-space: nowrap;
    width: 90px;
    overflow: hidden;
  }

  .el-table--border td,
  .el-table--border th,
  .el-table__body-wrapper
    .el-table--border.is-scrolling-left
    ~ .el-table__fixed {
    border-right: none !important;
  }
</style>
