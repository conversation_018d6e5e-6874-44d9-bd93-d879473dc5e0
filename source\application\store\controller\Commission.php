<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-06-13
 * Time: 15:33
 */

namespace app\store\controller;

use app\store\model\DeductStaffCommission as DeStaComModel;
use app\store\model\Staff as StaffModel;
use think\Cache;
use PHPExcel_IOFactory;
//<!--[meiye_07]-->
class Commission extends Controller
{
    public function index()
    {
        $userInfo = session('userinfo');
        $storeid = $userInfo['storeid'];
        $this->assign('storeid', $storeid);
        if ($storeid == 0) {
            $isStore = 0;
        } else {
            $isStore = 1;
        }
        $this->assign('isStore', $isStore);
        $this->outPut();
    }

    public function getCommissionDetailData()
    {
        try {
            $post = input('post.');
            $startTime = $post['startTime'];
            $endTime = $post['endTime'];
            if ($startTime == 0 || $endTime == 0) {
                switch ($post['type']) {
                    case 0:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400;
                        break;
                    case 1:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00');
                        $startTime = $endTime - 86400;
                        break;
                    case 2:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400 * 7;
                        break;
                    case 3:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = strtotime(date('Y-m') . '-1 00:00:00');
                        break;
                }
            } else {
                $startTime = strtotime($startTime . ' 00:00:00');
                $endTime = strtotime($endTime . ' 23:59:59') + 1;
            }
            $userInfo = session('userinfo');
            $where = array(
                'order_time' => array('between', array($startTime, $endTime)),
                'merchantid' => $userInfo['merchantid'],
                'storeid' => $post['storeId'],
                'status' => 1
            );
            if(isset($post['staffIds']) && $post['staffIds']){
                $where['staff_id'] = array('in',$post['staffIds']);
            }
            if(isset($post['checkedStaffId']) && $post['checkedStaffId']){
                $where['staff_id'] = $post['checkedStaffId'];
            }
            if(isset($post['refundType']) && $post['refundType']){
                $where['refund'] = $post['refundType'];
                $refundType = 2;
            }else{
                $refundType = 1;
            }
            $DeStaComModel = new DeStaComModel;
            $page = $post['page'];
            $limit = $post['limit'];
            $field = array(
                'id',
                'pid',
                'order_id',
                'order_detail_id',
                'deduct_type',
                'goods_type',
                'deduct_way',
                'performance as newPerformance',
                'commission as newCommission',
                'base_amount as newBaseAmount',
                'order_time as newOrderTime',
                'staff_id as staffInfo',
                'check'
            );
            $append = array(
                'goodsInfo'
            );
            $data = $DeStaComModel->getSelectByPage($where, $page, $limit, $field, $append);
            $newData['data'] = $data;
            $count = $DeStaComModel->getCount($where);
            if($page==1 || $refundType==2){
                if(isset($where['refund'])){
                    unset($where['refund']);
                }
                $where['_string'] = "`refund` = 1 OR (`refund` = 2 AND `check` = 1)";
                $where['deduct_type'] = 1;
                $where['goods_type'] = 3;
                $totalsales = $DeStaComModel->sumField($where,'base_amount');

                $sPer = $DeStaComModel->sumField($where,'performance');
                $sCom = $DeStaComModel->sumField($where,'commission');
                $where['goods_type'] = 1;
                $where['goods_type'] = 1;
                $tPer = $DeStaComModel->sumField($where,'performance');
                $tCom = $DeStaComModel->sumField($where,'commission');

                $newData['count'] = array(
                    'sPer' => $sPer,
                    'sCom' => $sCom,
                    'tPer' => $tPer,
                    'tCom' => $tCom,
                    'totalsales'=>$totalsales
                    // 'sql'=>$DeStaComModel->getLastSql()
                );
            }
            return $this->ajaxTable($newData, $count);
        } catch (\Exception $e) {
            $error = $e->getMessage();
            return $this->ajaxTable(array(), 0, $error);
        }
    }
    public function getStaffCommissionDetailData()
    {
        try {
            $post = input('post.');
            $startTime = $post['startTime'];
            $endTime = $post['endTime'];
            if ($startTime == 0 || $endTime == 0) {
                switch ($post['type']) {
                    case 0:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400;
                        break;
                    case 1:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00');
                        $startTime = $endTime - 86400;
                        break;
                    case 2:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400 * 7;
                        break;
                    case 3:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = strtotime(date('Y-m') . '-1 00:00:00');
                        break;
                }
            } else {
                $startTime = strtotime($startTime . ' 00:00:00');
                $endTime = strtotime($endTime . ' 23:59:59') + 1;
            }
            $userInfo = session('userinfo');
            $dataType = $post['dataType'];
            $where = array(
                'order_time' => array('between', array($startTime, $endTime)),
                'merchantid' => $userInfo['merchantid'],
                'storeid' => $post['storeId'],
                'status' => 1,
                'type'=>$dataType
            );
            if(isset($post['staffIds']) && $post['staffIds']){
                $where['staffIds'] = array('in',$post['staffIds']);
            }
            $page = $post['page'];
            $limit = $post['limit'];
            $name = 'StaffCommission_'.md5(json_encode($where));
            unset($where['staffIds']);
            $cache = Cache::connect();
            $newData = $cache->get($name);
            if($newData){
                $staffAllData = $newData['data'];
                $count = $newData['count'];
                $page = $page?$page:1;
                $start = ($page-1)*$limit;
                $data = array_slice($staffAllData,$start,$limit);
            }else{
                $StaffModel = new StaffModel;
                $StaffModel->setExtraData($where);
                $map = array(
                    'merchantid' => $userInfo['merchantid'],
                    'storeid' => $post['storeId']
                );
                if(isset($post['staffIds']) && $post['staffIds']){
                    $map['id'] = array('in',$post['staffIds']);
                }
                $field = array(
                    '*',
                    'avatar as staffAvatar'
                );
                $append = array(
                    'allMoney',                 //总额
                    'serviceTecMoney',          //服务劳动
                    'serviceSaleMoney',         // 服务销售
                    'productSaleMoney',         // 产品销售
                    'cardSaleMoney',            // 卡项销售
                    'rechargeSaleMoney'         // 充值销售金额
                );
                $staffAllData = $StaffModel->getAll($map,$field,$append);
                if(is_object($staffAllData)){
                    $staffAllData = $staffAllData->toArray();
                }
                array_multisort(array_column($staffAllData,'allMoney'),SORT_DESC,$staffAllData);
                $count = $StaffModel->getCount($map);
                $newData = array(
                    'data'=>$staffAllData,
                    'count'=>$count
                );
                // TODO 添加新客开发数量newCustomerDevelopment

                $cache->set($name,$newData,300);
                $page = $page?$page:1;
                $start = ($page-1)*$limit;
                $data = array_slice($staffAllData,$start,$limit);
            }
            return $this->ajaxTable($data, $count);
        } catch (\Exception $e) {
            $error = $e->getMessage();
            return $this->ajaxTable(array(), 0, $error);
        }
    }

    public function getAllStaff(){
        $StaffModel = new StaffModel;
        $userInfo = session('userinfo');
        $merchantId = $userInfo['merchantid'];
        $storeId = $userInfo['storeid'];
        $map = array(
            'merchantid'=>$merchantId,
            'status'=>array('in','1,2')
        );
        if($storeId){
            $map['storeid'] = $storeId;
        }
        $field = array(
            'id','nickname as name','storeid','is_dimission'
        );
        $staffData = $StaffModel->getAll($map,$field);
        if($staffData){
            return $this->ajaxSuccess($staffData);
        }else{
            return $this->ajaxFail('请先添加一个有效员工');
        }
    }

    /*保存核算业绩*/
    public function doCheckRefundCommission(){
        $post = input('post.');
        $userInfo = session('userinfo');
        $id = $post['id'];
        $where = array(
            'id'=>$id,
            'merchantid'=>$userInfo['merchantid'],
            'pid'=>$post['pid'],
            'refund'=>2,
            'check'=>2
        );
        $remark = isset($post['remark'])?$post['remark']:'';
        $updateData = array(
            'performance'=>round($post['newPerformance']*-100,0),
            'commission'=>round($post['newCommission']*-100,0),
            'check'=>1,
            'remark'=>$remark
        );
        $DeStaComModel = new DeStaComModel;
        $res = $DeStaComModel->updateData($where,$updateData);
        if($res){
            return $this->ajaxSuccess('设置成功');
        }else{
            $msg = $DeStaComModel->error;
            $msg = $msg?$msg:'设置失败';
            return $this->ajaxFail($msg);
        }
    }

    //导出员工业绩
    public function importCommission()
    {
        $get = input('get.');
        $user = session('userinfo');
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        if (!empty($get['time'])){
            $arr = explode(',',$get['time']);
            $startTime = $arr[0];
            $endTime = $arr[1];
        }else{
            $startTime = 0;
            $endTime = 0;
        }
        if ($startTime == 0 || $endTime == 0) {
            switch ($get['type']) {
                case 0:
                    $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                    $startTime = $endTime - 86400;
                    break;
                case 1:
                    $endTime = strtotime(date('Y-m-d') . ' 00:00:00');
                    $startTime = $endTime - 86400;
                    break;
                case 2:
                    $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                    $startTime = $endTime - 86400 * 7;
                    break;
                case 3:
                    $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                    $startTime = strtotime(date('Y-m') . '-1 00:00:00');
                    break;
            }
        } else {
            $startTime = strtotime($startTime . ' 00:00:00');
            $endTime = strtotime($endTime . ' 23:59:59') + 1;
        }
        $where = array(
            'order_time' => array('between', array($startTime, $endTime)),
            'merchantid' => $user['merchantid'],
            'storeid' => $get['sid'],
            'check' => 1,
            'status' => 1
        );
        if(isset($get['id']) && $get['id']){
            $where['staff_id'] = $get['id'];
        }

        $DeStaComModel = new DeStaComModel;
        $field = '*';
        $append = array(
            'staffName'
        );
        $order= '';
        $sql = $DeStaComModel->getSelect($where, $field, $append,$order);
        $performanceTotal = 0;
        $commissionTotal = 0;

        $where['_string'] = "`refund` = 1 OR (`refund` = 2 AND `check` = 1)";
        $where['deduct_type'] = 1;
        $where['goods_type'] = 3;
        // $totalsales = $DeStaComModel->sumField($where,'base_amount');

        $sPer = $DeStaComModel->sumField($where,'performance');
        $sCom = $DeStaComModel->sumField($where,'commission');
        $where['goods_type'] = 1;
        $where['goods_type'] = 1;
        $tPer = $DeStaComModel->sumField($where,'performance');
        $tCom = $DeStaComModel->sumField($where,'commission');


        foreach ($sql as $k=>&$v) {
            $v['storeid'] = $this->storeName($v['storeid']);  //获取门店名称
            $v['order_time'] = date('Y-m-d H:i:s',$v['order_time']);  //核算时间
            if ($v['deduct_type']==1){  //1，销售提成，2，服务提成
                $v['deduct_type'] = '销售提成';
            }else{
                $v['deduct_type'] = '服务提成';
            }
            $performanceTotal += $v['performance'];
            $commissionTotal += $v['commission'];
            $v['performance'] = number_format($v['performance']/100,2,'.','');
            $v['commission'] = number_format($v['commission']/100,2,'.','');
            if ($v['refund']==1){  //退款：1，不是，2，是
                $v['refund'] = '否';
            }else{
                $v['refund'] = '是';
            }
        }
        $performanceTotal = number_format($performanceTotal/100,2,'.','');
        $commissionTotal = number_format($commissionTotal/100,2,'.','');

        // 设置表头信息
        $objPHPExcel->setActiveSheetIndex(0)

            ->setCellValue('A1', '总业绩金额：')
            ->setCellValue('B1', $performanceTotal.' 元')

            ->setCellValue('A2', '总提成金额：')
            ->setCellValue('B2', $commissionTotal.' 元')

            ->setCellValue('C1', '产品销售总额：')
            ->setCellValue('D1', $sPer.' 元')

            ->setCellValue('C2', '产品销售提成：')
            ->setCellValue('D2', $sCom.' 元')

            ->setCellValue('E1', '服务销售总额')
            ->setCellValue('F1', $tPer.' 元')

            ->setCellValue('E2', '服务销售提成')
            ->setCellValue('F2', $tCom.' 元')

            ->setCellValue('A3', '所属门店')
            ->setCellValue('B3', '员工名称')
            ->setCellValue('C3', '提成类型')
            ->setCellValue('D3', '业绩（元）')
            ->setCellValue('E3', '提成（元）')
            ->setCellValue('F3', '时间')
            ->setCellValue('G3', '是否退款');
        $i=2;  //定义一个i变量，目的是在循环输出数据是控制行数
        $count = count($sql);  //计算有多少条数据
        for ($i = 4; $i <= $count+3; $i++) {
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $sql[$i-4]['storeid']);
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $sql[$i-4]['staffName']);
            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $sql[$i-4]['deduct_type']);
            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $sql[$i-4]['performance']);
            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $sql[$i-4]['commission']);
            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $sql[$i-4]['order_time']);
            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $sql[$i-4]['refund']);
        }
        $objPHPExcel->getActiveSheet()->getDefaultColumnDimension()->setWidth(20);
        $objPHPExcel->getActiveSheet()->getRowDimension('1')->setRowHeight(20);
        $objPHPExcel->getActiveSheet()->getRowDimension('2')->setRowHeight(20);
        $objPHPExcel->getActiveSheet()->getRowDimension('3')->setRowHeight(30);
        $objPHPExcel->getActiveSheet()->getStyle('A3:G3')->getFont()->setSize(14);//设置文字大小
        $objPHPExcel->getActiveSheet()->getStyle('A3:G3')->getFont()->setBold(true);//设置是否加粗

        $objPHPExcel->getActiveSheet()->setTitle('员工业绩信息');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter( $objPHPExcel,"Excel2007");
        $filename='员工业绩信息'.'_'.date('YmdHis').'.xlsx';
        header('Content-Disposition: attachment;filename='.$filename);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }
}