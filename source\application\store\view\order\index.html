<div class="layui-fluid">
  <div id="productList" v-cloak style="min-height: 100%" v-loading="loading2">
    <el-card class="box-card">
      <el-form :inline="true" size="small">
        <el-form-item label="订单类型:">
          <el-select
            v-model="order_type"
            size="small"
            @change="order_typeA"
            style="width: 160px"
          >
            <el-option label="全部订单" value="0"></el-option>
            <el-option
              v-for="item in order_types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下单门店:">
          <el-select
            v-model="storeid"
            size="small"
            @change="storeidA"
            style="width: 160px"
          >
            <el-option
              v-if="user.storeid == 0"
              label="全部门店"
              value="0"
            ></el-option>
            <el-option
              v-for="item in storeList"
              :key="item.id"
              :label="item.storetag"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="订单编号：">
          <el-input
            v-model="order_number"
            placeholder="请输入订单编号"
            size="small"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="getOrderList"
            ></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="客户信息：">
          <el-input
            placeholder="可输入客户名、备注名、手机、会员号"
            v-model="keyword"
            clearable
            @keyup.enter.native="current_change(1)"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="current_change(1)"
            ></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="退款状态:">
          <el-select
            v-model="isRefund"
            size="small"
            @change="storeidA"
            style="width: 160px"
          >
            <el-option
              v-for="item in refundList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="核销状态:">
              <el-select
                v-model="isVerify"
                size="small"
                @change="storeidA"
                style="width: 160px"
              >
                <el-option
                  v-for="item in verifyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item> -->

        <el-row type="flex">
          <div>
            <el-form-item label="" label-width="0px">
              <el-select
                v-model="dateType"
                placeholder="请选择"
                size="small"
                style="width: 120px"
              >
                <el-option key="1" label="下单时间：" value="1"></el-option>
                <el-option key="2" label="付款时间：" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <el-date-picker
                @change="Time"
                size="small"
                v-model="StartTime"
                type="datetime"
                style="width: 182px"
                value-format="timestamp"
                placeholder="开始日期时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <span>至</span>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <el-date-picker
                @change="Time"
                size="small"
                v-model="EndTime"
                type="datetime"
                style="width: 182px"
                value-format="timestamp"
                placeholder="结束日期时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <el-button type="primary" size="small" @click="today"
                >今天</el-button
              >
              <el-button type="primary" size="small" @click="threeDay"
                >近三天</el-button
              >
              <el-button type="primary" size="small" @click="weekDay"
                >近一周</el-button
              >
            </el-form-item>
            <el-button type="primary" @click="importOrder()" size="small" plain>
              导出订单
            </el-button>
          </div>
        </el-row>
      </el-form>

      <el-row style="margin-bottom: 0px">
        <template>
          <el-tabs
            v-model="switchs"
            type="card"
            @tab-click="handleClick"
            size="small"
          >
            <el-tab-pane label="全部" name="0"></el-tab-pane>
            <el-tab-pane label="待付款" name="1"></el-tab-pane>
            <!-- <el-tab-pane label="待发货" name="2"></el-tab-pane> -->
            <!-- <el-tab-pane label="已发货" name="3"></el-tab-pane> -->
            <el-tab-pane label="已完成" name="4"></el-tab-pane>
            <el-tab-pane label="已退款" name="6">
              <span slot="label">
                已退款
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content">退款状态以此处的为准</div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
            </el-tab-pane>
            <!-- <el-tab-pane label="待还款" name="7"></el-tab-pane> -->
            <!-- <el-tab-pane label="已还款" name="8"></el-tab-pane> -->
            <el-tab-pane label="已取消" name="5"></el-tab-pane>
          </el-tabs>
        </template>
      </el-row>
      <el-row style="margin-bottom: 20px" id="orderListContent">
        <template>
          <el-table :data="orderList" style="width: 100%">
            <el-table-column>
              <template slot="header" slot-scope="scope">
                <div class="title" style="width: 100%">
                  <div style="width: 20%; line-height: 26px">商品</div>
                  <div class="Cells" style="line-height: 26px">单价×数量</div>
                  <div class="Cells" style="line-height: 26px">
                    {$storeCraftsmanName}/销售
                  </div>
                  <div class="Cells" style="line-height: 26px">客户</div>
                  <div class="Cells" style="line-height: 26px">金额</div>
                  <div class="Cells" style="line-height: 26px">下单门店</div>
                  <div class="Cells" style="line-height: 26px">状态</div>
                </div>
              </template>
              <template slot-scope="scope">
                <el-card
                  class="box-card"
                  shadow="hover"
                  key="key"
                  v-for="(item,key) in scope"
                  v-if="key=='row'"
                >
                  <div slot="header" class="clearfix">
                    <span>{{item.order_time}}</span>
                    <i
                      class="el-icon-edit text-primary hover:text-primary/80 cursor-pointer ml-2"
                      @click="handleModifyOrderTime(item)"
                    ></i>
                    <el-divider direction="vertical"></el-divider>
                    <span
                      class="text-sm px-2 py-0.5 rounded w-fit ml-2"
                      :class="getOrderTypeColor(item.type)"
                    >
                      {{getOrderTypeName(item.type)}}
                    </span>
                    <span class="float-right"
                      ><span class="text-gray">订单号：</span>
                      {{item.order_number}}</span
                    >
                  </div>
                  <div>
                    <div class="order_data">
                      <div style="width: 60%">
                        <div
                          class="flex items-center py-2"
                          v-for="(key,indexs) in item.orderInfo"
                        >
                          <div class="details shrink-0">
                            <div class="details_img">
                              <el-image
                                :preview-src-list="[key.imgUrl]"
                                style="width: 60px; height: 60px"
                                fit="contain"
                                :src="key.imgUrl"
                              >
                                <div slot="error" class="image-slot">
                                  <i class="el-icon-picture-outline"></i>
                                </div>
                              </el-image>
                            </div>
                            <div class="details_text">
                              <p>{{key.name}}</p>
                              <p
                                style="color: #cacaca"
                                v-if="key.type==1 || key.type==2"
                              >
                                {{key.sku_name}}
                              </p>
                              <p style="color: #cacaca" v-if="key.type==4">
                                充<span
                                  v-text="toMoney(key.recharge_money)"
                                ></span
                                >送<span
                                  v-text="toMoney(key.present_money)"
                                ></span>
                              </p>
                              <p>
                                <span
                                  class="text-xs px-2 py-0.5 rounded w-fit"
                                  :class="getGoodsTypeColor(key.type)"
                                >
                                  {{getGoodsTypeName(key.type)}}
                                </span>
                                <span
                                  v-if="key.promoter_info"
                                  style="margin-left: 20px"
                                >
                                  推广：
                                  <el-button
                                    type="text"
                                    @click="toPromoter(key.promoter_info.id)"
                                    >{{key.promoter_info.name}}</el-button
                                  >
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class=""
                            style="width: 16.7%; text-align: center"
                          >
                            <label>￥</label>
                            <span>{{key.price}}</span>
                            <label>×</label>
                            <span>{{key.num}}</span>
                          </div>
                          <div
                            class="pl-2 text-sm"
                            style="
                              width: 43.4%;
                              border-left: 1px solid #eee;
                              border-right: 1px solid #eee;
                            "
                          >
                            <div class="flex" v-if="key.craftsData.length>0">
                              <div class="w-16 shrink-0 text-gray">
                                {$storeCraftsmanName}：
                              </div>
                              <div>
                                <span v-for="(cMan,index) in key.craftsData">
                                  {{cMan.nickname}}<span
                                    v-if="index!=key.craftsData.length-1"
                                    >、</span
                                  >
                                </span>
                              </div>
                            </div>
                            <div
                              class="flex mt-2"
                              v-if="key.cashierData.length>0"
                            >
                              <div class="w-16 shrink-0 text-gray">销售：</div>
                              <div>
                                <span v-for="(cMan,index) in key.cashierData">
                                  {{cMan.nickname}}<span
                                    v-if="index!=key.cashierData.length-1"
                                    >、</span
                                  >
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="Cells content"
                        style="border-right: 1px solid #eee"
                      >
                        <div style="width: 100%; overflow: hidden">
                          <div
                            class="text-primary hover:opacity-80 cursor-pointer"
                            @click="toVip(item.vip.id)"
                          >
                            {{item.vip.member_name}}
                          </div>
                          <div>{{item.vip.phone}}</div>
                          <!-- <p>{{item.vip.Grade}}</p> -->
                        </div>
                      </div>
                      <div
                        class="Cells content"
                        style="border-right: 1px solid #eee"
                      >
                        <p>
                          <label>￥</label>
                          <span>{{item.receivable}}</span>
                        </p>
                        <p v-if="item.state == 1 && item.net_receipts>0">
                          <label>定金:￥</label>
                          <span v-text="toMoney(item.net_receipts)"></span>
                        </p>
                        <p v-if="item.is_debt == 1 && item.debt_value>0">
                          <label>欠款:￥</label>
                          <span v-text="toMoney(item.debt_value)"></span>
                        </p>
                      </div>
                      <div
                        class="Cells content"
                        style="border-right: 1px solid #eee"
                      >
                        {{item.storeTag}}
                      </div>
                      <div class="Cells content">
                        <template v-if="item.state==4 && item.is_verify>0">
                          <p v-if="item.is_verify==1">待到店核销</p>
                          <p v-if="item.is_verify==2">已到店核销</p>
                        </template>
                        <el-tag
                          v-else
                          :type="item.order_state =='已完成'?'success': item.order_state =='待付款'?'warning':'info'"
                          size="medium"
                          >{{item.order_state}}</el-tag
                        >
                        <p>
                          <el-button type="text" @click="SeeOrder(item.id)">
                            订单详情
                          </el-button>
                        </p>
                        <p v-if="item.is_refund==1">退款成功</p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="buttoms mt-1"
                    v-if="scope.row.presentData.length>0"
                  >
                    <el-row type="flex" align="middle">
                      <div style="padding-right: 20px">
                        <el-tag effect="plain">开单赠送</el-tag>
                      </div>
                      <div>
                        <el-table
                          :show-header="false"
                          :key="'presentData_'+scope.row.id"
                          :data="scope.row.presentData"
                        >
                          <el-table-column
                            prop="itemType"
                            label="类型"
                            width="100"
                          >
                            <template slot-scope="itemScope">
                              <template v-for="it in goods_types">
                                <el-tag
                                  size="mini"
                                  v-if="itemScope.row.itemType == it.value"
                                  >{{it.label}}</el-tag
                                >
                              </template>
                            </template>
                          </el-table-column>
                          <el-table-column
                            show-overflow-tooltip
                            prop="date"
                            label="名称"
                            width="200"
                          >
                            <template slot-scope="itemScope">
                              <el-row type="flex" align="middle">
                                <div style="flex-shrink: 0">
                                  <el-image
                                    style="width: 50px; height: 50px"
                                    :src="itemScope.row.imgUrl"
                                    fit="contain"
                                  >
                                    <div
                                      slot="error"
                                      class="image-slot"
                                      style="
                                        width: 50px;
                                        height: 50px;
                                        font-size: 20px;
                                        display: flex;
                                        justify-content: center;
                                        align-items: center;
                                      "
                                    >
                                      <i class="el-icon-picture-outline"></i>
                                    </div>
                                  </el-image>
                                </div>
                                <div
                                  class="el-row--flex"
                                  style="
                                    padding-left: 10px;
                                    flex-direction: column;
                                    line-height: 14px;
                                  "
                                >
                                  <div
                                    style="
                                      white-space: nowrap;
                                      overflow: hidden;
                                    "
                                  >
                                    <p v-text="itemScope.row.name"></p>
                                  </div>
                                  <p
                                    v-if="itemScope.row.sku_name"
                                    v-text="itemScope.row.sku_name"
                                  ></p>
                                </div>
                              </el-row>
                            </template>
                          </el-table-column>
                          <el-table-column
                            prop="price"
                            label="价值"
                            width="100"
                          >
                            <template slot-scope="itemScope">
                              <span
                                v-text="'￥'+toMoney(itemScope.row.price)"
                              ></span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="num" label="数量" width="80">
                          </el-table-column>
                          <el-table-column
                            prop="status"
                            label="状态"
                            width="100"
                          >
                            <template slot-scope="itemScope">
                              <span v-if="itemScope.row.status==0">仅选择</span>
                              <span
                                style="color: #67c23a"
                                v-if="itemScope.row.status==1"
                                >已赠送</span
                              >
                              <span
                                style="color: #f56c6c"
                                v-if="itemScope.row.status==2"
                                >已退回</span
                              >
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-row>
                  </div>
                  <div class="buttoms mt-4 pt-2">
                    <div>
                      <span class="text-gray">备注：</span>
                      <span>{{item.remarks}}</span>
                    </div>
                    <div
                      v-if="(item.state == 1|| item.state == 2) && item.is_refund==2"
                    >
                      <el-button
                        size="mini"
                        @click="ReviseRemarks(item.id,item.remarks)"
                      >
                        备注
                      </el-button>
                      <el-button
                        size="mini"
                        type="primary"
                        plain
                        @click="cancelOrder(item.id,item)"
                        v-if="item.state == 1"
                        >取消订单
                      </el-button>
                      <el-button
                        size="mini"
                        type="primary"
                        plain
                        @click="hairGoods(item.orderInfo,item.id,item.address_info,item.dispatch_type)"
                        v-if="item.state == 2 && item.dispatch_type == 2"
                        >发货
                      </el-button>
                      <el-button
                        size="mini"
                        type="primary"
                        plain
                        @click="hairGoods(item.orderInfo,item.id,item.address_info,item.dispatch_type)"
                        v-if="item.state == 2 && item.dispatch_type == 1"
                        >自取
                      </el-button>
                    </div>
                    <div v-else>
                      <el-button
                        v-if="(item.type != 6) && (item.is_verify!=1)"
                        size="mini"
                        @click="changeStaffCommission(item.order_number)"
                      >
                        调整业绩及提成
                      </el-button>
                    </div>
                  </div>
                </el-card>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="margin-top: 10px; float: right"
            background
            @size-change="size_change"
            @current-change="current_change"
            :pager-count="5"
            :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </template>
        <el-backtop
          target="#orderListContent .is-scrolling-none"
          :right="80"
          :bottom="100"
        >
          <i class="el-icon-top"></i>
        </el-backtop>
      </el-row>
    </el-card>
    <el-dialog
      title="产品发货"
      :visible.sync="DeliverGoods"
      :modal-append-to-body="false"
      width="600px"
    >
      <template>
        <el-table :data="DeliverGoodsDetails" style="width: 100%">
          <el-table-column prop="imgUrl" label="图片" width="100">
            <template slot-scope="scope">
              <div style="width: 60px; height: 60px">
                <el-image
                  style="width: 60px; height: 60px"
                  fit="contain"
                  :src="scope.row.imgUrl"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="产品" width="200">
          </el-table-column>
          <el-table-column prop="price" label="单价"> </el-table-column>
          <el-table-column prop="num" label="数量" align="right">
            <template slot-scope="scope">
              <div>
                <span>x</span>
                <span>{{scope.row.num}}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <el-form
        ref="addressForm"
        :rules="rules"
        :model="addressForm"
        style="margin-top: 20px"
        label-width="100px"
        size="small"
      >
        <div v-if="addressForm.dispatch_type == 2">
          <el-form-item label="收货人："> {{addressForm.name}} </el-form-item>
          <el-form-item label="收货电话：">
            {{addressForm.phone}}
          </el-form-item>
          <el-form-item label="收货地址：">
            {{addressForm.address}}
          </el-form-item>
          <el-form-item label="发货方式：">
            <template>
              <el-radio-group v-model="addressForm.delivery">
                <el-radio
                  v-for="item in Delivery"
                  :key="item.val"
                  :label="item.val"
                  :value="item.val"
                  >{{item.name}}
                </el-radio>
              </el-radio-group>
            </template>
          </el-form-item>
          <el-form-item
            label="物流单号："
            prop="courier_num"
            v-if="addressForm.delivery==1"
          >
            <el-col :span="8">
              <el-input
                v-model="addressForm.courier_num"
                placeholder="请输入快递单号"
              ></el-input>
            </el-col>
          </el-form-item>
          <el-form-item
            label="物流公司："
            prop="logistics"
            v-if="addressForm.delivery==1"
          >
            <el-col :span="8">
              <el-select
                v-model="addressForm.logistics"
                filterable
                placeholder="请选择物流公司"
              >
                <el-option
                  v-for="item in logisticsList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  >{{item.name}}
                </el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </div>
        <div v-if="addressForm.dispatch_type == 1">
          <el-form-item label="取件人："> {{addressForm.name}} </el-form-item>
          <el-form-item label="取件人电话：">
            {{addressForm.phone}}
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="DeliverGoods = false">取 消 </el-button>
        <el-button type="primary" size="mini" @click="DeliverGoodsDes()"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="备注"
      :visible.sync="dialogFormVisible"
      :modal-append-to-body="false"
      width="500px"
    >
      <el-input
        type="textarea"
        :rows="4"
        placeholder="请输入内容"
        v-model="remarks"
      >
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"
          >取 消
        </el-button>
        <el-button type="primary" size="mini" @click="Edit(id,remarks)"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="导出订单明细报表"
      :visible.sync="importOrderVisible"
      :modal-append-to-body="false"
      width="800px"
    >
      <template>
        <el-row style="margin-bottom: 20px; flex-wrap: wrap" type="flex">
          <span style="line-height: 32px; margin-right: 20px">下单门店：</span>
          <el-select v-model="store_id" size="small" style="width: 160px">
            <el-option label="全部门店" value="0"></el-option>
            <el-option
              v-for="item in storeList"
              :key="item.id"
              :label="item.storetag"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-row>
        <el-row style="margin-bottom: 20px; flex-wrap: wrap" type="flex">
          <span style="line-height: 32px; margin-right: 20px">订单类型：</span>
          <el-select v-model="o_type" size="small" style="width: 160px">
            <el-option label="全部订单" value="0"></el-option>
            <el-option
              v-for="item in order_types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-row>
        <el-row style="margin-bottom: 20px; flex-wrap: wrap" type="flex">
          <span style="line-height: 32px; margin-right: 20px">订单状态：</span>
          <el-radio-group v-model="type" size="small">
            <el-radio-button label="0">全部</el-radio-button>
            <el-radio-button label="1">待付款</el-radio-button>
            <el-radio-button label="2">待发货</el-radio-button>
            <el-radio-button label="3">已发货</el-radio-button>
            <el-radio-button label="4">已完成</el-radio-button>
            <el-radio-button label="5">已取消</el-radio-button>
          </el-radio-group>
        </el-row>
        <el-row style="margin-bottom: 20px; flex-wrap: wrap" type="flex">
          <span style="line-height: 32px; margin-right: 20px">选择日期：</span>
          <el-select
            v-model="d_type"
            placeholder="请选择"
            size="small"
            style="width: 120px"
          >
            <el-option key="1" label="下单日期：" value="1"></el-option>
            <el-option key="2" label="付款日期：" value="2"></el-option>
          </el-select>
          <div class="block" style="margin: 0 10px">
            <el-date-picker
              size="small"
              v-model="start"
              type="date"
              style="width: 182px"
              value-format="yyyy-MM-dd"
              placeholder="开始日期时间"
            >
            </el-date-picker>
            <span>至</span>
            <el-date-picker
              size="small"
              v-model="end"
              type="date"
              style="width: 182px"
              value-format="yyyy-MM-dd"
              placeholder="结束日期时间"
            >
            </el-date-picker>
          </div>
        </el-row>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="importOrderVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" size="mini" @click="dayClicks()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="修改时间"
      width="300px"
      :visible.sync="isShowModifyTimeDialog"
      :before-close="handleModifyTimeDialogClose"
      :append-to-body="true"
    >
      <el-date-picker
        v-model="modifyTimeData.cdate"
        type="datetime"
        value-format="yyyy-MM-dd HH:mm:ss"
        placeholder="选择日期时间"
      >
      </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleModifyTimeDialogClose">取 消</el-button>
        <el-button type="primary" @click="modifyTimeDialogConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</div>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#productList",
      data() {
        return {
          user: {$user},
          loading2: false,
          id: "",
          remarks: "",
          order_number: "",
          dateType: "1",
          StartTime: "",
          EndTime: "",
          switchs: "4",
          isRefund: 0,
          keyword: "",
          refundList: [
            {id: 0, name: "全部"},
            {id: 1, name: "已退款"},
            {id: 2, name: "未退款"},
          ],
          Payment_method: "0",
          Payment_methods: [
            {
              value: "1",
              label: "现金",
            },
            {
              value: "2",
              label: "余额",
            },
            {
              value: "3",
              label: "微信",
            },
            {
              value: "4",
              label: "支付宝",
            },
          ],
          order_type: "0",
          order_types: [
            {
              value: "1",
              label: "品项",
              color: "o-tag-indigo",
            },
            {
              value: "2",
              label: "产品",
              color: "o-tag-pink",
            },
            {
              value: "3",
              label: "售卡",
              color: "o-tag-fuchsia",
            },
            {
              value: "4",
              label: "充值",
              color: "o-tag-gray",
            },
            {
              value: "5",
              label: "充卡",
              color: "o-tag-lime",
            },
            {
              value: "6",
              label: "直接收款",
              color: "o-tag-pink",
            },
            {
              value: "7",
              label: "获客活动",
              color: "o-tag-gray",
            },
            {
              value: "8",
              label: "股东礼券",
              color: "o-tag-gray",
            },
          ],
          goods_types: [
            {
              value: "1",
              label: "服务",
              color: "o-tag-blue",
            },
            {
              value: "2",
              label: "产品",
              color: "o-tag-cyan",
            },
            {
              value: "3",
              label: "售卡",
              color: "o-tag-fuchsia",
            },
            {
              value: "4",
              label: "充值",
              color: "o-tag-gray",
            },
            {
              value: "5",
              label: "直接收款",
              color: "o-tag-pink",
            },
            {
              value: "6",
              label: "获客活动",
              color: "o-tag-gray",
            },
            {
              value: "7",
              label: "股东礼券",
              color: "o-tag-gray",
            },
          ],
          storeid: "0",
          storeList: [],
          Integral_deduction: "0",
          Integral_deductions: [
            {
              value: "1",
              label: "使用",
            },
            {
              value: "2",
              label: "未使用",
            },
          ],
          Marketing_order: "0",
          Marketing_orders: [
            {
              value: "1",
              label: "积分兑换",
            },
          ],
          dialogFormVisible: false,
          formLabelWidth: "120px",
          value: "0",
          promotionloading: true,
          page: 1,
          limit: 10,
          total: 0,
          orderList: [],

          orderid: "",
          DeliverGoods: false,
          DeliverGoodsDetails: [
            {
              imgUrl: "",
              name: "",
              price: "",
              num: "",
            },
          ],
          addressForm: {
            dispatch_type: "",
            name: "",
            phone: "",
            address: "",
            delivery: 1,
            courier_num: "",
            logistics: "",
          },
          Delivery: [
            {
              name: "物流发货",
              val: 1,
            },
            {
              name: "无需物流",
              val: 2,
            },
          ],
          rules: {
            courier_num: [
              {required: true, message: "请输入快递单号", trigger: "blur"},
            ],
            logistics: [
              {required: true, message: "请选择快递公司", trigger: "change"},
            ],
          },
          logisticsList: [],
          imgLoadError: "/assets/common/images/img_load_error.png",
          verifyList: [
            {id: -1, name: "全部"},
            {id: 0, name: "非核销"},
            {id: 1, name: "待核销"},
            {
              id: 2,
              name: "已核销",
            },
          ],
          isVerify: -1,
          // 导出传参数
          importOrderVisible: false,
          store_id: "0",
          o_type: "0",
          d_type: "1",
          type: "0",
          start: "",
          end: "",
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            },
          },
          isShowModifyTimeDialog: false,
          modifyTimeData: {
            id: "",
            cdate: "",
            merchantid: "",
            storeid: "",
          },
        };
      },
      methods: {
        changeStaffCommission(orderNo) {
          location.hash = "/Order/deduct/orderNo=" + orderNo;
        },
        getOrderTypeName(type) {
          return this.order_types.find((item) => item.value == type).label;
        },
        getOrderTypeColor(type) {
          return this.order_types.find((item) => item.value == type).color;
        },
        getGoodsTypeName(type) {
          return this.goods_types.find((item) => item.value == type).label;
        },
        getGoodsTypeColor(type) {
          return this.goods_types.find((item) => item.value == type).color;
        },
        toPromoter(pid) {
          window.open("#/Promoters/details/id=" + pid);
        },
        // 导出订单打开弹窗
        importOrder() {
          var that = this;
          that.importOrderVisible = true;
        },
        dayClick() {
          var that = this;
          that.times = "";
        },
        //确定导出数据
        dayClicks() {
          var that = this;
          var store_id = that.store_id;
          var type = that.type;
          var d_type = that.d_type;
          var o_type = that.o_type;
          var start = that.start;
          var end = that.end;

          if (start == "" || end == "") {
            return that.$message({
              message: "请选择开始结束日期",
              type: "info",
            });
          }
          if (start > end) {
            return that.$message({
              message: "结束日期不能小于开始日期",
              type: "info",
            });
          }
          location.href =
            "{:url('store/Order/importOrderList')}" +
            "&sid=" +
            store_id +
            "&type=" +
            type +
            "&o_type=" +
            o_type +
            "&d_type=" +
            d_type +
            "&start=" +
            start +
            "&end=" +
            end;
        },
        log(item) {
          console.log(item);
          return 1;
        },
        toMoney(num) {
          return (num / 100).toFixed(2);
        },
        imgError(item) {
          item.imgUrl = this.imgLoadError;
        },
        //发货
        hairGoods(orderInfo, id, address_info, dispatch_type) {
          var that = this;
          that.DeliverGoods = true;
          that.DeliverGoodsDetails = orderInfo;
          that.orderid = id;
          that.addressForm.dispatch_type = dispatch_type;
          that.addressForm.name = address_info.name;
          that.addressForm.phone = address_info.tel;
          that.addressForm.address = address_info.address;
          that.getLogistics();
        },
        getLogistics: function () {
          var that = this;
          $.ajax({
            url: "{:url('getLogistics')}",
            type: "post",
            data: {},
            success: function (res) {
              if (res.code == 1) {
                that.logisticsList = res.data;
              }
            },
          });
        },
        //取件，发货操作
        DeliverGoodsDes() {
          var that = this;
          var orderid = that.orderid;
          var type = that.addressForm.dispatch_type;
          if (type == 2) {
            var msg = "是否确认发货？";
            var delivery = that.addressForm.delivery;
            var courier_num = that.addressForm.courier_num;
            var logistics = that.addressForm.logistics;
            if (delivery == 1) {
              if (courier_num == "" || logistics == "") {
                return that.$message({
                  message: "请填写必填项",
                  type: "info",
                });
              }
            }
            var data = {
              orderid: orderid,
              type: type,
              delivery: delivery,
              courier_num: courier_num,
              logistics: logistics,
            };
          } else {
            var msg = "是否完成取货？";
            var data = {
              orderid: orderid,
              type: type,
            };
          }
          that
            .$confirm(msg, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(() => {
              $.ajax({
                url: "{:url('EditDelivery')}",
                type: "post",
                data: data,
                success: function (res) {
                  if (res.code == 1) {
                    return that.$message({
                      message: res.msg,
                      type: "success",
                      onClose: function () {
                        that.DeliverGoods = false;
                        that.getOrderList();
                      },
                    });
                  } else {
                    return that.$message({
                      message: res.msg,
                      type: "info",
                    });
                  }
                },
              });
            })
            .catch(() => {
              that.$message({
                type: "info",
                message: "已取消发货",
              });
            });
        },
        //执行取件，发货操作接口
        EditDelivery() {
          var orderid = that.orderid;
          var type = that.addressForm.dispatch_type;
          if (type == 2) {
            var delivery = that.addressForm.delivery;
            var courier_num = that.addressForm.courier_num;
            var logistics = that.addressForm.logistics;
            if (delivery == 1) {
              if (courier_num == "" || logistics == "") {
                return that.$message({
                  message: "请填写必填项",
                  type: "info",
                });
              }
            }
            var data = {
              orderid: orderid,
              type: type,
              delivery: delivery,
              courier_num: courier_num,
              logistics: logistics,
            };
          } else {
            var data = {
              orderid: orderid,
              type: type,
            };
          }
          $.ajax({
            url: "{:url('EditDelivery')}",
            type: "post",
            data: data,
            success: function (res) {
              if (res.code == 1) {
                return that.$message({
                  message: res.msg,
                  type: "success",
                  onClose: function () {
                    that.DeliverGoods = false;
                    that.getOrderList();
                  },
                });
              } else {
                return that.$message({
                  message: res.msg,
                  type: "info",
                });
              }
            },
          });
        },
        ReviseRemarks(id, remarks) {
          var that = this;
          that.id = id;
          that.remarks = remarks;
          that.dialogFormVisible = true;
        },
        handleClick() {
          var that = this;
          that.getOrderList();
        },
        Time() {
          var that = this;
          that.getOrderList();
        },
        //今天
        today() {
          var that = this;
          that.StartDay(0);
        },
        //近三天
        threeDay() {
          var that = this;
          that.StartDay(2);
        },
        //近一周
        weekDay() {
          var that = this;
          that.StartDay(6);
        },
        //某一天时间
        StartDay: function (num) {
          var that = this;
          $.ajax({
            url: "{:url('GetTime')}",
            type: "post",
            data: {
              num: num,
            },
            success: function (res) {
              that.StartTime = res.data.StartTime;
              that.EndTime = res.data.EndTime;
              that.getOrderList();
            },
          });
        },
        //付款方式
        Payment_methodA() {
          var that = this;
          that.getOrderList();
        },
        //订单类型
        order_typeA() {
          var that = this;
          that.getOrderList();
        },
        //下单门店
        storeidA() {
          var that = this;
          that.getOrderList();
        },
        //跳转详情页
        SeeOrder(id) {
          // window.open("#/Order/seorder/id=" + id);
          location.hash = "/Order/seorder/id=" + id;
        },
        //修改备注
        Edit(id, remarks) {
          var that = this;
          $.ajax({
            url: "{:url('EditRemarks')}",
            type: "post",
            data: {
              id: id,
              remarks: remarks,
            },
            success: function (res) {
              if (res.code == 1) {
                return that.$message({
                  message: res.msg,
                  type: "success",
                  onClose: function () {
                    that.dialogFormVisible = false;
                    that.getOrderList();
                  },
                });
              } else {
                return that.$message({
                  message: res.msg,
                  type: "info",
                });
              }
            },
          });
        },
        //取消订单
        cancelOrder(id, item) {
          var that = this;
          this.$confirm("是否取消该笔订单", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('cancelOrder')}",
                type: "post",
                data: {
                  id: id,
                  storeid: item.store_id,
                  vipId: item.vip_id,
                },
                success: function (res) {
                  if (res.code == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                  that.getOrderList();
                },
              });
            })
            .catch(() => {
              that.$message({
                type: "info",
                message: "已退出取消订单",
              });
            });
        },
        //获取门店
        getStoreList: function () {
          var that = this;
          var storeid = that.user.storeid;
          var merchantid = that.user.merchantid;
          $.ajax({
            url: "{:url('getStoreList')}",
            type: "post",
            data: {
              storeid: storeid,
              merchantid: merchantid,
            },
            success: function (res) {
              if (res.code == 1) {
                that.storeList = res.data;
              } else {
                that.storeList = "";
              }
            },
          });
        },
        //获取订单信息
        getOrderList: function () {
          var that = this;
          that.loading2 = true;
          that.promotionloading = true;
          $.ajax({
            url: "{:url('getOrderList')}",
            type: "post",
            data: {
              keyword: that.keyword,
              page: that.page,
              limit: that.limit,
              order_number: that.order_number,
              dateType: that.dateType, //时间搜索类型 1.下单时间  2.付款时间
              StartTime: that.StartTime,
              EndTime: that.EndTime,
              Payment_method: that.Payment_method,
              order_type: that.order_type,
              switchs: that.switchs,
              isRefund: that.isRefund,
              storeid: that.storeid,
              cashierData: "object",
              isVerify: that.isVerify,
            },
            success: function (res) {
              if (res.code == 0) {
                that.orderList = res.data;
                that.total = res.count;
              } else {
                that.orderList = [];
                that.total = 0;
              }
              that.promotionloading = false;
              that.loading2 = false;
            },
          });
        },
        size_change(val) {
          var that = this;
          that.limit = val;
          that.page = 1;
          that.getOrderList();
        },
        /* 切换页数 */
        current_change(val) {
          var that = this;
          that.page = val;
          that.getOrderList();
        },
        toVip(vid) {
          var lhash = "/Member/det/det/" + vid;
          // window.open("#" + lhash);
          location.hash = lhash;
        },
        handleModifyOrderTime(data) {
          this.modifyTimeData.cdate = data.order_time;
          this.modifyTimeData.id = data.id;
          this.modifyTimeData.merchantid = data.merchant_id;
          this.modifyTimeData.storeid = data.store_id;
          this.isShowModifyTimeDialog = true;
        },
        handleModifyTimeDialogClose() {
          this.isShowModifyTimeDialog = false;
          this.modifyTimeData.cdate = "";
          this.modifyTimeData.id = "";
          this.modifyTimeData.merchantid = "";
          this.modifyTimeData.storeid = "";
        },
        modifyTimeDialogConfirm() {
          var that = this;
          $.ajax({
            url: "{:url('/android/order/EditOrdertime')}",
            type: "post",
            data: that.modifyTimeData,
            success: function (res) {
              if (res.code == 1) {
                that.getOrderList();
              } else {
                that.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            },
            error: function (res) {
              that.$message({
                message: res.msg,
                type: "error",
              });
            },
            complete: function () {
              that.isShowModifyTimeDialog = false;
            },
          });
        },
      },
      created: function () {
        var that = this;
        that.getStoreList();
        that.getOrderList();
        var storeid = that.user.storeid;
        if (storeid == 0) {
          that.storeid = storeid.toString();
        } else {
          that.storeid = parseInt(storeid);
        }
      },
      watch: {},
    });
  });
</script>
<style>
  .time {
    display: flex;
    align-items: center;
    height: 50px;
  }

  .Dtime {
    display: flex;
    align-items: center;
    margin-right: 20px;
  }

  .search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_block {
    margin-right: 16px;
    margin-bottom: 10px;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 0;
  }

  .el-card__header {
    padding: 10px 20px;
    /* background-color: #f2f2f2; */
  }

  .order_data {
    display: flex;
    justify-content: space-between;
  }

  .details {
    width: 40%;
    display: flex;
    font-size: 14px;
  }

  .details_img {
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-right: 10px;
  }

  .details_text {
    font-size: 14px;
  }

  .Cells {
    width: 10%;
    text-align: center;
  }

  .content {
    /*height: 80px;*/
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }

  .buttoms {
    width: 100%;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .box-card {
    margin-bottom: 20px;
  }

  .zan {
    text-align: center;
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    color: silver;
  }

  .el-radio-button__inner {
    border: 1px #dcdfe6 solid;
    border-radius: 0;
    margin-right: 20px;
  }

  .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 0;
  }

  .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0;
  }
</style>
