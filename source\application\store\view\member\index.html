<style>
  .demo-table-expand {
    font-size: 0;
  }

  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }

  .el-radio__inner {
    display: none;
  }

  .el-radio__label {
    padding-left: 0;
  }

  .el-radio {
    margin-right: 0;
  }
</style>

<div class="layui-fluid">
  <div id="member" v-cloak>
    <el-card class="box-card" v-loading="promotionloading">
      <div class="flex space-x-6">
        <div class="flex items-center space-x-2">
          <div class="shrink-0">客户信息</div>
          <el-input
            placeholder="可输入客户名、备注名、手机、会员号"
            v-model="keyword"
            size="small"
            clearable
            @keyup.enter.native="getMemberList"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="getMemberList"
            ></el-button>
          </el-input>
        </div>
        <el-button type="primary" @click="add" size="small">新增客户</el-button>
        <el-button v-if="resetNum>0" type="danger" @click="toReset" size="small"
          >重置会员累计金额</el-button
        >
        <el-button @click="importMember()" size="small" plain>
          导出会员
        </el-button>
      </div>
      <el-form
        ref="form"
        :model="form"
        label-width="90px"
        style="margin-top: 20px"
      >
        <el-form-item label="消费频次：" prop="last_time">
          <el-row type="flex" style="flex-wrap: wrap">
            <div @click="lasts">
              <el-radio-group
                v-model="form.last_time"
                size="small"
                @change="getMemberList"
              >
                <el-radio border label="">全部</el-radio>
                <el-radio border label="1">1个月未消费</el-radio>
                <el-radio border label="2">2个月未消费</el-radio>
              </el-radio-group>
            </div>
            <div style="margin-left: 10px" @click="last">
              <el-input
                v-model="form.z_last_time"
                size="small"
                placeholder="自定义"
                @change="getMemberList"
                style="width: 80px"
              >
              </el-input>
              个月未消费
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="消费次数：" prop="counts">
          <el-row type="flex" style="flex-wrap: wrap">
            <div @click="z_counts">
              <el-radio-group
                v-model="form.counts"
                size="small"
                @change="getMemberList"
              >
                <el-radio border label="">全部</el-radio>
                <el-radio border label="0">未消费</el-radio>
                <el-radio border label="3">3次以内</el-radio>
              </el-radio-group>
            </div>
            <div style="margin-left: 10px" @click="counts">
              <el-input
                v-model="form.z_count"
                size="small"
                placeholder="自定义"
                @change="getMemberList"
                style="width: 80px"
              >
              </el-input>
              次以内
            </div>
          </el-row>
        </el-form-item>
        <el-collapse-transition>
          <div v-if="Unfold==1">
            <el-row type="flex" style="flex-wrap: wrap">
              <div v-if="store == 0" style="margin-right: 18px">
                <el-form-item label="所属门店：" prop="store">
                  <el-select
                    v-model="form.store"
                    placeholder="请选择门店"
                    size="small"
                    @change="getMemberList"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in AffiliatedStore"
                      :key="item.id"
                      :label="item.storetag"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <!-- <div style="margin-right: 18px">
                <el-form-item label="会员等级：" prop="level">
                  <el-select
                    v-model="form.level"
                    placeholder="请选择会员等级"
                    size="small"
                    @change="getMemberList"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in MemberLevel"
                      :key="item.id"
                      :label="item.level_name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div> -->
              <div style="margin-right: 18px">
                <el-form-item label="是否会员：" prop="is_vip">
                  <el-radio-group
                    size="small"
                    v-model="form.is_vip"
                    @input="getMemberList"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="0">非会员</el-radio-button>
                    <el-radio-button label="1">会员</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="margin-right: 18px">
                <el-form-item label="新客来源：" prop="source">
                  <el-select
                    v-model="form.source"
                    placeholder="请选择来源"
                    size="small"
                    @change="getMemberList"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in member_source"
                      :key="item.id"
                      :label="item.source_name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-row>
            <el-row type="flex" style="flex-wrap: wrap">
              <div style="margin-right: 18px">
                <el-form-item label="新客归属：" prop="adviser">
                  <el-select
                    v-model="form.adviser"
                    placeholder="请选择新客归属"
                    size="small"
                    @change="getMemberList"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in adviser"
                      :key="item.id"
                      :label="item.nickname"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div style="margin-right: 18px">
                <el-form-item label="会员标签：" prop="tag">
                  <el-select
                    v-model="form.tag"
                    placeholder="请选择标签"
                    size="small"
                    @change="getMemberList"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                      v-for="item in membertag"
                      :key="item.id"
                      :label="item.tag_name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <!--积分筛选  勿删-->
              <!--<el-col :span="6">-->
              <!--<el-form-item label="会员积分：" prop="score1">-->
              <!--<el-input v-model="form.score1" size="small" placeholder="自定义" @change="getMemberList" style="width: 80px;"></el-input> - <el-input v-model="form.score2" size="small" placeholder="自定义" @change="getMemberList" style="width: 80px;"></el-input>-->
              <!--</el-form-item>-->
              <!--</el-col>-->
            </el-row>
            <el-form-item label="生日：" prop="birthday">
              <span @click="Birs">
                <el-radio-group
                  v-model="form.birthday"
                  size="small"
                  @change="getMemberList"
                >
                  <el-radio border label="">全部</el-radio>
                  <el-radio border label="1">今天</el-radio>
                  <el-radio border label="2">明天</el-radio>
                  <el-radio border label="3">本周</el-radio>
                  <el-radio border label="4">本月</el-radio>
                  <el-radio border label="5">下个月</el-radio>
                </el-radio-group>
              </span>
              <span style="margin-left: 10px" @click="Bir">
                <template>
                  <el-date-picker
                    size="small"
                    v-model="form.starttime"
                    type="date"
                    value-format="yyyy-MM-dd"
                    style="width: 140px"
                    placeholder="选择日期"
                    @change="getMemberList"
                  >
                  </el-date-picker>
                  至
                  <el-date-picker
                    size="small"
                    v-model="form.endtime"
                    type="date"
                    value-format="yyyy-MM-dd"
                    style="width: 140px"
                    placeholder="选择日期"
                    @change="getMemberList"
                  >
                  </el-date-picker>
                </template>
              </span>
            </el-form-item>
          </div>
        </el-collapse-transition>
        <el-divider
          ><el-button type="text" size="small" size="small" @click="isUnfold"
            ><span v-html="unfoldTitle"></span></el-button
        ></el-divider>
      </el-form>
      <el-table
        :data="tableData"
        style="width: 100%"
        @selection-change="Member"
        row-key="id"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="用户信息" width="280" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="flex space-x-2 items-center">
              <div class="shrink-0">
                <el-image
                  style="width: 60px; height: 60px; border-radius: 50%"
                  :src="scope.row.pic ? scope.row.pic : 'assets/img/touxoang.png'"
                  fit="contain"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div>
                <div class="flex space-x-2">
                  <div class="font-bold">
                    {{scope.row.member_name ? scope.row.member_name : '--'}}
                  </div>
                  <el-tag
                    size="mini"
                    v-if="scope.row.sex == 1 || scope.row.sex == 2"
                    :type="scope.row.sex == 1 ? 'primary' : scope.row.sex == 2 ? 'danger' : 'info'"
                  >
                    {{scope.row.sex == 1 ? '男' :scope.row.sex == 2 ? '女' :
                    ''}}
                  </el-tag>
                </div>

                <div>
                  <span class="text-gray">账号/手机：</span>{{scope.row.phone ?
                  scope.row.phone : '--'}}
                </div>
                <div>
                  <span class="text-gray">用户编号：</span
                  >{{scope.row.member_number ? scope.row.member_number : '--'}}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="持卡数" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" v-if="scope.row.is_vip" type="warning">
              会员
            </el-tag>
            <div>
              <span class="text-gray">持卡：</span>{{scope.row.card_num}}
            </div>
            <!-- <div>
              <span style="color: #999999">默认账户：</span
              ><span>￥ {{ scope.row.balance }}</span>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column label="累计消费(元)" min-width="140">
          <template slot-scope="scope">
            <div class="text-sm pr-8">
              <div class="flex justify-between items-baseline">
                <div class="text-gray shrink-0">金额：</div>
                <div class="flex-1 text-right font-bold text-normal">
                  {{ formatMoney(scope.row.total)}}
                </div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray">次数：</div>
                <div class="flex-1 text-right">
                  {{scope.row.count}}
                  <span class="text-gray">次</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="last_time" label="距上次消费"> </el-table-column>
        <el-table-column prop="addtime" label="创建日期" min-width="100">
          <template slot-scope="scope">
            <div class="text-sm">{{ scope.row.addtime.split(' ')[0] }}</div>
            <div class="text-sm">
              <span class="text-gray">至今：</span>
              {{ fromTheCurrentTime(scope.row.addtime.split(' ')[0] ) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="store_id" label="归属门店" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="birthday" label="生日/年龄" min-width="100">
          <template slot-scope="scope">
            <div>{{ scope.row.birthday}}</div>
            <div>{{ getAge(scope.row.birthday) }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="pid" label="新客归属">
          <template slot-scope="scope">
            <template v-if="scope.row.pid">
              <el-button
                @click="toPromoter(scope.row.pid)"
                type="text"
                size="small"
                >查看</el-button
              >
            </template>
            <template v-else> -- </template>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <div class="flex space-x-4">
              <div
                class="cursor-pointer text-primary hover:text-primary/80 w-fit"
                @click="det(scope.row.id)"
              >
                详情
              </div>
              <div
                class="cursor-pointer text-red hover:text-red/80 w-fit"
                @click="delMember(scope.row.id)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div
        class="block"
        style="margin-top: 20px; overflow: hidden"
        v-if="total>0"
      >
        <el-button
          size="small"
          style="float: left"
          size="small"
          @click="Del(sels)"
          >批量删除</el-button
        >
        <!-- <el-button
          size="small"
          style="float: left"
          size="small"
          @click="Tab(sels)"
          >添加标签</el-button
        > -->
        <el-pagination
          background
          @size-change="size_change"
          @current-change="current_change"
          :pager-count="5"
          :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
          :page-size="10"
          style="float: right"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
    <el-dialog
      title="添加标签"
      :visible.sync="dialogTablelabel"
      :modal-append-to-body="false"
    >
      <div class="layui-form-item">
        <div class="layui-inline" style="width: 100%">
          <div style="">
            <div>
              <template v-for="item in memberTabData">
                <el-tag size="medium" type="info" style="margin: 4px"
                  >{{ item.tag_name }}</el-tag
                >
              </template>
            </div>
          </div>
          <p style="height: 36px; line-height: 36px">自定义标签</p>
          <div>
            <el-form :ref="forms" :model="forms" label-width="80px">
              <el-input
                v-model="forms.tag_name"
                size="small"
                style="width: 150px"
              ></el-input>
              <el-button @click.prevent="onSubmit" size="small">确定</el-button>
            </el-form>
          </div>
          <p style="height: 36px; line-height: 36px">选择标签</p>
          <div
            style="
              border: 1px rgba(166, 169, 168, 0.3) solid;
              padding: 10px 10px 10px 10px;
              border-radius: 5px;
            "
          >
            <el-checkbox
              v-for="(item,index) in tabData"
              @change="tabCheck(item)"
              :checked="TabEffect(item.id)"
              border
              style="margin: 4px"
              size="mini"
              >{{ item.tag_name }}
            </el-checkbox>
          </div>
          <div style="float: right; margin-top: 20px">
            <el-button
              type="primary"
              size="small"
              @click="dialogTablelabel = false"
              >取消</el-button
            >
            <el-button type="primary" size="small" @click="onSubTag()"
              >确定</el-button
            >
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</div>
<script>
  layui.use(["table", "layer", "index", "form"], function () {
    var table = layui.table;
    var admin = layui.admin;
    var $ = layui.$;
    var form = layui.form;
    form.render();
    new Vue({
      el: "#member",
      data() {
        return {
          store: {$store},
          tableData: [],
          promotionloading: true,
          page: 1,
          limit: 10,
          total: 0,
          member_name: "",
          memberphone: "",
          sels: [], //选中的值显示
          expands: [], // 要展开的行，数值的元素是row的key值
          Level: "",
          MemberLevel: {$member_level},
          Store: "",
          AffiliatedStore: {$storelist},
          source: "",
          member_source: {$member_source},
          membertag: {$membertag},
          adviser: [],
          dialogTablelabel: false,
          dynamicTags: [],
          dangers: "",
          tags: "",
          lastIndex: "",
          currentIndex: "",
          tabData: [],
          keyword: "",
          forms: {
            tag_name: "",
          },
          form: {
            tag_name: "",
            last_time: "",
            z_last_time: "",
            counts: "",
            z_count: "",
            level: "",
            store: "",
            source: "",
            score1: "",
            score2: "",
            adviser: "",
            birthday: "",
            starttime: "",
            endtime: "",
            tag: "",
            is_vip: "",
          },
          ids: [],
          resetNum: 0,
          Unfold: 0,
          unfoldTitle: "展开",
          memberTabData: [],
        };
      },
      methods: {
        formatMoney(money) {
          return ff_util.formatMoney(money / 100);
        },
        fromTheCurrentTime(date) {
          return ff_util.fromTheCurrentTime(date);
        },
        getAge(birthday) {
          return ff_util.getAge(birthday);
        },
        toPromoter(pid) {
          window.open("#/Promoters/details/id=" + pid);
        },
        //确定导出数据
        importMember() {
          var vm = this;
          var last_time = vm.form.last_time ? vm.form.last_time : "";
          var z_last_time = vm.form.z_last_time ? vm.form.z_last_time : "";
          var counts = vm.form.counts ? vm.form.counts : "";
          var z_count = vm.form.z_count ? vm.form.z_count : "";
          var level = vm.form.level ? vm.form.level : "";
          var store = vm.form.store ? vm.form.store : "";
          var source = vm.form.source ? vm.form.source : "";
          var adviser = vm.form.adviser ? vm.form.adviser : "";
          var tag = vm.form.tag ? vm.form.tag : "";
          var birthday = vm.form.birthday ? vm.form.birthday : "";
          var starttime = vm.form.starttime ? vm.form.starttime : "";
          var endtime = vm.form.endtime ? vm.form.endtime : "";
          location.href =
            "{:url('store/Member/importMember')}" +
            "&last_time=" +
            last_time +
            "&z_last_time=" +
            z_last_time +
            "&counts=" +
            counts +
            "&z_count=" +
            z_count +
            "&level=" +
            level +
            "&store=" +
            store +
            "&source=" +
            source +
            "&adviser=" +
            adviser +
            "&tag=" +
            tag +
            "&birthday=" +
            birthday +
            "&starttime=" +
            starttime +
            "&endtime=" +
            endtime;
        },
        isUnfold() {
          //展开收起
          var that = this;
          Unfold = that.Unfold;
          if (Unfold == 0) {
            that.Unfold = 1;
            that.unfoldTitle = "收起";
          } else {
            that.Unfold = 0;
            that.unfoldTitle = "展开";
          }
        },

        onSubmit() {
          var that = this;
          var tag_name = that.forms.tag_name;
          if (tag_name == "") {
            this.$message({
              type: "info",
              message: "请输入标签名称",
            });
            return;
          }
          $.ajax({
            url: "{:url('adTab')}",
            data: {
              tag_name: tag_name,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.forms.tag_name = "";
                that.getTabList();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //
        onSubTag() {
          var vm = this;
          var dynamicTags = vm.memberTabData;
          var ids = vm.ids;
          if (dynamicTags == "") {
            this.$message({
              type: "info",
              message: "请选择标签",
            });
            return;
          }
          $.ajax({
            url: "{:url('setTab')}",
            data: {
              dynamicTags: dynamicTags,
              ids: ids,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
                vm.getMemberList();
                vm.dialogTablelabel = false;
              } else {
                vm.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //详情页
        det(id) {
          // window.open("#/Member/det/det/" + id);
          location.hash = "/Member/det/det/" + id;
        },

        //清除
        last() {
          var vm = this;
          vm.form.last_time = "";
        },
        lasts() {
          var vm = this;
          vm.form.z_last_time = "";
        },
        counts() {
          var vm = this;
          vm.form.counts = "";
        },
        z_counts() {
          var vm = this;
          vm.form.z_count = "";
        },
        Bir() {
          var vm = this;
          vm.form.birthday = "";
        },
        Birs() {
          var vm = this;
          vm.form.starttime = "";
          vm.form.endtime = "";
        },

        getMemberList: function () {
          var vm = this;
          vm.promotionloading = true;
          var member_name = vm.memberphone ? vm.memberphone : "";
          var last_time = vm.form.last_time ? vm.form.last_time : "";
          var z_last_time = vm.form.z_last_time ? vm.form.z_last_time : "";
          var counts = vm.form.counts ? vm.form.counts : "";
          var z_count = vm.form.z_count ? vm.form.z_count : "";
          var level = vm.form.level ? vm.form.level : "";
          var store = vm.form.store ? vm.form.store : "";
          var source = vm.form.source ? vm.form.source : "";
          var score1 = vm.form.score1 ? vm.form.score1 : "";
          var score2 = vm.form.score2 ? vm.form.score2 : "";
          var adviser = vm.form.adviser ? vm.form.adviser : "";
          var tag = vm.form.tag ? vm.form.tag : "";
          var birthday = vm.form.birthday ? vm.form.birthday : "";
          $.ajax({
            url: "{:url('getMemberList')}",
            data: {
              keyword: vm.keyword,
              page: vm.page,
              limit: vm.limit,
              member_name: member_name,
              last_time: last_time,
              z_last_time: z_last_time,
              counts: counts,
              z_count: z_count,
              level: level,
              store: store,
              source: source,
              score1: score1,
              score2: score2,
              adviser: adviser,
              starttime: vm.form.starttime,
              endtime: vm.form.endtime,
              birthday: birthday,
              tag: tag,
              is_vip: vm.form.is_vip,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.tableData = res.data;
                vm.total = res.count;
              }
              vm.promotionloading = false;
            },
          });
        },
        //获取新客归属
        getAdviserList: function () {
          var vm = this;
          $.ajax({
            url: "{:url('getAdviser')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.adviser = res.data;
              }
            },
          });
        },
        //会员标签
        TabEffect(id) {
          for (var i = 0; i < this.memberTabData.length; i++) {
            if (id == this.memberTabData[i]["id"]) {
              return true;
            }
          }
          return false;
        },
        tabCheck(item) {
          for (var i = 0; i < this.memberTabData.length; i++) {
            if (item.id == this.memberTabData[i]["id"]) {
              this.memberTabData.splice(i, 1);
              return;
            }
          }
          this.memberTabData.push(item);
        },
        Member: function (sels) {
          this.sels = sels;
        },

        //添加会员
        add() {
          location.hash = "/Member/member";
        },
        //修改修改
        editMember: function (id) {
          var _this = this;
          admin.popupRight({
            type: 2,
            title: "编辑会员",
            area: "50%",
            content: "{:url('Member')}&id=" + id,
            end: function () {
              _this.getMemberList();
            },
          });
        },
        //批量删除
        Del(sels) {
          var that = this;
          if (sels == "") {
            that.$message({
              type: "info",
              message: "请先选择会员",
            });
            return;
          }
          this.form = sels;
          var ids = [];
          sels.forEach((item) => {
            ids.push(item.id);
          });
          that
            .$confirm("确认批量删除？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('delMember')}",
                type: "post",
                data: {id: ids},
                success: function (res) {
                  if (res.code == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    that.getMemberList();
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },

        //打开标签添加弹框
        Tab(sels) {
          var vm = this;
          if (sels == "") {
            vm.$message({
              type: "info",
              message: "请先选择会员",
            });
            return;
          }
          vm.getTabList();
          vm.dialogTablelabel = true;
          vm.form = sels;
          var ids = [];
          sels.forEach((item) => {
            ids.push(item.id);
          });
          vm.ids = ids;
        },
        getTabList: function () {
          var vm = this;
          $.ajax({
            url: "{:url('getTabList')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.tabData = res.data;
              }
            },
          });
        },

        //删除会员
        delMember(id) {
          var that = this;
          that
            .$confirm("确认删除该会员？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('delMember')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.code == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    that.getMemberList();
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },

        size_change(val) {
          this.limit = val;
          this.page = 1;
          this.getMemberList();
        },
        /* 切换页数 */
        current_change(val) {
          this.page = val;
          this.getMemberList();
        },
        getResetMember() {
          var vm = this;
          $.ajax({
            url: "{:url('Memberresettotal/getnum')}",
            data: {},
            type: "POST",
            success(res) {
              console.log(res);
              vm.resetNum = res.data.num;
            },
          });
        },
        toReset() {
          var vm = this;
          vm.promotionloading = true;
          $.ajax({
            url: "{:url('Memberresettotal/resettotal')}",
            data: {},
            type: "POST",
            success(res) {
              console.log(res);
              if (res.code == 1) {
                vm.getResetMember();
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
              vm.promotionloading = false;
            },
          });
        },
      },
      created: function () {
        this.getResetMember();
        this.getMemberList();
        this.getAdviserList();
      },
    });
  });
</script>
