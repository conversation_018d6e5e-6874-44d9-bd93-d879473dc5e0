<div class="layui-fluid">
  <div id="aftersale" v-cloak v-loading="ajaxloading">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div>售后服务</div>
      </div>
      <el-form :inline="true" ref="formRef" :model="searchForm" size="small">
        <el-form-item label="工单号" prop="workOrderNumber">
          <el-input v-model="searchForm.workOrderNumber"></el-input>
        </el-form-item>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="searchForm.deviceModel"></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="isFactory" label="店铺名" prop="shopName">
          <el-input v-model="searchForm.shopName"></el-input>
        </el-form-item> -->
        <el-form-item label="开单日期">
          <el-date-picker
            v-model="dates"
            type="daterange"
            size="small"
            range-separator="至"
            clearable
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status">
            <el-option label="全部" value=""></el-option>
            <el-option label="待审核" value="待审核"></el-option>
            <el-option label="已驳回" value="已驳回"></el-option>
            <el-option label="待寄回工厂" value="待寄回工厂"></el-option>
            <el-option label="寄回工厂中" value="寄回工厂中"></el-option>
            <el-option label="处理中" value="处理中"></el-option>
            <el-option label="寄回门店中" value="寄回门店中"></el-option>
            <el-option label="已结束" value="已结束"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-button
        v-if="!isFactory"
        type="primary"
        size="small"
        @click="handleCreate"
        >新建工单
      </el-button>
      <div>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="workOrderNumber" label="工单号">
            <template slot-scope="scope">
              <div>
                <span class="text-gray">工单号：</span>{{
                scope.row.workOrderNumber }}
              </div>
              <div>
                <span class="text-gray">开单日期：</span>{{
                scope.row.created_at?.split(" ")[0] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="isFactory"
            prop="shopName"
            label="店铺名"
          ></el-table-column>
          <el-table-column prop="content" label="工单内容">
            <template slot-scope="scope">
              <div>
                <span class="text-gray">设备型号：</span>{{
                scope.row.deviceModel }}
              </div>
              <div class="truncate">{{ scope.row.content }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="statusStr" label="状态">
            <template slot-scope="scope">
              <div
                class="f-status-box"
                style="
                  --f-c-size: 10px;
                  --fc-green: #008573;
                  --fc-gray: #d1d5db;
                "
                :style="'--f-color-w: clamp(0%, '+(scope.row.statusNum-1) / 5 * 100+'%, 100%)'"
              >
                <div>
                  <div class="relative flex justify-between items-center">
                    <div
                      class="absolute left-0 w-full rounded-full progress-bar"
                      style="height: 2px; top: calc(var(--f-c-size) / 2 - 1px)"
                    ></div>
                    <div
                      v-for="index in 6"
                      :key="index"
                      class="rounded-full relative z-1"
                      :class="getStatusColorClass(scope.row.status,index,scope.row.statusNum)"
                      style="width: var(--f-c-size); height: var(--f-c-size)"
                    ></div>
                  </div>
                </div>
                <div class="relative">
                  <div
                    class="f-afterSale-status-tag"
                    :style="{
                      left: ((scope.row.statusNum-1) / 5 * 100 <= 0 ?
                             'calc(var(--f-color-w) - 26px)' :
                             ((scope.row.statusNum-1) / 5 * 100 === 20 ?
                              'calc(var(--f-color-w) - 28px)' :
                              ((scope.row.statusNum-1) / 5 * 100 === 40 ?
                               'calc(var(--f-color-w) - 30px)' :
                               ((scope.row.statusNum-1) / 5 * 100 === 60 ?
                                'calc(var(--f-color-w) - 32px)' :
                                ((scope.row.statusNum-1) / 5 * 100 === 80 ?
                                 'calc(var(--f-color-w) - 34px)' :
                                 'calc(var(--f-color-w) - 36px)')))))
                    }"
                  >
                    {{ scope.row.status }}
                    <div class="f-afterSale-status-tag_arrow"></div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="timeCost" label="耗时">
            <template slot-scope="scope">
              <div
                v-if="scope.row.status !== '已结束' && scope.row.status !== '已驳回'"
              >
                <div>
                  <span class="text-gray">整体耗时：</span
                  >{{formatHour(scope.row.created_at)}}
                </div>
                <div>
                  <span class="text-gray"
                    >{{getCostTimeTypeStr(scope.row.status)}}：</span
                  >{{formatHour(scope.row.updated_at)}}
                </div>
              </div>
              <div v-else>
                <span class="text-gray">整体耗时：</span
                >{{getOverTimeCost(scope.row.created_at, scope.row.updated_at)}}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="操作" width="90">
            <template slot-scope="scope">
              <el-button type="text" @click="handleViewDetail(scope.row.id)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div
          class="block"
          style="margin-top: 20px; overflow: hidden"
          v-if="total>0"
        >
          <el-pagination
            style="float: right"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 30, 40,100]"
            :page-size="limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </el-card>
    <el-dialog
      width="720px"
      :title="detailTitle"
      :visible.sync="isShowDetailDialog"
      append-to-body
    >
      <div
        class="text-white bg-red-500 py-2 rounded-md mb-4 px-4"
        v-if="form.status === '已驳回'"
      >
        已被驳回
      </div>
      <el-steps
        v-if="!isCreate && form.status !== '已驳回'"
        class="mb-4 px-4"
        :active="activeStep"
        finish-status="success"
      >
        <el-step title="待审核"></el-step>
        <el-step title="待寄回工厂"></el-step>
        <el-step title="寄回工厂中"></el-step>
        <el-step title="处理中"></el-step>
        <el-step title="寄回门店中"></el-step>
        <el-step title="结束"></el-step>
      </el-steps>
      <el-form
        class="o-view-disabled"
        label-width="80px"
        size="small"
        :rules="isCreate ? rules : {}"
        ref="form"
        :model="form"
        :validate-on-rule-change="false"
        :show-message="isCreate"
      >
        <el-form-item
          v-if="isShowLogisticsInput"
          label="快递公司"
          prop="expressCompany"
        >
          <el-select
            :disabled="disabledLogisticsInput"
            v-model="form.expressCompany"
            placeholder="请选择快递公司"
          >
            <el-option
              v-for="item in logisticsCompanyList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="isShowLogisticsInput"
          label="快递单号"
          prop="expressNumber"
        >
          <el-input
            :disabled="disabledLogisticsInput"
            v-model="form.expressNumber"
          ></el-input>
        </el-form-item>
        <div
          class="py-4 px-8 border border-solid border-gray-200 rounded-md mb-4 overflow-y-auto"
          style="max-height: 200px"
          v-if="['寄回工厂中','寄回门店中'].includes(form.status)"
          v-loading="logisticsListLoading"
        >
          <ul class="f-ul space-y-2" v-if="form.logisticsList?.length > 0">
            <li
              v-for="(item, index) in form.logisticsList"
              :key="index"
              class="f-logistics-li"
            >
              <div class="">{{item.status}}</div>
              <div class="text-gray text-sm">{{item.time}}</div>
              <div class="text-gray text-sm">{{item.context}}</div>
            </li>
          </ul>
          <div class="text-gray text-sm" v-else>暂无物流信息</div>
        </div>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input :disabled="!isCreate" v-model="form.deviceModel"></el-input>
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input
            :disabled="!isCreate"
            maxlength="99"
            v-model="form.userName"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备编码" prop="deviceCode">
          <el-input :disabled="!isCreate" v-model="form.deviceCode"></el-input>
        </el-form-item>
        <el-form-item label="工单内容" prop="content">
          <el-input
            :disabled="!isCreate"
            type="textarea"
            :autosize="{ minRows: 2}"
            v-model="form.content"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input :disabled="!isCreate" v-model="form.phone"></el-input>
        </el-form-item>
        <el-form-item label="微信号" prop="wechat">
          <el-input :disabled="!isCreate" v-model="form.wechat"></el-input>
        </el-form-item>
      </el-form>
      <div class="text-gray-600 p-4 mt-4 mb-2">图片</div>
      <el-upload
        class="px-8"
        action="{:url('Aftersale/upload')}"
        list-type="picture-card"
        :disabled="!isCreate"
        :on-preview="handlePictureCardPreview"
        :on-remove="handleRemoveImg"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :before-upload="beforeUpload"
        :headers="{'X-Requested-With': 'XMLHttpRequest'}"
      >
        <i class="el-icon-plus"></i>
      </el-upload>
      <el-dialog :visible.sync="imgDialogVisible" :append-to-body="true">
        <img width="100%" :src="imgDialogImageUrl" alt="" />
      </el-dialog>
      <div slot="footer" class="flex justify-between">
        <div>
          <el-button
            v-if="!isFactory && !isCreate && !['已驳回','寄回门店中','已结束'].includes(form.status)"
            type="danger"
            @click="handleSetOver"
            >提前结束</el-button
          >
        </div>
        <div>
          <el-button
            v-if="isFactory && form.status === '待审核'"
            type="danger"
            @click="rejectAfterSale"
            >驳 回</el-button
          >
          <el-button :type="btnConfirmType" @click="handleConfirm"
            >{{btnConfirmText}}</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</div>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/util/enum.js?v=1.2.1"
></script>
<script>
  layui.use("index", function () {
    var admin = layui.admin;
    var view = layui.view;
    var $ = layui.$;
    new Vue({
      el: "#aftersale",
      data() {
        return {
          AfterSaleType: window.MyEnums.AfterSaleType,
          isFactory: {$isfactory},
          imgDialogImageUrl: "",
          imgDialogVisible: false,
          dates: [],
          detailTitle: "",
          searchForm: {
            workOrderNumber: "",
            deviceModel: "",
            status: "",
          },
          form: {
            deviceModel: "",
            userName: "",
            deviceCode: "",
            content: "",
            phone: "",
            wechat: "",
            id: "",
            expressCompany: "",
            expressNumber: "",
            status: "",
            logisticsList: [],
            images: [],
          },
          logisticsCompanyList: [],
          logisticsListLoading: false,
          rules: {
            deviceModel: [
              {required: true, message: "请输入设备型号", trigger: "blur"},
            ],
            deviceCode: [
              {required: true, message: "请输入设备编码", trigger: "blur"},
            ],
            content: [
              {required: true, message: "请输入工单内容", trigger: "blur"},
            ],
            phone: [
              {required: true, message: "请输入联系电话", trigger: "blur"},
              {
                pattern: /^1[3456789]\d{9}$/,
                message: "手机号格式不正确",
                trigger: "blur",
              },
            ],
            userName: [
              {required: true, message: "请输入用户姓名", trigger: "blur"},
            ],
            wechat: [
              {required: false, message: "请输入微信号", trigger: "blur"},
            ],
            expressCompany: [
              {required: true, message: "请输入快递公司", trigger: "blur"},
            ],
            expressNumber: [
              {required: true, message: "请输入快递单号", trigger: "blur"},
            ],
          },
          page: 1,
          limit: 10,
          total: 0,
          tableData: [],
          ajaxloading: false,
          isShowDetailDialog: false,
          isShowLogisticsInput: false,
          disabledLogisticsInput: false,
          isCreate: false,
          staffList: [],
          fileList: [],
          activeStep: 0,
          btnCancelText: "取 消",
          btnConfirmText: "确 定",
          btnCancelType: "default",
          btnConfirmType: "primary",
        };
      },
      methods: {
        getStatusColorClass(status, index, statusNum) {
          if (status === "已驳回" && index === 1) return "bg-red";
          // class="scope.row.status === '已驳回' & index === 1 ? 'bg-red' : index <= scope.row.statusNum ? 'bg-primary' : 'bg-default'"
          if (status === "已结束") return "bg-success";

          if (
            [
              "待审核",
              "待寄回工厂",
              "寄回工厂中",
              "处理中",
              "寄回门店中",
            ].includes(status)
          ) {
            // if (index <= statusNum) return "bg-primary";
            if (index <= statusNum) return "bg-success";
          }

          return "bg-default";
        },
        getCostTimeTypeStr(status) {
          switch (status) {
            case "待审核":
              return "提交工单至今";
            case "待寄回工厂":
              return "审核通过至今";
            case "寄回工厂中":
              return "填发货单至今";
            case "处理中":
              return "开始处理至今";
            case "寄回门店中":
              return "填发货单至今";
            default:
              return "";
          }
        },
        getLogisticsCompanyList() {
          var vm = this;
          $.ajax({
            url: "{:url('Aftersale/getLogistics')}",
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.logisticsCompanyList = res.data;
              }
            },
          });
        },
        formatHour(timeStr) {
          if (!timeStr) return "0小时";

          // 将时间字符串转换为时间戳
          let createTime;
          // 检查是否已经是毫秒时间戳
          if (typeof timeStr === "number") {
            createTime = timeStr;
          } else {
            // 否则当作时间字符串处理
            createTime = new Date(timeStr.replace(/-/g, "/")).getTime();
          }
          const now = new Date().getTime();

          if (isNaN(createTime)) return "格式错误";

          // 计算时间差（毫秒）- 对于未来时间使用绝对值计算
          let diffMs = Math.abs(now - createTime);

          // 转换为小时
          const hours = Math.floor(diffMs / (1000 * 60 * 60));

          const d = Math.floor(hours / 24);
          const h = hours % 24;

          if (d > 7) {
            return d + "天";
          } else if (d > 0) {
            return d + "天" + h + "小时";
          } else {
            return h + "小时";
          }
        },
        getOverTimeCost(created_at, updated_at) {
          const createTime = new Date(created_at.replace(/-/g, "/")).getTime();
          const updateTime = new Date(updated_at.replace(/-/g, "/")).getTime();
          const diffMs = Math.abs(updateTime - createTime);

          // 直接计算时间差，不通过formatHour方法
          const hours = Math.floor(diffMs / (1000 * 60 * 60));
          const d = Math.floor(hours / 24);
          const h = hours % 24;

          if (d > 7) {
            return d + "天";
          } else if (d > 0) {
            return d + "天" + h + "小时";
          } else {
            return h + "小时";
          }
        },
        validateForm() {
          let valid = false;
          if (!this.isCreate) {
            return true;
          }
          this.$refs.form.validate((isValid) => {
            valid = isValid;
          });
          if (!valid) {
            this.$message.error("请填写完整的表单信息");
            return false;
          }
          return valid;
        },
        handlePictureCardPreview(file) {
          this.imgDialogImageUrl = file.url;
          this.imgDialogVisible = true;
        },
        handleRemoveImg(file, fileList) {
          this.fileList = fileList;
          // 从form.images数组中移除被删除的图片
          if (
            Array.isArray(this.form.images) &&
            file.response &&
            file.response.data &&
            file.response.data.url
          ) {
            const index = this.form.images.indexOf(file.response.data.url);
            if (index !== -1) {
              this.form.images.splice(index, 1);
            }
          }
        },
        handleUploadSuccess(response, file, fileList) {
          if (response.code === 1) {
            this.fileList = fileList;
            // 将新上传的图片URL添加到图片数组中
            if (!Array.isArray(this.form.images)) {
              this.form.images = [];
            }
            this.form.images.push(response.data.url);
            this.$message.success("上传成功");
          } else {
            this.$message.error(response.msg || "上传失败");
          }
        },
        beforeUpload(file) {
          const isImage = file.type.startsWith("image/");
          const isLt2M = file.size / 1024 / 1024 < 2;

          if (!isImage) {
            this.$message.error("只能上传图片文件!");
            return false;
          }
          if (!isLt2M) {
            this.$message.error("图片大小不能超过 2MB!");
            return false;
          }
          return true;
        },
        formClear() {
          this.form = {
            deviceModel: "",
            userName: "",
            deviceCode: "",
            content: "",
            phone: "",
            wechat: "",
            id: "",
            status: "",
            expressCompany: "",
            expressNumber: "",
            logisticsList: [],
            images: [],
          };
          this.fileList = [];
        },
        handleCreate() {
          this.isCreate = true;
          this.detailTitle = "新增工单";
          this.formClear();
          this.btnConfirmType = "primary";
          this.btnConfirmText = "确 定";
          this.isShowLogisticsInput = false;
          this.isShowDetailDialog = true;
        },

        getLogisticsList(logisticsId, courier_num) {
          var vm = this;
          vm.logisticsListLoading = true;
          $.ajax({
            url: "{:url('Aftersale/ExpressData')}",
            type: "POST",
            data: {
              logisticsId,
              courier_num,
            },
            success: function (res) {
              if (res.code == 1) {
                vm.form.logisticsList = res.data.data;
              }
            },
            complete: function () {
              vm.logisticsListLoading = false;
            },
          });
        },

        getCompanyId(companyName) {
          return this.logisticsCompanyList.find(
            (item) => item.name == companyName
          ).id;
        },

        handleViewDetail(id) {
          var vm = this;
          vm.formClear();
          vm.isCreate = false;
          vm.isShowDetailDialog = true;
          vm.detailTitle = "详情";

          // 从tableData中查找对应id的数据
          const item = vm.tableData.find((item) => item.id == id);

          if (item) {
            // 直接使用表格中的数据，不再调用API
            vm.form = {
              id: item.id,
              deviceModel: item.deviceModel || "",
              userName: item.userName || "",
              deviceCode: item.deviceCode || "",
              content: item.content || "",
              phone: item.phone || "",
              wechat: item.wechat || "",
              expressCompany: item.expressCompany || "",
              expressNumber: item.expressNumber || "",
              status: item.status || "",
              logisticsList: [],
              images: [],
            };

            // 处理图片数据，将逗号分隔的字符串转为数组
            if (item.images) {
              if (typeof item.images === "string") {
                vm.form.images = item.images.split(",");
              } else if (Array.isArray(item.images)) {
                vm.form.images = item.images;
              }
            }

            // 如果有图片数据，设置fileList以显示已有图片
            if (vm.form.images && vm.form.images.length > 0) {
              vm.fileList = vm.form.images.map((url, index) => ({
                name: "图片" + (index + 1),
                url: url,
              }));
            }

            switch (vm.form.status) {
              case "已驳回":
                vm.activeStep = 0;

                vm.btnConfirmType = "default";
                vm.btnConfirmText = "确 定";
                vm.isShowLogisticsInput = false;
                break;
              case "待审核":
                vm.activeStep = 0;
                vm.isShowLogisticsInput = false;
                if (vm.isFactory) {
                  vm.btnCancelType = "danger";
                  vm.btnConfirmType = "success";
                  vm.btnConfirmText = "通 过";
                  vm.btnCancelText = "驳 回";
                } else {
                  vm.btnConfirmType = "default";
                  vm.btnConfirmText = "等待审核";
                }
                break;
              case "待寄回工厂":
                vm.activeStep = 1;
                if (vm.isFactory) {
                  vm.btnConfirmType = "default";
                  vm.btnConfirmText = "确 定";
                  vm.isShowLogisticsInput = false;
                } else {
                  vm.btnConfirmType = "primary";
                  vm.btnConfirmText = "发 货";
                  vm.isShowLogisticsInput = true;
                  vm.disabledLogisticsInput = false;
                }
                break;
              case "寄回工厂中":
                vm.activeStep = 2;
                vm.getLogisticsList(
                  vm.getCompanyId(vm.form.expressCompany),
                  vm.form.expressNumber
                );
                vm.isShowLogisticsInput = true;
                if (vm.isFactory) {
                  vm.btnConfirmType = "primary";
                  vm.btnConfirmText = "转为处理中";
                  vm.disabledLogisticsInput = true;
                } else {
                  vm.btnConfirmType = "danger";
                  vm.btnConfirmText = "修改快递信息";
                  vm.disabledLogisticsInput = false;
                }
                break;
              case "处理中":
                vm.activeStep = 3;
                vm.form.expressCompany = "";
                vm.form.expressNumber = "";
                if (vm.isFactory) {
                  vm.isShowLogisticsInput = true;
                  vm.btnConfirmType = "primary";
                  vm.btnConfirmText = "发 货";
                  vm.disabledLogisticsInput = false;
                } else {
                  vm.isShowLogisticsInput = false;
                  vm.btnConfirmType = "default";
                  vm.btnConfirmText = "确 定";
                }
                break;
              case "寄回门店中":
                vm.activeStep = 4;
                vm.getLogisticsList(
                  vm.getCompanyId(vm.form.expressCompany),
                  vm.form.expressNumber
                );
                vm.isShowLogisticsInput = true;
                if (vm.isFactory) {
                  vm.btnConfirmType = "danger";
                  vm.btnConfirmText = "修改快递信息";
                  vm.disabledLogisticsInput = false;
                } else {
                  vm.btnConfirmType = "danger";
                  vm.btnConfirmText = "收货并结束工单";
                  vm.disabledLogisticsInput = true;
                }
                break;
              case "已结束":
                // 大于5才能打勾5
                vm.activeStep = 6;

                vm.isShowLogisticsInput = false;
                vm.btnConfirmType = "default";
                vm.btnConfirmText = "确 定";
                break;
            }
          } else {
            vm.$message.error("找不到对应的工单数据");
            vm.closeDialog();
            return;
          }

          vm.detailTitle = `工单 - ${vm.form.status}`;
        },
        handleConfirm() {
          var vm = this;
          if (vm.isCreate) {
            // 确保表单验证通过
            if (!vm.validateForm()) {
              return;
            }

            // 确保images是数组格式
            if (!Array.isArray(vm.form.images)) {
              vm.form.images = vm.form.images ? [vm.form.images] : [];
            }

            // 准备提交的数据，字段名与API一致
            const submitData = {
              deviceModel: vm.form.deviceModel,
              user_name: vm.form.userName,
              device_code: vm.form.deviceCode,
              content: vm.form.content,
              phone: vm.form.phone,
              wechat: vm.form.wechat,
              images: vm.form.images,
            };

            $.ajax({
              url: "{:url('Aftersale/addAfterSale')}",
              data: submitData,
              type: "POST",
              success: function (res) {
                if (res.code == 1) {
                  vm.$message.success(res.msg);
                  vm.closeDialog();
                  vm.getTableList();
                } else {
                  vm.$message.error(res.msg);
                }
              },
            });
            return;
          }

          // 在发货或修改快递信息时验证表单
          if (
            (vm.form.status === "待寄回工厂" && !vm.isFactory) ||
            (vm.form.status === "处理中" && vm.isFactory) ||
            (vm.form.status === "寄回工厂中" && !vm.isFactory) ||
            (vm.form.status === "寄回门店中" && vm.isFactory)
          ) {
            if (!vm.validateForm()) {
              return;
            }
          }

          switch (vm.form.status) {
            case "已驳回":
              //  已驳回
              vm.rejectAfterSale();
              break;
            case "待审核":
              //  待审核
              if (vm.isFactory) {
                vm.passAfterSale();
              } else {
                vm.closeDialog();
              }
              break;
            case "待寄回工厂":
              //  待寄回工厂
              if (vm.isFactory) {
                vm.closeDialog();
              } else {
                vm.sendAfterSale();
              }
              break;
            case "寄回工厂中":
              //  寄回工厂中
              if (vm.isFactory) {
                // 转为处理中
                vm.setStatusToFixing();
              } else {
                // 修改快递信息
                vm.updateExpressInfo();
              }
              break;
            case "处理中":
              //  处理中
              if (vm.isFactory) {
                // 发 货
                vm.sendAfterSale();
              } else {
                vm.closeDialog();
              }
              break;
            case "寄回门店中":
              //  寄回门店中
              if (vm.isFactory) {
                // 修改快递信息
                vm.updateExpressInfo();
              } else {
                vm.handleSetOver();
              }
              break;
            case "已结束":
              //  已结束
              vm.closeDialog();
              break;
          }
        },
        closeDialog() {
          this.isShowDetailDialog = false;
          // this.formClear();
        },
        handleSearch() {
          this.page = 1;
          this.limit = 10;
          this.getTableList();
        },
        getTableList() {
          var vm = this;
          vm.ajaxloading = true;
          // 构建查询参数
          let params = {
            page: vm.page,
            limit: vm.limit,
            order_id: "",
            workOrderNumber: vm.searchForm.workOrderNumber || "",
            device_model: vm.searchForm.deviceModel || "",
            status: vm.searchForm.status || "",
            shopName: vm.searchForm.shopName || "",
          };

          // 处理日期范围
          if (vm.dates && vm.dates.length === 2) {
            params.start_date = vm.formatDate(vm.dates[0]);
            params.end_date = vm.formatDate(vm.dates[1]);
          }

          $.ajax({
            url: "{:url('Aftersale/getAfterSaleList')}",
            data: params,
            type: "POST",
            success: function (res) {
              vm.ajaxloading = false;
              if (res.code == 1) {
                vm.total = res.data.total;
                vm.page = res.data.current_page;
                vm.tableData = res.data.data.map((item) => {
                  // 数字状态值 - 用于进度条显示
                  let statusNum = 0;
                  switch (item.status) {
                    case "已驳回":
                      statusNum = 0;
                      break;
                    case "待审核":
                      statusNum = 1;
                      break;
                    case "待寄回工厂":
                      statusNum = 2;
                      break;
                    case "寄回工厂中":
                      statusNum = 3;
                      break;
                    case "处理中":
                      statusNum = 4;
                      break;
                    case "寄回门店中":
                      statusNum = 5;
                      break;
                    case "已结束":
                      statusNum = 6;
                      break;
                    default:
                      statusNum = 0;
                  }

                  return {
                    ...item,
                    statusNum: statusNum, // 用于进度条显示
                  };
                });

                // 处理每个记录的images字段，将字符串转为数组
                vm.tableData.forEach((item) => {
                  if (item.images && typeof item.images === "string") {
                    item.images = item.images.split(",");
                  }
                });
              } else {
                vm.$message.error(res.msg || "获取数据失败");
                vm.tableData = [];
                vm.total = 0;
              }
            },
            error: function () {
              vm.ajaxloading = false;
              vm.$message.error("网络错误，请稍后再试");
              vm.tableData = [];
              vm.total = 0;
            },
          });
        },
        rejectAfterSale() {
          var vm = this;
          this.$confirm("确定要驳回此工单吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/rejectAfterSale')}",
              type: "POST",
              data: {
                id: vm.form.id,
              },
              success: function () {
                vm.$message.success("已驳回");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },
        passAfterSale() {
          var vm = this;
          this.$confirm("确定要通过此工单吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/passAfterSale')}",
              type: "POST",
              data: {
                id: vm.form.id,
              },
              success: function () {
                vm.$message.success("已通过");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },

        handleSizeChange(val) {
          this.page = 1;
          this.limit = val;
          this.getTableList();
        },
        handleCurrentChange(val) {
          this.page = val;
          this.getTableList();
        },
        sendAfterSale() {
          var vm = this;

          // 验证快递信息
          if (!vm.form.expressCompany || !vm.form.expressNumber) {
            vm.$message.error("请填写完整的快递信息");
            return;
          }
          this.$confirm("确定要发货吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/sendAfterSale')}",
              type: "POST",
              data: {
                isToFactory: vm.isFactory ? 0 : 1,
                expressCompany: vm.form.expressCompany,
                expressNumber: vm.form.expressNumber,
                id: vm.form.id,
              },
              success: function () {
                vm.$message.success("已发货");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },
        handleSetOver() {
          var vm = this;
          this.$confirm("确定要结束此工单吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/setOver')}",
              type: "POST",
              data: {
                id: vm.form.id,
              },
              success: function () {
                vm.$message.success("工单已结束");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },
        updateExpressInfo() {
          var vm = this;

          // 验证快递信息
          if (!vm.form.expressCompany || !vm.form.expressNumber) {
            vm.$message.error("请填写完整的快递信息");
            return;
          }
          this.$confirm("确定要修改快递信息吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/updateExpressInfo')}",
              type: "POST",
              data: {
                expressCompany: vm.form.expressCompany,
                expressNumber: vm.form.expressNumber,
                id: vm.form.id,
                isToFactory: vm.isFactory ? 0 : 1,
              },
              success: function () {
                vm.$message.success("已修改快递信息");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },
        setStatusToFixing() {
          var vm = this;
          this.$confirm("确定要转为处理中吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            $.ajax({
              url: "{:url('Aftersale/setStatusToFixing')}",
              type: "POST",
              data: {
                id: vm.form.id,
              },
              success: function () {
                vm.$message.success("已转为处理中");
                vm.closeDialog();
                vm.getTableList();
              },
            });
          });
        },
        // 格式化日期
        formatDate(date) {
          return ff_util.formatEleMentPickDate(date);
        },
      },
      watch: {
        isShowDetailDialog: function (newVal) {
          if (!newVal) {
            this.formClear();
            // 重置表单验证规则
            if (this.$refs.form) {
              this.$refs.form.clearValidate();
            }
          }
        },
      },
      created: function () {
        this.getTableList();
        this.getLogisticsCompanyList();
      },
    });
  });
</script>
<style>
  .f-ul > li {
    list-style: disc;
  }

  .bg-default {
    background-color: var(--fc-gray);
  }

  .bg-success {
    background-color: var(--fc-green);
  }

  .progress-bar {
    background: linear-gradient(
      90deg,
      var(--fc-green) 0%,
      var(--fc-green) var(--f-color-w),
      var(--fc-gray) var(--f-color-w),
      var(--fc-gray) 100%
    );
  }

  .f-status-box {
    width: 100%;
    height: 50px;
    padding: 0 26px;
  }

  .f-afterSale-status-tag {
    width: fit-content;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0px 8px;
    background: #fff;
    position: absolute;
    top: 7px;
    white-space: nowrap;
  }

  .f-afterSale-status-tag_arrow {
    position: absolute;

    top: -8px;
    left: 20px;
  }

  .f-afterSale-status-tag_arrow::before {
    content: " ";
    position: absolute;
    top: 0px;
    left: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 8px solid #bdbdbd;
    z-index: 1;
  }

  .f-afterSale-status-tag_arrow::after {
    content: " ";
    position: absolute;
    top: 1px;
    left: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 8px solid #fff;
    z-index: 2;
  }

  .f-logistics-li {
    position: relative;
  }

  ul > .f-logistics-li:first-child::before {
    content: "";
    position: absolute;
    top: 0;
    left: -15px;
    width: 4px;
    height: 100%;
    background-color: #3e63dd;
  }
</style>
