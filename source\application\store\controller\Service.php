<?php

namespace app\store\controller;

use app\store\model\Classification as ClassificationModel;
use app\store\model\Createstore as CreatestoreModel;
use app\store\model\Label as LabelModel;
use app\store\model\Material as MaterialModel;
use app\store\model\Service as ServiceModel;
use app\store\model\Sku as SkuModel;
use app\store\model\Skuattr as SkuAttrModel;
use app\store\model\Skuattr;
use app\store\model\Skuval as SkuValModel;
use app\store\model\Stoserviec;
use app\store\model\Stoserviec as StoserviecModel;
use app\store\model\StoSkuAttr as StoSkuAttrModel;
use app\store\model\StoSkuAttr;
use app\api\controller\Xcx as XcxCtrl;
use think\Request;
use think\Db;

//<!--[meiye_10_13]-->
class Service extends Controller
{
    public function Index()
    {
        $user = $this->userinfo;
        if ($user) {
            $this->assign("user", $user);
        } else {
            $this->assign("user", '');
        }
        if(isset($user['user_group']) && $user['user_group']==3){
            $this->assign('isMerchant',1);
        }else{
            $this->assign('isMerchant',0);
        }
        //获取首页搜索的分类数据
        $ifi = new ClassificationModel;
        $ws['merchant_id'] = $user['merchantid'];
        $ws['status'] = 1;
        $classificationlist = $ifi->getlist($ws);
        $this->assign('classificationlist', $classificationlist);
        //获取首页搜索的标签数据
        $ifi = new LabelModel;
        $ws['merchant_id'] = $user['merchantid'];
        $labellist = $ifi->getlist($ws);
        $this->assign('labellist', $labellist);
        return $this->outPut();
    }

    //获取分类信息
    public function Getifi()
    {
        $user = $this->userinfo;
        $post = input("post.");
        $ifi = new ClassificationModel;
        $where['merchant_id'] = $user['merchantid'];
        $where['status'] = 1;
        $count = $ifi->getCount($where);
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $where['ord'] = "addtime desc";
        $classificationlist = $ifi->getPalist($where);
        foreach ($classificationlist as $k => &$v) {
            $v['addtime'] = date("Y-m-d H:i:s", $v['addtime']);
        }
        return array('code' => 0, 'data' => $classificationlist, 'count' => $count);
    }
    // 卡项部分有使用该接口，修改请注意
    //服务管理首页数据
    public function GetServiceList()
    {
        $user = $this->userinfo;
        $post = input('post.');
        $model = new ServiceModel;
        $where = array();
        if ($post['classification']) {
            $where['classification_id'] = $post['classification'];
        }
        if ($post['name']) {
            $where['service_name'] = array('like', "%" . $post['name'] . "%");
        }
        if (!empty($post['status'])) {
            $where['status'] = array(
                array("NEQ", 3),
                array("eq", $post['status']),
                "AND"
            );
        } else {
            $where['status'] = array("NEQ", 3);
        }
        $where['merchant_id'] = $user['merchantid'];
        $where['storeid'] =['in',[0,isset($post['storeid'])?$post['storeid']:0]];
        $count = $model->getCount($where);
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $where['ord'] = "addtime desc";
        $field = array(
            '*',
            'id as consumables'
        );
        $append = array(
            'img'
        );
        $extraData = array(
            'merchantid' => $user['merchantid']
        );
        $data = $model->setExtraData($extraData)->getList($where, $field, $append);
        $Stoserviecmodel = new StoserviecModel;
        foreach ($data as $k => &$v) {
            $we['merchant_id'] = $user['merchantid'];
            $we['store_id'] = $user['storeid'];
            $we['service_id'] = $v['id'];
            $serviceList = $Stoserviecmodel->getFind($we);
            if ($serviceList) {
                $v['is_Add_store'] = true;
            } else {
                $v['is_Add_store'] = false;
            }
            if ($v['shop_display'] == 1) {
                $v['shop_display'] = "是";
            } else {
                $v['shop_display'] = "否";
            }
            $v['addtime'] = date("Y-m-d H:i:s", $v['addtime']);
            $countWe['merchant_id'] = $user['merchantid'];
            $countWe['service_id'] = $v['id'];
            if(isset($post['storeStatus']) && $post['storeStatus']==1){
				$countWe['status'] = 1;
			}
            $servicecount = $Stoserviecmodel->getSellStoreList($countWe);
            $v['selling_stores'] = $servicecount ? $servicecount : array();//在售门店
            //获取首页搜索的分类数据
            $ifi = new ClassificationModel;
            $ws['id'] = $v['classification_id'];
            $classificationlist = $ifi->getFind($ws);
            $v['classification_id'] = $classificationlist['classification_name'];
            $ifi = new LabelModel;
            $ws['id'] = array('in', $v['label_id']);
            $grolist = $ifi->getlist($ws);
            $name = '';
            foreach ($grolist as $k => &$val) {
                $name .= $val['label_name'] . ",";
            }
            $v['label_id'] = substr($name, 0, -1);
            $Skuattr = new SkuAttrModel;
            $Attr['ser_good_id'] = $v['id'];
            $Attr['merchant_id'] = $user['merchantid'];
            $min = "price";
            $pricemin = $Skuattr->getminList($Attr, $min);
            $pricemax = $Skuattr->getmaxList($Attr, $min);
            $price = '';
            if ($pricemin && $pricemax) {
				$pricemin = $pricemin?$pricemin:0;
				$pricemax = $pricemax?$pricemax:0;
                $price = number_format(($pricemin / 100), 2, ".", "") . " - " . number_format(($pricemax / 100), 2, ".", "");
            }
            $v['price'] = $price ? "￥" . $price : "￥" . number_format(($v['price'] / 100), 2, ".", "");
        }
        if ($data) {
            return array('code' => 0, 'count' => (int)$count, 'data' => $data);
        } else {
            return array('code' => 0, 'count' => 0, 'data' => array());
        }
    }

    //修改上架下架状态
    public function EditStatus()
    {
        $user = $this->userinfo;
        $id = input("post.id");
        $status = input("post.status");
        $model = new ServiceModel;
        $where['id'] = $id;
        $Ed['status'] = $status;
        $StoserviecModel = new StoserviecModel;
        $We['merchant_id'] = $user['merchantid'];
        $We['service_id'] = $id;
        $Eds['status'] = 2;
        Db::startTrans();
        try {
            $model->updates($where, $Ed);
            if($status==2){
                $StoserviecModel->updates($We, $Eds);
            }
            // 提交事务
            Db::commit();
            return $this->ajaxSuccess("修改成功");
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->ajaxFail("修改失败");
        }
    }

    //删除
    public function Del()
    {
        $id = input('post.id');
        $model = new ServiceModel;
        $where['id'] = $id;
        $find = $model->getFind($where);
        if ($find['status'] == 1) {
            $res['status'] = 0;
            $res['msg'] = "服务开启状态，禁止删除！";
            return $res;
        }
        $Ed['status'] = 3;
        $Del = $model->updates($where, $Ed);
        if ($Del) {
            $res['status'] = 1;
            $res['msg'] = '删除成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = "删除失败";
        }
        return $res;
    }

    //数据修改添加
    public function servicead()
    {
        //编辑传的id
        $id = input('get.id');
        $fromStore = input('get.fromStore');
        if ($id) {
            $this->assign('id', $id);
        } else {
            $this->assign('id', '');
        }
        if ($fromStore) {
            $this->assign('fromStore', $fromStore);
        } else {
            $this->assign('fromStore', '');
        }
        return $this->outPut("ServiceAd");
    }

    public function getifiList()
    {
        $user = $this->userinfo;
        //获取首页搜索的分类数据
        $ifi = new ClassificationModel;
        $ws['merchant_id'] = $user['merchantid'];
        $ws['status'] = array("neq", 2);
        $classificationlist = $ifi->getlist($ws);
        if ($classificationlist) {
            return $this->ajaxSuccess($classificationlist, "ok");
        } else {
            return $this->ajaxFail("失败");
        }
    }

    public function geLabList()
    {
        $user = $this->userinfo;
        //获取首页搜索的标签数据
        $Lab = new LabelModel;
        $wss['merchant_id'] = $user['merchantid'];
        $wss['status'] = array("neq", 2);
        $labellist = $Lab->getlist($wss);
        if ($labellist) {
            return $this->ajaxSuccess($labellist, "ok");
        } else {
            return $this->ajaxFail("失败");
        }
    }

    //查询修改的信息
    public function getFind()
    {
        $id = input('post.id');
        $user = $this->userinfo;
        $where['id'] = $id;
		$where['merchant_id'] = $user['merchantid'];
        $model = new ServiceModel;
        $List = $model->getFind($where);
        if ($List) {
            $positionids = explode(',', $List['label_id']);
            foreach ($positionids as $val) {
                $post[] = (int)$val;
            }
            $List['content'] = htmlspecialchars_decode($List['content']);
            $List['label_id'] = $post;
            $List['price'] = $List['price'] ? $List['price'] / 100 : '';
            $List['duration'] = (int)$List['duration'];
            $List['shop_display'] = (int)$List['shop_display'];
            $List['door_service'] = (int)$List['door_service'];
            $List['pre_door'] = (int)$List['pre_door'];
            $List['pre_door_mode'] = (int)$List['pre_door_mode'];
            $List['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] * 100 : '';
            $List['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] / 100 : '';
            $List['pro_shop'] = (int)$List['pro_shop'];
            $List['pro_shop_mode'] = (int)$List['pro_shop_mode'];
            $List['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] * 100 : '';
            $List['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] / 100 : '';
            if ($List['material_id']) {
                $imgUrl = array();
                $imaid = explode(',', $List['material_id']);
                foreach ($imaid as $k => &$v) {
                    $ws['id'] = $v;
                    $matModel = new MaterialModel;
                    $imgFile = $matModel->getFind($ws);
                    if($imgFile){
                        $imgUrl[] = $imgFile;
                    }
                }
                $List['imgUrl'] = $imgUrl;
            }else{
                $List['imgUrl'] = array();
            }

            if ($List['video_id']) {
                $wes['id'] = $List['video_id'];
                $List['radioUrl'] = $matModel->getPageList($wes, 1, 10, 'id,urltype,url,cover as poster');
            } else {
                $List['radioUrl'] = array();
            }
            $Skumodel = new SkuModel;
            $skuWe['ser_good_id'] = $List['id'];
            $skuList = $Skumodel->getList($skuWe);
            $Skuname = array();
            if ($skuList) {
                $attribute_s = array();
                foreach ($skuList as $k => &$v) {
                    $attribute_s[$k]['attribute'] = $v['name'];
                    $Skuname[] = $v['name'];
                    $attribute_s[$k]['labclo'] = false;
                    $SkuVwe['sku_id'] = $v['id'];
                    $skuValmodel = new SkuValModel;
                    $SkuVal = $skuValmodel->getList($SkuVwe);
                    foreach ($SkuVal as $key => $val) {
                        $matModel = new MaterialModel;
                        $imgwe['id'] = $val['material_id'];
                        $img = $matModel->getFind($imgwe);
                        $attribute_s[$k]['SpecVal'][$key]['id'] = $val['id'];
                        $attribute_s[$k]['SpecVal'][$key]['text'] = $val['name'];
                        $attribute_s[$k]['SpecVal'][$key]['img'] = $img ? $img : '';
                    }
                }
                $List['attribute_s'] = $attribute_s ? $attribute_s : '';//规格数据
            }
            $attrTable = array();
            $attrTable['name'] = $Skuname ? $Skuname : '';
            $SkuAttrModel = new SkuAttrModel;
            $sAttr['ser_good_id'] = $List['id'];
            $sAttr['merchant_id'] = $user['merchantid'];
            $SkuAttr = $SkuAttrModel->getList($sAttr);
            $price = array();
            $barcode = array();
            $SkuAttrId = array();
            foreach ($SkuAttr as $k => $v) {
                $val = explode(',', $v['sku']);
                $attrTable['val'][$k] = $val;
                $price[$k] = $v['price'] / 100;
                $barcode[$k] = $v['barcode'];
                $SkuAttrId[$k] = $v['id'];
            }
            $List['attrTable'] = $attrTable ? $attrTable : '';
            $List['prices'] = $price ? $price : '';
            $List['barcode'] = $barcode ? $barcode : '';
            $List['skuattrid'] = $SkuAttrId;
        }
        if ($List) {
            $res['status'] = 1;
            $res['data'] = $List;
        } else {
            $res['status'] = 0;
        }
        return $res;
    }

    //执行添加修改   总部
    public function AdService()
    {
        $user = session('userinfo');
        $post = input('post.');
        $model = new ServiceModel;
        $Skumodel = new SkuModel;
        $skuValmodel = new SkuValModel;
        $SkuAttr = new SkuAttrModel;
        $labelid = '';
        //标签id拼接
        // if (is_array($post['label_id'])) {
        //     foreach ($post['label_id'] as $k => &$v) {
        //         $labelid .= $v . ",";
        //     }
        //     $labelid = substr($labelid, 0, -1);//标签id拼接
        // }
        $img_id = '';
        if ($post['imgUrl']) {
            foreach ($post['imgUrl'] as $k => &$v) {
                $img_id .= $v['id'] . ",";
            }
            $img_id = substr($img_id, 0, -1);//标签id拼接
        }
        $video_id = '';
        if (isset($post['radioUrl'])) {
            foreach ($post['radioUrl'] as $k => &$v) {
                $video_id = $v['id'];
            }
        }
        if (isset($post['text'])) {
            $text = $post['text'];
        } else {
            $text = '';
        }
        if ($post['AddGImgs'] == "true") {
            $is_sku_img = 1;
        } else {
            $is_sku_img = 2;
        }
        $data['service_name'] = $post['service_name'];//服务名称
        $data['bar_code'] = $post['bar_code'];//条形码
        $data['classification_id'] = $post['classification_id'];//分类id
        // $data['label_id'] = $labelid;//标签id，多选时已英文逗号分隔
        $data['price'] = $post['price'] ? $post['price'] * 100 : '';//售价以分为单位
        $data['price_tag'] = $post['price_tag'];//价格标签
        $data['duration'] = $post['duration'];//服务时长
        $data['shop_display'] = $post['shop_display'];//网店展示
        $data['door_service'] = $post['door_service'];//上门服务
        $data['pre_door'] = $post['pre_door'] ? $post['pre_door'] : '';//预约上门
        $data['pre_door_mode'] = $post['pre_door_mode'] ? $post['pre_door_mode'] : '';//上门支付
        $data['door_percentage'] = $post['door_percentage'] ? $post['door_percentage'] / 100 : 0;//上门百分比支付
        $data['door_fixed_amount'] = $post['door_fixed_amount'] ? $post['door_fixed_amount'] * 100 : 0;//上门固定金额支付
        $data['pro_shop'] = $post['pro_shop'];//预约到店
        $data['pro_shop_mode'] = $post['pro_shop_mode'] ? $post['pro_shop_mode'] : '';//到店支付
        $data['shop_percentage'] = $post['shop_percentage'] ? $post['shop_percentage'] / 100 : 0;//到店百分比支付
        $data['shop_fixed_amount'] = $post['shop_fixed_amount'] ? $post['shop_fixed_amount'] * 100 : 0;//到店固定金额支付
        $data['material_id'] = $img_id;//图片表id
        $data['video_id'] = $video_id ? $video_id : 0;//视频id
        $data['content'] = $text;
        $data['adjust_price_id'] = isset($post['adjust_price_id']) ? $post['adjust_price_id'] : 0;
        $nameMap = array(
            'merchant_id' => $user['merchantid'],
            'service_name' => $post['service_name'],
            'status' => array('in', '1,2')
        );
        $one = $model->getFind($nameMap, 'id');
        if ($one && $one['id'] && $post['id'] != $one['id']) {
            return $this->ajaxFail("操作失败，服务名称重复");
        }
        $nameMap = array(
            'merchant_id' => $user['merchantid'],
            'bar_code' => $post['bar_code'],
            'status' => array('in', '1,2')
        );
        $one = $model->getFind($nameMap, 'id');
        if ($one && $one['id'] && $post['id'] != $one['id']) {
            return $this->ajaxFail("操作失败，服务条码重复");
        }
        // 启动事务
        Db::startTrans();
        try {
            // 编辑服务
            if ($post['id'] != '') {
                $we['ser_good_id'] = $post['id'];
                $we['merchant_id'] = $user['merchantid'];
                $where['id'] = $post['id'];
                $where['merchant_id'] = $user['merchantid'];
                $Sta = $model->updates($where, $data);
                if($Sta===false){
                    Db::rollback();
                    return $this->ajaxFail("操作失败".$model->error);
                }
                if (isset($post['attribute_s'])) {
                    foreach ($post['attribute_s'] as $k => &$v) {
                        foreach ($v['SpecVal'] as $key => $val) {
                            unset($SkuVal);
                            $skuattr['id'] = $val['id'];
                            $skuattr['merchant_id'] = $user['merchantid'];
                            if (!empty($val['img'])) {
                                $SkuVal['material_id'] = $val['img']['id'];
                                $SkuValid = $skuValmodel->updates($skuattr, $SkuVal);
                                if($SkuValid===false){
                                    Db::rollback();
                                    return $this->ajaxFail("操作失败".$skuValmodel->error);
                                }
                            }
                        }
                    }
                    $StoSkuAttrModel = new StoSkuAttrModel;
                    foreach ($post['prices'] as $k => $v) {
                        $skuattrwe['id'] = $post['skuattrid'][$k];
                        $attr['price'] = $post['prices'][$k] * 100;
                        $Attrs = $SkuAttr->updates($skuattrwe, $attr);
                        if($Attrs===false){
                            Db::rollback();
                            return $this->ajaxFail("操作失败".$SkuAttr->error);
                        }
                        $StoSkuAttrMap = array(
                            'isset'=>2,
                            'sku_attr_id'=>$skuattrwe['id'],
                            'merchant_id'=>$user['merchantid']
                        );
                        $res = $StoSkuAttrModel->updates($StoSkuAttrMap,array('price'=>$attr['price']));
                        if($res===false){
                            Db::rollback();
                            return $this->ajaxFail("操作失败".$StoSkuAttrModel->error);
                        }
                    }
                }
                // 更新店铺的服务数据
                $storeServiceMap = array(
                    'merchant_id'=>$user['merchantid'],
                    'isset'=>2,
                    'service_id'=>$post['id'],
                );
                $StoserviecModle = new StoserviecModel;
                $storeServiceData = array(
                    'service_name'=>$data['service_name'],
                    'material_id'=>$data['material_id'],
                    'price'=>$data['price'],
                    'shop_display'=>$data['shop_display'],
                    'door_service'=>$data['door_service'],
                    'pre_door'=>$data['pre_door'],
                    'pre_door_mode'=>$data['pre_door_mode'],
                    'door_percentage'=>$data['door_percentage'],
                    'door_fixed_amount'=>$data['door_fixed_amount'],
                    'pro_shop'=>$data['pro_shop'],
                    'pro_shop_mode'=>$data['pro_shop_mode'],
                    'shop_percentage'=>$data['shop_percentage'],
                    'shop_fixed_amount'=>$data['shop_fixed_amount'],
                );
                $res =  $StoserviecModle->updates($storeServiceMap,$storeServiceData);
                if($res===false){
                    Db::rollback();
                    return $this->ajaxFail("操作失败".$StoserviecModle->error);
                }
            } else {
                // 新增服务
                $data['merchant_id'] = $user['merchantid'];//商家id
                $data['addtime'] = time();//创建时间
                $data['is_sku_img'] = $is_sku_img;
                if (isset($post['attribute_s'])) {
                    $data['is_sku'] = 1;
                } else {
                    $data['is_sku'] = 2;
                }
                if ($user['storeid']!=0) {
                    $data['status'] = 1;
                    $data['storeid'] = $user['storeid'];
                 }
                
                $Sta = $model->add($data);
                if($Sta===false || !$Sta){
                    Db::rollback();
                    return $this->ajaxFail("操作失败".$model->error);
                }
                if ($Sta) {
                    if (isset($post['attribute_s'])) {
                        foreach ($post['attribute_s'] as $k => &$v) {
                            $Sku["merchant_id"] = $user['merchantid'];
                            $Sku["ser_good_id"] = $Sta;
                            $Sku["name"] = $v['attribute'];
                            $Sku["addtime"] = time();
                            $Skuid = $Skumodel->add($Sku);
                            if(!$Skuid){
                                Db::rollback();
                                return $this->ajaxFail("操作失败".$Skumodel->error);
                            }
                            if ($Skuid) {
                                foreach ($v['SpecVal'] as $key => $val) {
                                    unset($SkuVal);
                                    $SkuVal['sku_id'] = $Skuid;
                                    $SkuVal['merchant_id'] = $user['merchantid'];;
                                    $SkuVal['ser_good_id'] = $Sta;
                                    $SkuVal['name'] = $val['text'];
                                    $SkuVal['addtime'] = time();
                                    if (!empty($val['img']) && $post['AddGImgs'] == true) {
                                        $SkuVal['material_id'] = $val['img']['id'];
                                    }
                                    $SkuValid = $skuValmodel->add($SkuVal);
                                    if(!$SkuValid){
                                        Db::rollback();
                                        return $this->ajaxFail("操作失败".$skuValmodel->error);
                                    }
                                }
                            }
                        }
                        foreach ($post['sxv'] as $k => $v) {
                            $sku = substr($v, 0, -1);
                            $wess['name'] = array("in", $sku);
                            $wess['merchant_id'] = $user['merchantid'];
                            $wess['ser_good_id'] = $Sta; // 服务标识
                            $SkuValList = $skuValmodel->getList($wess);
                            $sku_val_id = '';
                            foreach ($SkuValList as $kay => $val) {
                                $sku_val_id .= $val['id'] . ",";
                            }
                            $attr['sku_val_id'] = substr($sku_val_id, 0, -1);
                            $attr['ser_good_id'] = $Sta;
                            $attr['merchant_id'] = $user['merchantid'];
                            $attr['sku'] = $sku;
                            $attr['price'] = $post['prices'][$k] * 100;
                            $attr['barcode'] = $post['barcode'][$k];
                            $attr['addtime'] = time();
                            $Attrs = $SkuAttr->add($attr);
                            if(!$Attrs){
                                Db::rollback();
                                return $this->ajaxFail("操作失败".$SkuAttr->error);
                            }
                        }
                    }
                }
                if ($user['storeid']!=0) {
                   $this->Auto_AddstoSer($user['storeid'],$Sta);
                }
            }
            //提交事务
            Db::commit();
            return $this->ajaxSuccess("操作成功");
        } catch (\Exception $e) {
            $this->error = $e->getMessage().$e->getLine();
            Db::rollback();
            return $this->ajaxFail("操作失败".$this->error);
        }
    }

    //执行分类添加
    public function Adifi()
    {
        $post = input('post.');
        $user = session('userinfo');
        $ifimodel = new ClassificationModel;
        if ($post['id']) {
            $we['id'] = array('neq', $post['id']);
        }
        $we['merchant_id'] = $user['merchantid'];
        $we['classification_name'] = $post['name'];
        $we['status'] = 1;
        $ifi = $ifimodel->getFind($we);
        if ($ifi) {
            $res['status'] = 0;
            $res['msg'] = '该分类已存在';
            return $res;
        }
        if ($post['id']) {
            $where['id'] = $post['id'];
            $Ed['classification_name'] = $post['name'];
            $edit = $ifimodel->updates($where, $Ed);
            if ($edit) {
                $res['status'] = 1;
                $res['msg'] = "保存成功";
            } else {
                $res['status'] = 0;
                $res['msg'] = "保存失败";
            }
        } else {

            $where['merchant_id'] = $user['merchantid'];
            $where['classification_name'] = $post['name'];
            $where['addtime'] = time();
            $Ad = $ifimodel->add($where);
            if ($Ad) {
                $res['status'] = 1;
                $res['msg'] = "保存成功";
            } else {
                $res['status'] = 0;
                $res['msg'] = "保存失败";
            }
        }
        return $res;
    }

    //删除分类
    public function Delifi()
    {
        $id = input("post.id");
        $model = new ServiceModel;
        $where['classification_id'] = $id;
        $find = $model->getFind($where);
        if ($find['status'] == 1) {
            $res['status'] = 0;
            $res['msg'] = "使用中，禁止删除！";
            return $res;
        }
        $ifimodel = new ClassificationModel;
        $ws['id'] = $id;
        $Ed['status'] = 2;
        $Del = $ifimodel->updates($ws, $Ed);
        if ($Del) {
            $res['status'] = 1;
            $res['msg'] = '删除成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = "删除失败";
        }
        return $res;
    }

    //获取标签管理数据
    public function Getlab()
    {
        $user = $this->userinfo;
        $post = input("post.");
        $ifi = new LabelModel;
        $where['merchant_id'] = $user['merchantid'];
        $where['status'] = 1;
        $count = $ifi->getCount($where);
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $where['ord'] = "addtime desc";
        $label = $ifi->getPalist($where);
        foreach ($label as $k => &$v) {
            $v['addtime'] = date("Y-m-d H:i:s", $v['addtime']);
        }
        return array('code' => 0, 'data' => $label, 'count' => $count);
    }

    //执行分类添加
    public function Adlab()
    {
        $post = input('post.');
        $user = session('userinfo');
        $labmodel = new LabelModel;
        if ($post['id']) {
            $we['id'] = array('neq', $post['id']);
        }
        $we['merchant_id'] = $user['merchantid'];
        $we['label_name'] = $post['name'];
        $we['status'] = 1;
        $ifi = $labmodel->getFind($we);
        if ($ifi) {
            $res['status'] = 0;
            $res['msg'] = '该标签已存在';
            return $res;
        }
        if ($post['id']) {
            $where['id'] = $post['id'];
            $Ed['label_name'] = $post['name'];
            $edit = $labmodel->updates($where, $Ed);
            if ($edit) {
                $res['status'] = 1;
                $res['msg'] = "保存成功";
            } else {
                $res['status'] = 0;
                $res['msg'] = "保存失败";
            }
        } else {
            $where['merchant_id'] = $user['merchantid'];
            $where['label_name'] = $post['name'];
            $where['addtime'] = time();
            $Ad = $labmodel->add($where);
            if ($Ad) {
                $res['status'] = 1;
                $res['msg'] = "保存成功";
            } else {
                $res['status'] = 0;
                $res['msg'] = "保存失败";
            }
        }
        return $res;
    }

    //删除标签
    public function Dellab()
    {
        $id = input("post.id");
        $model = new ServiceModel;
        $where['label_id'] = array(
            array("like", "%" . $id . "%"),
            array("like", $id . "%"),
            array("like", "%" . $id),
            "OR"
        );
        $find = $model->getFind($where);
        if ($find['status'] == 1) {
            $res['status'] = 0;
            $res['msg'] = "使用中，禁止删除！";
            return $res;
        }
        $ifimodel = new LabelModel;
        $ws['id'] = $id;
        $Ed['status'] = 2;
        $Del = $ifimodel->updates($ws, $Ed);
        if ($Del) {
            $res['status'] = 1;
            $res['msg'] = '删除成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = "删除失败";
        }
        return $res;
    }

    //门店信息
    public function GetStoreList()
    {
        $CreatestoreModel = new CreatestoreModel;
        $user = $this->userinfo;
        $where['merchantid'] = $user['merchantid'];
        $where['status'] = array("neq", 3);
        $storeList = $CreatestoreModel->getList($where);
        if ($storeList) {
            return $this->ajaxSuccess($storeList, 'ok');
        } else {
            $msg = "暂无数据";
            return $this->ajaxFail($msg);
        }
    }

    //获取门店服务
    public function GetStoServiceList()
    {
        $user = $this->userinfo;
        $storeid = $user['storeid'];
        $post = input("post.");
        if (!empty($post['storeid'])) {
            $where['stser.store_id'] = $post['storeid'];
            $storeid = $post['storeid'];
        }
        if (!empty($post['status'])) {
            $where['stser.status'] = $post['status'];
        }
        if (!empty($post['Storeclassification'])) {
            $where['ser.classification_id'] = $post['Storeclassification'];
        }
        $ServiceModel = new ServiceModel;
        $StoSkuAttrModel = new StoSkuAttrModel;
        $where['stser.merchant_id'] = $user['merchantid'];
        $where['ser.status'] = array("in", array(1, 2));
        if (isset($post['name']) && $post['name']) {
            $name = $post['name'];
            $where['_string'] = "(ser.service_name LIKE '%{$name}%') OR (stser.service_name LIKE '%{$name}%')";
        }
        if(isset($post['isSellOnLine']) && $post['isSellOnLine']==1){
            $where['stser.shop_display'] = 1;
            $where['ser.status'] = 1;
            $where['stser.status'] = 1;
        }
        $isSkuService = 0;
        if (isset($post['isSkuService']) && $post['isSkuService'] == 2) {
            // 非规格服务
            $isSkuService = 2;
            $where['ser.is_sku'] = 2;
            $count = $ServiceModel->stoGetCount($where);
        } else if (isset($post['isSkuService']) && $post['isSkuService'] == 1) {
            // 规格服务
            $where['ser.is_sku'] = 1;
            $isSkuService = 1;
            $count = $ServiceModel->stoGetCount($where, $isSkuService);
        } else {
            //所有服务
            $isSkuService = 0;
            $count = $ServiceModel->stoGetCount($where);
        }
        $pa['limit'] = $post['limit'];
        $pa['page'] = $post['page'];
        $pa['ord'] = "stser.sort desc,stser.addtime desc";
        if ($isSkuService == 0) {
            $field = array(
                'ser.storeid as form',
                'ser.classification_id',
                'ser.label_id',
                'ser.duration',
                'ser.service_name',
                'ser.service_name as originalName',
                'stser.*',
                'stser.service_id as consumables',
                'ser.bar_code'
            );
            if(isset($post['isRecommend']) && $post['isRecommend']==1){
                $field = array(
                    'ser.storeid as form',
                    'ser.service_name',
                    'ser.service_name as originalName',
                    'stser.service_name',
                    'stser.id',
                    'stser.service_id',
                    'stser.material_id',
                    'stser.shop_display',
                    'stser.status',
                    'stser.is_sku',
                    'stser.price',
                    'stser.store_id',
                    'ser.classification_id',
                    'ser.bar_code',
                    'ser.status as originalStatus',
                );
            }
        } else if ($isSkuService == 1) {
            // 规格
            $field = array(
                'ser.storeid as form',
                'ser.classification_id',
                'ser.label_id',
                'ser.duration',
                'ser.service_name',
                'ser.service_name as originalName',
                'stser.*',
//                'stser.service_id as consumables',
                'ser.bar_code',
                'stsku.price',
                'stsku.sku_attr_id',
                'sku.id as sku_id',
                'sku.barcode as bar_code',
                'sku.sku',
                'stser.service_id as consumablesData'
            );
        } else if ($isSkuService == 2) {
            // 规格
            $field = array(
                'ser.storeid as form',
                'ser.classification_id',
                'ser.label_id',
                'ser.duration',
                'ser.service_name',
                'ser.service_name as originalName',
                'stser.*',
//                'stser.service_id as consumables',
                'ser.bar_code',
                'stser.service_id as consumablesData'
            );
        }
        $extraData = array(
            'merchantid' => $user['merchantid'],
            'storeid'=>$storeid
        );
        $field[] = 'stser.sort';
        $stoservicList = $ServiceModel->setExtraData($extraData)->stoGetList($where, $pa, $field, $isSkuService);
        if (is_object($stoservicList)) {
            $stoservicList = $stoservicList->toArray();
        }
        $ificlass = new ClassificationModel;
        $ifi = new LabelModel;
        foreach ($stoservicList as $k => &$v) {
            if ($v['shop_display'] == 1) {
                $v['shop_display'] = "是";
            } else {
                $v['shop_display'] = "否";
            }
            if ($v['status'] == 1) {
                $v['state'] = "上架";
            } else {
                $v['state'] = "下架";
            }
            if ($v['status'] == 1) {
                $v['sta'] = true;
            } else {
                $v['sta'] = false;
            }
            if ($v['form'] == $storeid) {
                $v['form_store'] = 1;
            } else {
                $v['form_store'] = 0;
            }
            //获取首页搜索的分类数据
            if(isset($v['classification_id'])){
                $ws['id'] = $v['classification_id'];
                $classificationlist = $ificlass->getFind($ws);
                $v['classification_id'] = $classificationlist['classification_name'];
            }
            if ($isSkuService == 0) {
                if(isset($v['label_id'])){
                    $ws['id'] = array('in', $v['label_id']);
                    $grolist = $ifi->getlist($ws);
                    $name = '';
                    foreach ($grolist as $k => &$val) {
                        $name .= $val['label_name'] . ",";
                    }
                    $v['label_id'] = substr($name, 0, -1);
                }
                if ($v['is_sku'] == 2) {
                    $v['price'] = $v['price']?$v['price']:0;
                    $v['price'] = "￥" . number_format($v['price'] / 100, 2, '.', '');
                } else {
                    $we['service_id'] = $v['service_id'];
                    $we['store_id'] = $v['store_id'];
                    $min = "price";
                    $minPrice = $StoSkuAttrModel->getminList($we, $min);
                    $maxPrice = $StoSkuAttrModel->getmaxList($we, $min);
					$minPrice = $minPrice?$minPrice:0;
					$maxPrice = $maxPrice?$maxPrice:0;
                    $v['price'] = "￥" . number_format($minPrice / 100, 2, '.', '') . " - " . number_format($maxPrice / 100, 2, '.', '');
                }
            } else {
                $v['price'] = $v['price']?$v['price']:0;
                $v['price'] = "￥" . number_format($v['price'] / 100, 2, '.', '');
            }
        }
        return $this->ajaxTable($stoservicList, $count);
    }

    //门店服务详情
    public function servicedetails()
    {
        $id = input("get.id");
        if ($id) {
            $this->assign("id", $id);
        } else {
            $this->assign("id", '');
        }
        return $this->outPut("ServiceDetails");
    }

    //查询修改的信息
    public function getStoSerFind()
    {
        $id = input('post.id');
        $user = session('userinfo');
        $where['stser.id'] = $id;
        $field = array(
            "stser.*",
            "ser.bar_code",
            "ser.classification_id",
            "ser.label_id",
            "ser.price_tag",
            "ser.video_id",
            "ser.duration",
            "ser.content",
            "ser.adjust_price_id",
            "ser.price as archivePrice"
        );
        $model = new ServiceModel;
        $List = $model->stoSerList($where, $field);
        if ($List) {
            if (is_object($List)) {
                $List = $List->toArray();
            }
            if ($List['archivePrice']) {
                $List['archivePrice'] = number_format(($List['archivePrice'] / 100), 2, '.', '');
            } else {
                $List['archivePrice'] = '';
            }
            $List['content'] = htmlspecialchars_decode($List['content']);
            $ifi = new ClassificationModel;
            $ws['id'] = $List['classification_id'];
            $classificationlist = $ifi->getFind($ws);
            $List['classification'] = $classificationlist['classification_name'];
            $ifi = new LabelModel;
            $wws['id'] = array('in', $List['label_id']);
            $grolist = $ifi->getlist($wws);
            $name = '';
            foreach ($grolist as $k => &$val) {
                $name .= $val['label_name'] . ",";
            }
            $List['label'] = substr($name, 0, -1);
            if ($List['is_sku'] == 2) {
                $List['price'] = $List['price'] / 100;
                $List['member_price'] = $List['member_price'] / 100;
            } else {
                $List['price'] = '';
            }
            $List['shop_display'] = (int)$List['shop_display'];
            $List['door_service'] = (int)$List['door_service'];
            $List['pre_door'] = (int)$List['pre_door'];
            $List['pre_door_mode'] = (int)$List['pre_door_mode'];
            $List['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] * 100 : '';
            $List['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] / 100 : '';
            $List['pro_shop'] = (int)$List['pro_shop'];
            $List['pro_shop_mode'] = (int)$List['pro_shop_mode'];
            $List['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] * 100 : '';
            $List['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] / 100 : '';
            if ($List['shop_display'] == 1) {
                $List['shop_displays'] = "展示";
            } else {
                $List['shop_displays'] = "不展示";
            }
            if ($List['door_service'] == 2) {
                $List['door_services'] = "开启";
            } else {
                $List['door_services'] = "关闭";
            }

            $matModel = new MaterialModel;
            if ($List['material_id']) {
                $imgid = explode(',', $List['material_id']);
                $imgwes['id'] = array("in", $imgid);
                $List['imgUrl'] = $matModel->getAll($imgwes);
                $List['imgUrl'] = $List['imgUrl']?$List['imgUrl']:array();
            } else {
                $List['imgUrl'] = array();
            }
            if ($List['video_id']) {
                $wes['id'] = $List['video_id'];
                $List['radioUrl'] = $matModel->getPageList($wes, 1, 10, 'id,urltype,url,cover as poster');
            } else {
                $List['radioUrl'] = array();
            }
            $List['duration'] = $List['duration'] . "分钟";

            $Skumodel = new SkuModel;
            $SkuValModel = new SkuValModel;
            $SkuAttrModel = new SkuAttrModel;
            $skuWe['ser_good_id'] = $List['service_id'];
            $skuWe['merchant_id'] = $user['merchantid'];
            $skuList = $Skumodel->getList($skuWe);
            $Skuname = array();
            $sku = '';
            if ($skuList) {
                foreach ($skuList as $k => $v) {
                    $sku .= $v['name'] . "：";
                    $Skuname[] = $v['name'];
                    $skuValwe['sku_id'] = $v['id'];
                    $skuValwe['merchant_id'] = $user['merchantid'];
                    $skuval = $SkuValModel->getList($skuValwe);
                    foreach ($skuval as $key => $val) {
                        $sku .= $val['name'] . ",";
                    }
                    $sku = substr($sku, 0, -1) . "；";
                }
                $List['sku'] = $sku;
                $attrTable = array();
                $attrTable['name'] = $Skuname ? $Skuname : '';
                $sAttr['attr.ser_good_id'] = $List['service_id'];
                $sAttr['attr.merchant_id'] = $user['merchantid'];
                $sAttr['stoattr.store_id'] = $List['store_id'];
                $SkuAttr = $SkuAttrModel->getStoList($sAttr);
                $price = array();
                $barcode = array();
                $archivePrice = array();
                foreach ($SkuAttr as $k => $v) {
                    $val = explode(',', $v['sku']);
                    $attrTable['val'][$k] = $val;
                    $price[$k] = $v['pri'] ? $v['pri'] / 100 : $v['price'] / 100;
                    $barcode[$k] = $v['barcode'];
                    $archivePrice[$k] = number_format(($v['archivePrice'] / 100), 2, '.', '');
                }
                $List['archivePriceArr'] = $archivePrice ? $archivePrice : '';
            }
            $List['attrTable'] = $attrTable ? $attrTable : '';
            $List['prices'] = $price ? $price : '';
            $List['barcode'] = $barcode ? $barcode : '';
        }
        if ($List) {
            $res['status'] = 1;
            $res['data'] = $List;
        } else {
            $res['status'] = 0;
        }
        return $res;
    }

    //门店
    public function EditStoStatus()
    {
        $id = input("post.id");
        $status = input("post.status");
        $ServiceModel = new ServiceModel;
        $serviceid = input("post.serviceid");
        $We['id'] = $serviceid;
        $find = $ServiceModel->getFind($We);
        if ($find['status'] == 2) {
            return $this->ajaxFail("总部服务为下架状态，禁止启用");
        }
        $model = new StoserviecModel;
        $where['id'] = $id;
        $Ed['status'] = $status;
        $Ed = $model->updates($where, $Ed);
        if ($Ed) {
            $msg = "修改成功";
            return $this->ajaxSuccess($data = null, $msg, $url = null);
        } else {
            return $this->ajaxFail();
        }
    }

    //门店编辑服务
    public function storeserviceed()
    {
//编辑传的id
        $id = input('get.id');
        if ($id) {
            $this->assign('id', $id);
        } else {
            $this->assign('id', '');
        }
        return $this->outPut("StoreServiceEd");
    }
 //门店添加商品添加至本店
 public function Auto_AddstoSer($storeid,$serviceid)
 {
     $Stoserviecmodel = new StoserviecModel;
     $service = new ServiceModel;
     $user = $this->userinfo;
    //  $post = input("post.");


     // 启动事务
     Db::startTrans();
     try {
         if (is_array($serviceid)) {
             foreach ($serviceid as $val) {
                 $we['id'] = $val;
                 $List = $service->getFind($we);
                 $add = array();
                 $add['merchant_id'] = $user['merchantid'];
                 $add['store_id'] =  $storeid;
                 $add['service_id'] = $val;
                 $serviceList = $Stoserviecmodel->getFind($add);
                 if (!$serviceList) {
                     $add['status'] = $List['status'];
                     $add['service_name'] = $List['service_name'];
                     $add['material_id'] = $List['material_id'];
                     $add['price'] = $List['price'] ? $List['price'] : '';
                     $add['shop_display'] = $List['shop_display'];
                     $add['door_service'] = $List['door_service'];
                     $add['pre_door'] = $List['pre_door'] ? $List['pre_door'] : '';
                     $add['pre_door_mode'] = $List['pre_door_mode'] ? $List['pre_door_mode'] : '';
                     $add['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] : '';
                     $add['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] : '';
                     $add['pro_shop'] = $List['pro_shop'] ? $List['pro_shop'] : '';
                     $add['pro_shop_mode'] = $List['pro_shop_mode'] ? $List['pro_shop_mode'] : '';
                     $add['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] : '';
                     $add['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] : '';
                     $add['is_sku'] = $List['is_sku'];
                     $add['addtime'] = time();
                     $Stoserviecmodel->add($add);
                     if ($List['is_sku'] == 1) {
                         $SkuAttrModel = new SkuAttrModel;
                         $SkuAttrWe['ser_good_id'] = $val;
                         $SkuAttrWe['merchant_id'] = $user['merchantid'];
                         $SkuAttr = $SkuAttrModel->getList($SkuAttrWe);
                         foreach ($SkuAttr as $k => $v) {
                             $StoSkuAttr = new StoSkuAttrModel;
                             $StoSkuAd['merchant_id'] = $user['merchantid'];
                             $StoSkuAd['store_id'] =  $storeid;
                             $StoSkuAd['service_id'] = $val;
                             $StoSkuAd['sku_attr_id'] = $v['id'];
                             $StoSkuAd['price'] = $v['price'];
                             $StoSkuAd['addtime'] = time();
                             $StoSkuAttr->add($StoSkuAd);
                         }
                     }
                 }
             }
         } else {
             $we['id'] = $serviceid;
             $List = $service->getFind($we);
             $add['merchant_id'] = $user['merchantid'];
             $add['store_id'] =  $storeid;
             $add['service_id'] = $serviceid;
             $serviceList = $Stoserviecmodel->getFind($add);
             if ($serviceList) {
                 $msg = "已加至门店";
                 return $this->ajaxFail($msg);
             }
             $add['status'] = $List['status'];
             $add['service_name'] = $List['service_name'];
             $add['material_id'] = $List['material_id'];
             $add['price'] = $List['price'] ? $List['price'] : '';
             $add['shop_display'] = $List['shop_display'];
             $add['door_service'] = $List['door_service'];
             $add['pre_door'] = $List['pre_door'] ? $List['pre_door'] : '';
             $add['pre_door_mode'] = $List['pre_door_mode'] ? $List['pre_door_mode'] : '';
             $add['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] : '';
             $add['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] : '';
             $add['pro_shop'] = $List['pro_shop'] ? $List['pro_shop'] : '';
             $add['pro_shop_mode'] = $List['pro_shop_mode'] ? $List['pro_shop_mode'] : '';
             $add['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] : '';
             $add['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] : '';
             $add['is_sku'] = $List['is_sku'];
             $add['addtime'] = time();
             $Stoserviecmodel->add($add);
             if ($List['is_sku'] == 1) {
                 $SkuAttrModel = new SkuAttrModel;
                 $SkuAttrWe['ser_good_id'] = $serviceid;
                 $SkuAttrWe['merchant_id'] = $user['merchantid'];
                 $SkuAttr = $SkuAttrModel->getList($SkuAttrWe);
                 foreach ($SkuAttr as $k => $v) {
                     $StoSkuAttr = new StoSkuAttrModel;
                     $StoSkuAd['merchant_id'] = $user['merchantid'];
                     $StoSkuAd['store_id'] =  $storeid;
                     $StoSkuAd['service_id'] = $serviceid;
                     $StoSkuAd['sku_attr_id'] = $v['id'];
                     $StoSkuAd['price'] = $v['price'];
                     $StoSkuAd['addtime'] = time();
                     $StoSkuAttr->add($StoSkuAd);
                 }
             }
         }
         // 提交事务
         Db::commit();
         return $this->ajaxSuccess();
     } catch (\Exception $e) {
         // 回滚事务
         Db::rollback();
     }
     return $this->ajaxFail();
 }
    //添加至本店
    public function AddstoSer()
    {
        $Stoserviecmodel = new StoserviecModel;
        $service = new ServiceModel;
        $user = $this->userinfo;
        $post = input("post.");
        // 启动事务
        Db::startTrans();
        try {
            if (is_array($post['serviceid'])) {
                foreach ($post['serviceid'] as $val) {
                    $we['id'] = $val;
                    $List = $service->getFind($we);
                    $add = array();
                    $add['merchant_id'] = $user['merchantid'];
                    $add['store_id'] = $post['storeid'];
                    $add['service_id'] = $val;
                    $serviceList = $Stoserviecmodel->getFind($add);
                    if (!$serviceList) {
                        $add['status'] = $List['status'];
                        $add['service_name'] = $List['service_name'];
                        $add['material_id'] = $List['material_id'];
                        $add['price'] = $List['price'] ? $List['price'] : '';
                        $add['shop_display'] = $List['shop_display'];
                        $add['door_service'] = $List['door_service'];
                        $add['pre_door'] = $List['pre_door'] ? $List['pre_door'] : '';
                        $add['pre_door_mode'] = $List['pre_door_mode'] ? $List['pre_door_mode'] : '';
                        $add['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] : '';
                        $add['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] : '';
                        $add['pro_shop'] = $List['pro_shop'] ? $List['pro_shop'] : '';
                        $add['pro_shop_mode'] = $List['pro_shop_mode'] ? $List['pro_shop_mode'] : '';
                        $add['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] : '';
                        $add['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] : '';
                        $add['is_sku'] = $List['is_sku'];
                        $add['addtime'] = time();
                        $Stoserviecmodel->add($add);
                        if ($List['is_sku'] == 1) {
                            $SkuAttrModel = new SkuAttrModel;
                            $SkuAttrWe['ser_good_id'] = $val;
                            $SkuAttrWe['merchant_id'] = $user['merchantid'];
                            $SkuAttr = $SkuAttrModel->getList($SkuAttrWe);
                            foreach ($SkuAttr as $k => $v) {
                                $StoSkuAttr = new StoSkuAttrModel;
                                $StoSkuAd['merchant_id'] = $user['merchantid'];
                                $StoSkuAd['store_id'] = $post['storeid'];
                                $StoSkuAd['service_id'] = $val;
                                $StoSkuAd['sku_attr_id'] = $v['id'];
                                $StoSkuAd['price'] = $v['price'];
                                $StoSkuAd['addtime'] = time();
                                $StoSkuAttr->add($StoSkuAd);
                            }
                        }
                    }
                }
            } else {
                $we['id'] = $post['serviceid'];
                $List = $service->getFind($we);
                $add['merchant_id'] = $user['merchantid'];
                $add['store_id'] = $post['storeid'];
                $add['service_id'] = $post['serviceid'];
                $serviceList = $Stoserviecmodel->getFind($add);
                if ($serviceList) {
                    $msg = "已加至门店";
                    return $this->ajaxFail($msg);
                }
                $add['status'] = $List['status'];
                $add['service_name'] = $List['service_name'];
                $add['material_id'] = $List['material_id'];
                $add['price'] = $List['price'] ? $List['price'] : '';
                $add['shop_display'] = $List['shop_display'];
                $add['door_service'] = $List['door_service'];
                $add['pre_door'] = $List['pre_door'] ? $List['pre_door'] : '';
                $add['pre_door_mode'] = $List['pre_door_mode'] ? $List['pre_door_mode'] : '';
                $add['door_percentage'] = $List['door_percentage'] ? $List['door_percentage'] : '';
                $add['door_fixed_amount'] = $List['door_fixed_amount'] ? $List['door_fixed_amount'] : '';
                $add['pro_shop'] = $List['pro_shop'] ? $List['pro_shop'] : '';
                $add['pro_shop_mode'] = $List['pro_shop_mode'] ? $List['pro_shop_mode'] : '';
                $add['shop_percentage'] = $List['shop_percentage'] ? $List['shop_percentage'] : '';
                $add['shop_fixed_amount'] = $List['shop_fixed_amount'] ? $List['shop_fixed_amount'] : '';
                $add['is_sku'] = $List['is_sku'];
                $add['addtime'] = time();
                $Stoserviecmodel->add($add);
                if ($List['is_sku'] == 1) {
                    $SkuAttrModel = new SkuAttrModel;
                    $SkuAttrWe['ser_good_id'] = $post['serviceid'];
                    $SkuAttrWe['merchant_id'] = $user['merchantid'];
                    $SkuAttr = $SkuAttrModel->getList($SkuAttrWe);
                    foreach ($SkuAttr as $k => $v) {
                        $StoSkuAttr = new StoSkuAttrModel;
                        $StoSkuAd['merchant_id'] = $user['merchantid'];
                        $StoSkuAd['store_id'] = $post['storeid'];
                        $StoSkuAd['service_id'] = $post['serviceid'];
                        $StoSkuAd['sku_attr_id'] = $v['id'];
                        $StoSkuAd['price'] = $v['price'];
                        $StoSkuAd['addtime'] = time();
                        $StoSkuAttr->add($StoSkuAd);
                    }
                }
            }
            // 提交事务
            Db::commit();
            return $this->ajaxSuccess();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
        }
        return $this->ajaxFail();
    }

    //总部服务详情页面
    public function servicetotal()
    {
        //编辑传的id
        $id = input('get.id');
        if ($id) {
            $this->assign('id', $id);
        } else {
            $this->assign('id', '');
        }
        return $this->outPut("Servicetotal");
    }

    //总部服务详情
    public function Servicetotals()
    {
        $id = input('post.id');
        $user = session('userinfo');
        $where['id'] = $id;
        $model = new ServiceModel;
        $List = $model->getFind($where);
        if ($List) {
            $List['content'] = htmlspecialchars_decode($List['content']);
            $ifi = new ClassificationModel;
            $ws['id'] = $List['classification_id'];
            $classificationlist = $ifi->getFind($ws);
            $List['classification'] = $classificationlist['classification_name'];
            $ifi = new LabelModel;
            $wws['id'] = array('in', $List['label_id']);
            $grolist = $ifi->getlist($wws);
            $name = '';
            foreach ($grolist as $k => &$val) {
                $name .= $val['label_name'] . ",";
            }
            $List['label'] = substr($name, 0, -1);
            $List['price'] = $List['price'] ? $List['price'] / 100 : '-';
            if ($List['material_id']) {
                $imgUrl = array();
                $imaid = explode(',', $List['material_id']);
                foreach ($imaid as $k => &$v) {
                    $ws['id'] = $v;
                    $matModel = new MaterialModel;
                    $imgFile = $matModel->getFind($ws);
                    if($imgFile){
                        $imgUrl[] = $imgFile;
                    }
                }
                $List['imgUrl'] = $imgUrl;
            }

            if ($List['video_id']) {
                $wes['id'] = $List['video_id'];
                $List['radioUrl'] = $matModel->getAll($wes);
            } else {
                $List['radioUrl'] = array();
            }
            if ($List['shop_display'] == 1) {
                $List['shop_display'] = "展示";
            } else {
                $List['shop_display'] = "不展示";
            }
            if ($List['door_service'] == 2) {
                $List['door_services'] = "开启";
            } else {
                $List['door_services'] = "关闭";
            }
            $List['duration'] = $List['duration'] . "分钟";

            $Skumodel = new SkuModel;
            $SkuValModel = new SkuValModel;
            $skuWe['ser_good_id'] = $List['id'];
            $skuWe['merchant_id'] = $user['merchantid'];
            $skuList = $Skumodel->getList($skuWe);
            $Skuname = array();
            $sku = '';
            if ($skuList) {
                foreach ($skuList as $k => $v) {
                    $sku .= $v['name'] . "：";
                    $Skuname[] = $v['name'];
                    $skuValwe['sku_id'] = $v['id'];
                    $skuValwe['merchant_id'] = $user['merchantid'];
                    $skuval = $SkuValModel->getList($skuValwe);
                    foreach ($skuval as $key => $val) {
                        $sku .= $val['name'] . ",";
                    }
                    $sku = substr($sku, 0, -1) . "；";
                }
                $List['sku'] = $sku;
                $attrTable = array();
                $attrTable['name'] = $Skuname ? $Skuname : '';
                $SkuAttrModel = new SkuAttrModel;
                $sAttr['ser_good_id'] = $List['id'];
                $sAttr['merchant_id'] = $user['merchantid'];
                $SkuAttr = $SkuAttrModel->getList($sAttr);
                $price = array();
                $barcode = array();
                foreach ($SkuAttr as $k => $v) {
                    $val = explode(',', $v['sku']);
                    $attrTable['val'][$k] = $val;
                    $price[$k] = $v['price'] / 100;
                    $barcode[$k] = $v['barcode'];
                }
            }
            $List['attrTable'] = $attrTable ? $attrTable : '';
            $List['prices'] = $price ? $price : '';
            $List['barcode'] = $barcode ? $barcode : '';
        }
        if ($List) {
            $res['status'] = 1;
            $res['data'] = $List;
        } else {
            $res['status'] = 0;
        }
        return $res;
    }

    //修改门店服务名称，价格
    public function EdStoSer()
    {
        $post = input("post.");
        $user = $this->userinfo;
        $Stoser = new StoserviecModel;
        $img_id = '';
        if ($post['imgUrl']) {
            foreach ($post['imgUrl'] as $k => &$v) {
                $img_id .= $v['id'] . ",";
            }
            $img_id = substr($img_id, 0, -1);//标签id拼接
        }
        $where['id'] = $post['id'];
        $Ed['service_name'] = $post['name'];
        $Ed['shop_display'] = $post['shop_display'];
        $Ed['door_service'] = $post['door_service'];
        $Ed['door_service'] = $post['door_service'];//上门服务
        $Ed['pre_door'] = $post['pre_door'] ? $post['pre_door'] : '';//预约上门
        $Ed['pre_door_mode'] = $post['pre_door_mode'] ? $post['pre_door_mode'] : '';//上门支付
        $Ed['door_percentage'] = $post['door_percentage'] ? $post['door_percentage'] / 100 : 0;//上门百分比支付
        $Ed['door_fixed_amount'] = $post['door_fixed_amount'] ? $post['door_fixed_amount'] * 100 : 0;//上门固定金额支付
        $Ed['pro_shop'] = $post['pro_shop'];//预约到店
        $Ed['pro_shop_mode'] = $post['pro_shop_mode'] ? $post['pro_shop_mode'] : '';//到店支付
        $Ed['shop_percentage'] = $post['shop_percentage'] ? $post['shop_percentage'] / 100 : 0;//到店百分比支付
        $Ed['shop_fixed_amount'] = $post['shop_fixed_amount'] ? $post['shop_fixed_amount'] * 100 : 0;//到店固定金额支付
        $Ed['material_id'] = $img_id;
        $Ed['issellnum'] = $post['issellnum'];
        $Ed['isset'] = 1;
        if (!isset($post['sxv'])) {
            $Ed['price'] = $post['price'] * 100;
            $Ed['member_price'] = $post['member_price'] * 100;
            
        }
        // 启动事务
        Db::startTrans();
        try {
            $stoSerEd = $Stoser->updates($where, $Ed);
            if (isset($post['sxv'])) {
                $skuAttr = new SkuAttrModel;
                $SkuWe['ser_good_id'] = $post['serviceid'];
                $SkuWe['merchant_id'] = $user['merchantid'];
                $skuAttrList = $skuAttr->getList($SkuWe);
                $StoSkuAttr = new StoSkuAttrModel;
                foreach ($post['sxv'] as $k => &$v) {
                    $StoSku['merchant_id'] = $user['merchantid'];
                    $StoSku['service_id'] = $post['serviceid'];
                    $StoSku['store_id'] = $user['storeid'];
                    $StoSku['sku_attr_id'] = $skuAttrList[$k]['id'];
                    $price = array();
                    $price['price'] = $post['prices'][$k] * 100;
                    // $price['member_price'] = $post['member_price'][$k] * 100;
                    $price['isset'] = 1;
                    $EdSkuprice = $StoSkuAttr->updates($StoSku, $price);
                }
            }
            // 提交事务
            Db::commit();
            $msg = "修改成功";
            return $this->ajaxSuccess($data = null, $msg);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->ajaxFail("修改失败");
        }
    }

    //删除门店服务
    public function DelStoSer()
    {
        $id = input("post.id");
        $user = $this->userinfo;
        $StoService = new StoserviecModel;
        $StoSkuAttr = new StoSkuAttrModel;
        $where['id'] = $id;
        $where['store_id'] = $user['storeid'];
        $where['merchant_id'] = $user['merchantid'];
        $edit['status'] = 3;

        $Ed = $StoService->updates($where, $edit);
        if ($Ed) {
            return $this->ajaxSuccess($data = null, '删除成功');
        } else {
            return $this->ajaxFail("删除失败");
        }
    }

    public function getClassificationList()
    {
        $user = $this->userinfo;
        $ifi = new ClassificationModel;
        $ws['merchant_id'] = $user['merchantid'];
        $ws['status'] = 1;
        $data = $ifi->getlist($ws);
        if ($data) {
            return $this->ajaxSuccess($data, '成功');
        } else {
            return $this->ajaxFail("失败");
        }
    }

    //生成服务推广二维码
    public function getServiceCode()
    {
        $post = input('post.');
        $user = $this->userinfo;
        $serviceid = $post['id'];
        $storeid = $post['storeid'];
        $merchant_id = $user['merchantid'];
        $page = 'pages/appointmentDetail/appointmentDetail';
        $xcxData["path"] = $page.'?serviceid='.$serviceid.'&merchantid='.$merchant_id.'&storeid='.$storeid; //绑定参数，字符串依次：服务id，商户id，门店id
        $xcxData["width"] = 280;//二维码宽度
		Request::instance()->post(array(
			'merchantid' => $merchant_id,
			'path' => $xcxData["path"],
			'width' => $xcxData["width"],
		));
		$XcxCtrl = new XcxCtrl;
		$res = $XcxCtrl->setVerify(md5($merchant_id))->getXcxQrCode();
		return $res;
    }
    /*批量操作*/
    public function HQbatchSet(){
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $ServiceModel = new ServiceModel;
        // 上架
        if($post['type'] == 1){
            $map = array(
                'merchant_id'=>$userInfo['merchantid'],
                'id'=>array('in',$ids)
            );
            $res = $ServiceModel->updates($map,array('status'=>1));
            if($res!==false){
                return $this->ajaxSuccess('上架成功');
            }else{
                return $this->ajaxSuccess('上架失败');
            }
        }
        // 下架
        if($post['type'] == 2){
            $map = array(
                'merchant_id'=>$userInfo['merchantid'],
                'id'=>array('in',$ids)
            );
            $res = $ServiceModel->updates($map,array('status'=>2));
            if($res!==false){
                $StoserviecModel = new StoserviecModel;
                $map = array(
                    'merchant_id'=>$userInfo['merchantid'],
                    'service_id'=>array('in',$ids)
                );
                // 总部下架  店铺直接下架
                $StoserviecModel->updates($map,array('status'=>2));
                return $this->ajaxSuccess('下架成功');
            }else{
                return $this->ajaxFail('下架失败');
            }
        }
        // 删除
        if($post['type'] == 3){
            $map = array(
                'merchant_id'=>$userInfo['merchantid'],
                'id'=>array('in',$ids)
            );
            $res = $ServiceModel->updates($map,array('status'=>3));
            if($res!==false){
                $StoserviecModel = new StoserviecModel;
                $map = array(
                    'merchant_id'=>$userInfo['merchantid'],
                    'service_id'=>array('in',$ids)
                );
                // 总部下架  店铺直接下架
                $StoserviecModel->updates($map,array('status'=>3));
                return $this->ajaxSuccess('删除成功');
            }else{
                return $this->ajaxFail('删除失败');
            }
        }
        return $this->ajaxFail('错误的操作');
    }
    /*批量操作*/
    public function STbatchSet(){
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $StoserviecModel = new StoserviecModel;
        // 上架
        if($post['type'] == 1){
            // 有小问题
            $ServiceModel = new ServiceModel;
            $idsArr = explode(',',$ids);
            foreach ($idsArr as $k=>$v){
                $one = $StoserviecModel->getFind(array('id'=>$v,'store_id'=>$userInfo['storeid']),array('id','service_id'));
                if($one){
                    $We['id'] = $one['service_id'];
                    $find = $ServiceModel->getFind($We);
                    if ($find['status'] == 2) {
                        continue;
                    }
                    $map = array(
                        'merchant_id'=>$userInfo['merchantid'],
                        'id'=>$one['id'],
                        'store_id'=>$userInfo['storeid']
                    );
                    $res = $StoserviecModel->updates($map,array('status'=>1));
                }
            }
            if($res!==false){
                return $this->ajaxSuccess('上架成功');
            }else{
                return $this->ajaxFail('上架失败');
            }
        }
        // 下架
        if($post['type'] == 2){
            $map = array(
                'merchant_id'=>$userInfo['merchantid'],
                'id'=>array('in',$ids),
                'store_id'=>$userInfo['storeid']
            );
            $res = $StoserviecModel->updates($map,array('status'=>2));
            if($res!==false){
                return $this->ajaxSuccess('下架成功');
            }else{
                return $this->ajaxFail('下架失败');
            }
        }
        return $this->ajaxFail('错误的操作');
    }

    public function batchAdjustPrice(){
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $ServiceModel = new ServiceModel;
        $map = array(
            'merchant_id'=>$userInfo['merchantid'],
            'id'=>array('in',$ids)
        );
        $res = $ServiceModel->updates($map,array('adjust_price_id'=>$post['val']));
        if($res!==false){
            return $this->ajaxSuccess('调价范围设置成功');
        }else{
            return $this->ajaxSuccess('调价范围设置失败');
        }
    }

    public function setSort(){
        $post = input('post.');
        $StoserviecModel = new StoserviecModel;
        $data = array(
            'sort'=>$post['sort'],
        );
        $map = array(
            'merchant_id'=>$this->userinfo['merchantid'],
            'store_id'=>$this->userinfo['storeid'],
            'id'=>$post['id'],
        );
        $res = $StoserviecModel->updates($map,$data);
        if($res){
            return $this->ajaxSuccess('设置成功');
        }
        $error = $StoserviecModel->error;
        return $this->ajaxFail($error?$error:'设置失败');
    }
}