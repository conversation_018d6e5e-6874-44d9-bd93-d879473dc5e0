<style>
  .f-incometable-table {
    border-collapse: collapse;
    text-align: center;
    vertical-align: middle;
  }
  .f-incometable-table td,
  .f-incometable-table th {
    border: 1px solid rgba(0, 0, 0, 0.3);
    padding: 6px 18px;
  }
  .f-incometable-table tbody,
  .f-incometable-table tr:not(:nth-child(1)) {
    text-align: right;
  }
</style>
<div class="layui-fluid">
  <div id="incometable" v-cloak style="height: 100%">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>利润表</span>
      </div>
      <div>
        <div class="flex items-center space-x-4">
          <template v-if="isFactory">
            <div>统计门店</div>
            <el-select
              v-model="storeid"
              filterable
              placeholder="请选择统计门店"
              @change="handleStoreChange"
              size="small"
            >
              <el-option :key="0" label="请选择门店" :value="0"> </el-option>
              <el-option
                v-for="item in storeListOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
          <div class="flex items-center space-x-2">
            <div>统计年份</div>
            <el-date-picker
              v-model="statisticalYear"
              type="year"
              size="small"
              placeholder="选择年份"
              :clearable="false"
            ></el-date-picker>
            <el-button type="primary" size="small" @click="exportData"
              >导出</el-button
            >
          </div>
        </div>
        <div class="p-4 mt-4 h-fit overflow-auto">
          <table class="f-incometable-table" v-loading="loading">
            <thead>
              <tr class="bg-gray-200">
                <th>月份</th>
                <th>收入</th>
                <th>支出</th>
                <th>利润</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(month, index) in months" :key="month">
                <td>{{month}}</td>
                <td :class="tableData.income[index] < 0 && 'text-red'">
                  {{formatMoney(tableData.income[index])}}
                </td>
                <td :class="tableData.expenditure[index] < 0 && 'text-red'">
                  {{formatMoney(tableData.expenditure[index])}}
                </td>
                <td :class="tableData.profit[index] < 0 && 'text-red'">
                  {{formatMoney(tableData.profit[index])}}
                </td>
              </tr>
              <tr class="bg-gray-100">
                <td>汇总</td>
                <td :class="tableData.total.income < 0 && 'text-red'">
                  {{formatMoney(tableData.total[0])}}
                </td>
                <td :class="tableData.total.expenditure < 0 && 'text-red'">
                  {{formatMoney(tableData.total[1])}}
                </td>
                <td :class="tableData.total.profit < 0 && 'text-red'">
                  {{formatMoney(tableData.total[2])}}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-card>
  </div>
</div>
<script type="text/javascript" src="js/xlsx.full.min.js"></script>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#incometable",
      data() {
        return {
          // 默认上个月
          statisticalYear: new Date(),
          loading: false,
          months: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月",
          ],
          tableData: {
            income: Array(12).fill(0), // 收入数据
            expenditure: Array(12).fill(0), // 支出数据
            profit: Array(12).fill(0), // 利润数据
            total: {
              income: 0,
              expenditure: 0,
              profit: 0,
            },
          },
          isFactory: false,
          storeid: 0,
          storeList: [],
          selectedStore: {},
        };
      },
      computed: {
        storeListOptions() {
          return this.storeList.map((item) => ({
            value: item.id,
            label: item.storetag,
          }));
        },
      },
      methods: {
        getStoreList() {
          var vm = this;
          $.ajax({
            url: "{:url('/store/tab_store/getstorelist')}",
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.storeList = res.data;
              }
            },
          });
        },
        // 格式化金额
        formatMoney(money) {
          if (money == null || money == undefined) return "-";
          const num = Number(money);
          const isNegative = num < 0;
          const absNum = Math.abs(num);
          const formatted = absNum.toLocaleString("en-US", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          });
          return (isNegative ? "-￥" : "￥") + formatted;
        },
        /**
         * 获取利润表数据
         * @param {string} year - 年份
         */
        getTableData(year) {
          var vm = this;
          // 初始化 tableData
          vm.tableData = {
            income: Array(12).fill(0), // 收入数据
            expenditure: Array(12).fill(0), // 支出数据
            profit: Array(12).fill(0), // 利润数据
            total: {
              income: 0,
              expenditure: 0,
              profit: 0,
            },
          };
          if (!vm.storeid) return;
          vm.loading = true;
          $.ajax({
            url: "{:url('Financialstatements/getIncomeTableData')}",
            data: {
              year: year,
              storeid: vm.storeid,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.tableData = res.data;
              } else {
                vm.$message.error(res.msg);
              }
            },
            error: function () {
              vm.$message.error("获取数据失败");
            },
            complete: function () {
              vm.loading = false;
            },
          });
        },
        exportData() {
          // 获取当前月份作为文件名的一部分
          const year = this.statisticalYear.getFullYear();

          // 检查是否需要通过URL导出
          if (this.isFactory) {
            // 构建下载URL
            const preUrl = "{:url('Financialstatements/exportIncomeReport')}";
            const data = {
              year: year,
              storeid: this.storeid,
            };
            const url = this.getUrl(preUrl, data);

            // 显示提示消息
            this.$message.warning("数据较多时，将花费较多时间，请耐心等待");

            // 使用location.href触发文件下载
            location.href = url;
          } else {
            const fileName = `利润表_${year}年.xlsx`;

            // 获取表格DOM元素
            const table = document.querySelector(".f-incometable-table");

            // 从表格DOM创建工作表
            const ws = XLSX.utils.table_to_sheet(table);

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 将工作表添加到工作簿
            XLSX.utils.book_append_sheet(wb, ws, `${year}年利润表`);

            // 导出工作簿为Excel文件
            XLSX.writeFile(wb, fileName);
          }
        },

        getParam(data) {
          let url = "";
          for (var k in data) {
            let value = data[k] !== undefined ? data[k] : "";
            url += `&${k}=${encodeURIComponent(value)}`;
          }
          return url ? url.substring(1) : "";
        },

        /**
         * 将url和参数拼接成完整地址
         * @param {string} url url地址
         * @param {Json} data json对象
         * @returns {string}
         */
        getUrl(url, data) {
          //看原始url地址中开头是否带?，然后拼接处理好的参数
          return (url +=
            (url.indexOf("?") < 0 ? "?" : "&") + this.getParam(data));
        },

        handleStoreChange(value) {
          this.storeid = value;
          const year = this.statisticalYear.getFullYear();
          this.getTableData(year);
        },
      },
      watch: {
        statisticalYear(newVal) {
          // 只需要年份
          const year = newVal.getFullYear();
          this.getTableData(year);
        },
      },
      created: function () {
        this.storeid = layui.S_USER_TOKEN.storeid;
        this.isFactory = !this.storeid;
        if (this.isFactory) {
          this.getStoreList();
        } else {
          const year = this.statisticalYear.getFullYear();
          this.getTableData(year);
        }
      },
    });
  });
</script>
