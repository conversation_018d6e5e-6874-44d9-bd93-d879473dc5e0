<!--[meiye_07_01]-->
<div class="layui-fluid">
  <div id="card" v-cloak>
    <el-card class="box-card" v-loading="loading">
      <template>
        <div class="flex flex-wrap items-center gap-x-4 gap-y-2 mb-4">
          <!--  <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="卡项类型">
              <el-radio-group
                v-model="cardType"
                @change="cardTypeChange"
                size="mini"
              >
                <el-radio-button :label="0">全部</el-radio-button>
                <el-radio-button :label="1">次卡</el-radio-button>
                <el-radio-button :label="2">充值卡</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form> -->
          <div class="flex items-center space-x-2">
            <div class="shrink-0">购买店铺</div>
            <el-select
              v-model="storeid"
              size="small"
              @change="cardTypeChange"
              style="width: 160px"
            >
              <el-option v-if="!isStore" label="全部门店" value="0"></el-option>
              <el-option
                v-for="item in storeList"
                :key="item.id"
                :label="item.storetag"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex items-center space-x-2">
            <div class="shrink-0">客户信息</div>
            <el-input
              placeholder="可输入客户名、备注名、手机、会员号"
              v-model="keyword"
              clearable
              size="small"
              @keyup.enter.native="invalid_current_change(1)"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="invalid_current_change(1)"
              ></el-button>
            </el-input>
          </div>
        </div>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="有效" name="1">
            <el-table :data="effectiveData" style="width: 100%">
              <el-table-column prop="cardName" label="卡信息" min-width="177px">
                <template slot-scope="scope">
                  <div>
                    <div v-text="scope.row.cardName"></div>
                    <!-- <el-tag size="mini"
                      ><span v-text="scope.row.type"></span
                    ></el-tag> -->
                    <div class="text-gray">{{scope.row.IndateName}}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Buyer" label="归属会员" min-width="150px">
                <template slot-scope="scope">
                  <el-row type="flex" justify="start" align="middle">
                    <!-- <div v-if="scope.row.Buyer.id">
                      <el-image
                        style="width: 50px; height: 50px; border-radius: 50%"
                        :src="scope.row.Buyer.pic ? scope.row.Buyer.pic : 'assets/img/touxoang.png'"
                        fit="contain"
                      >
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </div> -->
                    <div class="text-sm" v-if="scope.row.Buyer.id">
                      <div
                        class="text-primary cursor-pointer"
                        @click="toMember(scope.row.Buyer)"
                        v-text="scope.row.Buyer.member_name"
                      ></div>
                      <div class="text-gray">{{scope.row.Buyer.phone}}</div>
                    </div>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column prop="buyprice" label="销售信息" width="200px">
                <template slot-scope="scope">
                  <div>
                    <span class="text-gray">售价：</span
                    >￥{{toMoney(scope.row.buyprice)}}
                  </div>
                  <div>
                    <span class="text-gray">购卡：</span
                    >{{scope.row.BuyTimeName}}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="money" label="余额">
                <template slot-scope="scope">
                  <div
                    v-if="scope.row.cardtype==2"
                    v-text="'￥'+toMoney(scope.row.totalbalance - scope.row.usebalance)"
                  ></div>
                  <div v-else>--</div>
                </template>
              </el-table-column> -->
              <el-table-column prop="money" label="次数" min-width="280">
                <template slot-scope="scope">
                  <div>
                    <div class="flex items-center space-x-2">
                      <div class="text-gray">非赠共：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.num == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.num}}
                      </div>
                      <div class="text-gray">，已用：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.usenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.usenum}}
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="text-gray">赠送共：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.givenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.givenum}}
                      </div>
                      <div class="text-gray">，已用：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.usegivenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.usegivenum}}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="money"
                label="次数"
                width="200px"
                align="center"
              >
                <el-table-column
                  prop="money"
                  label="使用"
                  width="60px"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p><span>{{scope.row.numData.usenum}}</span></p>
                      <hr />
                      <p><span>{{scope.row.numData.usegivenum}} </span></p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="money"
                  label="总次数"
                  width="80px"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p><span>{{scope.row.numData.num}}</span></p>
                      <hr />
                      <p>
                        <span>{{scope.row.numData.givenum}}</span>
                      </p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="money" label="" width="40px">
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p style="visibility: hidden">购</p>
                      <hr />
                      <p><el-badge :value="'赠'" type="primary"> </el-badge></p>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column> -->
              <el-table-column prop="storeName" label="适用门店">
              </el-table-column>
              <el-table-column prop="StatusName" label="状态">
                <template slot-scope="scope">
                  <el-tag
                    size="mini"
                    :type="scope.row.StatusName=='使用中'?'primary':'danger'"
                  >
                    {{scope.row.StatusName}}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="170">
                <template slot-scope="scope">
                  <el-button
                    @click="getSellCard(scope.row)"
                    type="text"
                    size="small"
                    >卡项详情
                  </el-button>
                  <el-button
                    @click="seeOrder(scope.row.order_id)"
                    type="text"
                    size="small"
                    >订单详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px">
              <el-pagination
                @size-change="effective_size_change"
                @current-change="effective_current_change"
                :pager-count="5"
                :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                :page-size="10"
                style="float: right"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane label="失效" name="2">
            <el-table :data="invalidData" style="width: 100%">
              <el-table-column prop="cardName" label="卡信息" min-width="177px">
                <template slot-scope="scope">
                  <div>
                    <div v-text="scope.row.cardName"></div>
                    <!-- <el-tag size="mini"
                      ><span v-text="scope.row.type"></span
                    ></el-tag> -->
                    <div class="text-gray">{{scope.row.IndateName}}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Buyer" label="归属会员" min-width="177px">
                <template slot-scope="scope">
                  <el-row type="flex" justify="start" align="middle">
                    <div v-if="scope.row.Buyer.id">
                      <el-image
                        style="width: 50px; height: 50px; border-radius: 50%"
                        :src="scope.row.Buyer.pic ? scope.row.Buyer.pic : 'assets/img/touxoang.png'"
                        fit="contain"
                      >
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </div>
                    <div v-if="scope.row.Buyer.id" style="padding-left: 10px">
                      <el-button
                        size="small"
                        type="text"
                        @click="toMember(scope.row.Buyer)"
                        v-text="scope.row.Buyer.member_name"
                      ></el-button>
                      <p v-text="scope.row.Buyer.phone"></p>
                    </div>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column prop="buyprice" label="销售信息" width="200px">
                <template slot-scope="scope">
                  <div>
                    <span class="text-gray">售价：</span
                    >￥{{toMoney(scope.row.buyprice)}}
                  </div>
                  <div>
                    <span class="text-gray">购卡：</span
                    >{{scope.row.BuyTimeName}}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="money" label="余额">
                <template slot-scope="scope">
                  <div
                    v-if="scope.row.cardtype==2"
                    v-text="'￥'+toMoney(scope.row.totalbalance - scope.row.usebalance)"
                  ></div>
                  <div v-else>--</div>
                </template>
              </el-table-column> -->
              <el-table-column prop="money" label="次数" min-width="280">
                <template slot-scope="scope">
                  <div>
                    <div class="flex items-center space-x-2">
                      <div class="text-gray">非赠共：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.num == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.num}}
                      </div>
                      <div class="text-gray">，已用：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.usenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.usenum}}
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="text-gray">赠送共：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.givenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.givenum}}
                      </div>
                      <div class="text-gray">，已用：</div>
                      <div
                        class="font-bold"
                        :class="scope.row.numData.usegivenum == 0 ? 'text-gray' : ''"
                      >
                        {{scope.row.numData.usegivenum}}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="money"
                label="次数"
                width="200px"
                align="center"
              >
                <el-table-column
                  prop="money"
                  label="使用"
                  width="60px"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p><span>{{scope.row.numData.usenum}}</span></p>
                      <hr />
                      <p><span>{{scope.row.numData.usegivenum}} </span></p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="money"
                  label="总次数"
                  width="80px"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p><span>{{scope.row.numData.num}}</span></p>
                      <hr />
                      <p>
                        <span>{{scope.row.numData.givenum}}</span>
                      </p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="money" label="" width="40px">
                  <template slot-scope="scope">
                    <div v-if="scope.row.cardtype==2" v-text="'--'"></div>
                    <div v-else>
                      <p style="visibility: hidden">购</p>
                      <hr />
                      <p><el-badge :value="'赠'" type="primary"> </el-badge></p>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column> -->
              <el-table-column prop="storeName" label="适用门店">
              </el-table-column>
              <el-table-column prop="StatusName" label="状态">
                <template slot-scope="scope">
                  <el-tag
                    size="mini"
                    :type="scope.row.StatusName=='使用中'?'primary':'danger'"
                  >
                    {{scope.row.StatusName}}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="170">
                <template slot-scope="scope">
                  <!--<el-button @click="seeDetails(scope.row.id)" type="text" size="small">卡项详情
                                    </el-button>-->
                  <el-button
                    @click="getSellCard(scope.row)"
                    type="text"
                    size="small"
                    >卡项详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px">
              <el-pagination
                @size-change="invalid_size_change"
                @current-change="invalid_current_change"
                :pager-count="5"
                :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                :page-size="10"
                style="float: right"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totals"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <el-dialog
        title="会员卡信息"
        :visible.sync="dialogTable"
        :modal-append-to-body="false"
        width="50%"
      >
        <div class="layui-form-item" id="seeImg">
          <div class="layui-inline" style="width: 100%">
            <el-row>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">会员卡名称：</div>
                  <div class="main_right">
                    <p>{{cardForm.cardName ? cardForm.cardName : '--'}}</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">卡类型：</div>
                  <div class="main_right">
                    <p>{{cardForm.type ? cardForm.type : '--'}}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">购买金额：</div>
                  <div class="main_right">
                    <p>￥ {{cardForm.BuyMoney}}</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">购买时间：</div>
                  <div class="main_right">
                    <p>{{cardForm.BuyTimeName}}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">卡项次数：</div>
                  <div class="main_right">
                    <p>{{cardData.num}}</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">使用次数：</div>
                  <div class="main_right">
                    <p>{{cardData.usenum}}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">赠送次数：</div>
                  <div class="main_right">
                    <p>{{cardData.givenum}}</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="Information">
                  <div class="main_left">使用赠送次数：</div>
                  <div class="main_right">
                    <p>{{cardData.usegivenum}}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div v-if="cardForm.cardtype == 2">
              <el-row>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">充值卡总金额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.totalbalance}}</p>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">已使用余额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.usebalance}}</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">本金金额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.capitalbalance}}</p>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">已使用本金金额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.usecapitalbalance}}</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">赠送金额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.presentbalance}}</p>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="Information">
                    <div class="main_left">已使用赠送金额：</div>
                    <div class="main_right">
                      <p>￥ {{cardForm.usepresentbalance}}</p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-dialog>
      <order-detail-dialog
        :is-show="showOrderCardData"
        :order-details="orderDetails"
        @sync-is-show="showOrderCardData = $event"
      ></order-detail-dialog>
    </el-card>
  </div>
</div>
<script src="/assets/src/views/component/orderDetailDialog/index.js"></script>
<script>
  var $ = layui.$;
  new Vue({
    el: "#card",
    data: function () {
      return {
        activeName: "1",
        effectiveData: [],
        invalidData: [],
        cardForm: [],
        cardData: [],
        loading: false,
        dialogTable: false,
        keyword: "",
        page: 1,
        limit: 10,
        total: 0,
        totals: 0,
        showOrderCardData: false,
        orderDetails: {},
        cardType: 0,
        storeList: [],
        storeid: "{$storeid}",
        isStore: "{$storeid}",
      };
    },
    methods: {
      toMoney(num) {
        return (num / 100).toFixed(2);
      },
      getSellCard(row) {
        this.orderDetails = row;
        this.showOrderCardData = true;
        /* let orderDetails = row;
        let vm = this;
        let obj = {
          target: vm.$refs.activeRefund.$refs.dialog,
          background: "rgba(255,255,255,0.5)",
          duration: 0,
        };
        top.layui.vueCommon.showLoading(obj);
        $.ajax({
          url: "{:url('order/getVipCardData')}",
          data: {
            storeid: orderDetails.storeid,
            memberId: orderDetails.memberid,
            order_id: orderDetails.order_id,
          },
          type: "POST",
          success(res) {
            if (res.code == 1) {
              vm.orderCardData = res.data;
            } else {
              vm.$message({
                type: "error",
                message: res.msg,
              });
            }
            top.layui.vueCommon.hideLoading();
          },
        }); */
      },
      seeOrder(id) {
        let h = "/Order/seorder/id=" + id;
        // window.open("#" + h);
        location.hash = h;
      },
      toMember(buyer) {
        let h = "/Member/det/det/" + buyer.id;
        // window.open("#" + h);
        location.hash = h;
      },
      onSubmit() {},
      //有效卡项
      effectiveList: function () {
        let vm = this;
        vm.loading = true;
        $.ajax({
          url: "{:url('effectiveList')}",
          data: {
            page: vm.page,
            limit: vm.limit,
            cardType: vm.cardType,
            storeid: vm.storeid,
            keyword: vm.keyword,
          },
          type: "post",
          success: function (res) {
            if (res.code == 0) {
              vm.effectiveData = res.data;
              vm.total = res.count;
            }
            vm.loading = false;
          },
        });
      },
      //失效卡项
      invalidList: function () {
        let vm = this;
        vm.loading = true;
        $.ajax({
          url: "{:url('invalidList')}",
          data: {
            page: vm.page,
            limit: vm.limit,
            cardType: vm.cardType,
            storeid: vm.storeid,
            keyword: vm.keyword,
          },
          type: "post",
          success: function (res) {
            if (res.code == 0) {
              vm.invalidData = res.data;
              vm.totals = res.count;
              vm.loading = false;
            }
          },
        });
      },
      seeDetails(id) {
        let that = this;
        $.ajax({
          url: "{:url('cardDetails')}",
          data: {
            id: id,
          },
          type: "post",
          success: function (res) {
            that.cardForm = res.data;
            that.cardData = res.data.numData;
          },
        });
        that.dialogTable = true;
      },

      //有效分页
      effective_size_change(val) {
        this.limit = val;
        this.page = 1;
        this.effectiveList();
      },
      /* 切换页数 */
      effective_current_change(val) {
        this.page = val;
        this.effectiveList();
      },

      //失效分页
      invalid_size_change(val) {
        this.limit = val;
        this.page = 1;
        this.invalidList();
      },
      /* 切换页数 */
      invalid_current_change(val) {
        this.page = val;
        this.invalidList();
      },
      cardTypeChange() {
        if (this.activeName == 1) {
          this.page = 1;
          this.effectiveList();
        } else {
          this.page = 1;
          this.invalidList();
        }
      },
      //获取门店
      getStoreList() {
        $.ajax({
          url: "{:url('TabStore/getStoreList')}",
          data: {},
          type: "POST",
          success: (res) => {
            if (res.code == 0) {
              this.storeList = res.data;
            }
          },
        });
      },
    },
    created: function () {
      this.effectiveList();
      this.isStore = parseInt(this.isStore);
      if (!this.isStore) {
        this.getStoreList();
      }
    },
    watch: {
      activeName(n) {
        if (n == 1) {
          this.page = 1;
          this.effectiveList();
        } else {
          this.page = 1;
          this.invalidList();
        }
      },
    },
  });
</script>
<style>
  #card {
    height: 100%;
    min-height: 100%;
  }

  .Information {
    min-height: 36px;
    margin-bottom: 16px;
  }

  .Information .main_left {
    width: 120px;
    line-height: 36px;
    text-align: right;
    float: left;
    color: #999999;
  }

  .Information .main_right {
    float: left;
    min-width: 148px;
    line-height: 36px;
    padding-left: 10px;
  }

  .el-dialog {
    min-width: 620px;
  }
</style>

