<link rel="stylesheet" href="/css/o-common.css" />
<div class="layui-fluid">
  <div id="medicalexaminationequ" v-cloak style="height: 100%">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>体检设备</span>
      </div>
      <div class="mb-4">
        <el-button type="primary" size="small" @click="openQrCodeDialog()"
          >生成设备绑定二维码</el-button
        >
      </div>
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item label="设备编号">
          <el-input
            v-model="searchForm.deviceCode"
            placeholder="请输入设备编号"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <div>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="device_id" label="设备编号"></el-table-column>
          <!-- <el-table-column prop="brand" label="设备品牌"></el-table-column> -->
          <el-table-column prop="device_name" label="设备名"></el-table-column>
          <el-table-column prop="device_type" label="设备类型">
            <template slot-scope="scope">
              <div
                class="o-tag"
                :class="getDeviceTypeColor(scope.row.device_type)"
              >
                {{getDeviceTypeName(scope.row.device_type)}}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="openQrCodeDialog(scope.row.device_id)"
              >
                设备二维码
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="handleModify(scope.row.id)"
              >
                修改
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="delLabelClass(scope.row.id)"
                >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div
          class="block"
          style="margin-top: 20px; overflow: hidden"
          v-if="total>0"
        >
          <el-pagination
            style="float: right"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 30, 40,100]"
            :page-size="limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </el-card>
    <el-dialog
      title="设备绑定二维码"
      :visible.sync="deviceQrCodeDialogVisible"
      :append-to-body="true"
      width="400px"
    >
      <div style="text-align: center">
        <!--  <div>
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="设备编号">
              <el-input
                v-model="qrCodeForm.deviceCode"
                placeholder="请输入设备编号"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleGenerateQrCode"
                >生成设备绑定二维码</el-button
              >
            </el-form-item>
          </el-form>
        </div> -->
        <div v-if="deviceQrCodeLoading" style="padding: 50px">
          <i class="el-icon-loading text-primary" style="font-size: 30px"></i>
          <p style="margin-top: 10px; color: #666">正在生成设备绑定二维码...</p>
        </div>
        <div v-else-if="deviceQrCode" style="padding: 20px">
          <img
            :src="deviceQrCode"
            alt="生成设备绑定二维码"
            style="
              max-width: 100%;
              height: auto;
              border: 1px solid #eee;
              border-radius: 4px;
            "
          />
          <div style="margin-top: 15px">
            <el-button type="primary" size="small" @click="downloadQrCode">
              下载设备绑定二维码
            </el-button>
          </div>
        </div>
        <div v-else-if="qrCodeGenerateAttempted" style="padding: 50px">
          <i
            class="el-icon-warning"
            style="font-size: 30px; color: #e6a23c"
          ></i>
          <p style="margin-top: 10px; color: #666">
            设备绑定二维码生成失败，请重试
          </p>
        </div>
        <div v-else style="padding: 30px; color: #909399">
          <p>请输入设备编号并点击生成按钮</p>
        </div>
      </div>
    </el-dialog>
  </div>
</div>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/util/enum.js?v=1.2.1"
></script>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#medicalexaminationequ",
      data() {
        return {
          searchForm: {
            deviceCode: "",
          },
          qrCodeForm: {
            deviceCode: "",
          },
          tableData: [],
          total: 0,
          page: 1,
          limit: 10,
          deviceQrCodeDialogVisible: false,
          deviceQrCode: "",
          deviceQrCodeLoading: false,
          qrCodeGenerateAttempted: false,
        };
      },
      methods: {
        getDeviceTypeName(device_type) {
          return window.MyEnums.getDeviceTypeName(device_type);
        },
        getDeviceTypeColor(device_type) {
          return window.MyEnums.getDeviceTypeColor(device_type);
        },
        openQrCodeDialog(deviceId) {
          this.deviceQrCodeDialogVisible = true;
          this.deviceQrCode = "";
          this.qrCodeGenerateAttempted = false;

          // 如果传入了设备ID，则自动填充到表单中
          if (deviceId) {
            this.qrCodeForm.deviceCode = deviceId;
            // 自动生成二维码
            this.$nextTick(() => {
              this.handleGenerateQrCode();
            });
          } else {
            // 如果没有传入设备ID，则清空表单
            this.qrCodeForm.deviceCode = "";
          }
        },
        getTableData() {
          var vm = this;
          $.ajax({
            url: "{:url('device/getDeviceList')}",
            data: {
              page: vm.page,
              limit: vm.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.total = res.count;
                vm.tableData = res.data;
              } else {
                vm.$message.error(res.msg || "获取设备列表失败");
              }
            },
            error: function () {
              vm.$message.error("网络错误，请稍后重试");
            },
          });
        },
        handleSearch() {
          this.page = 1;
          this.getTableData();
        },
        handleSizeChange(val) {
          this.page = 1;
          this.limit = val;
          this.getTableData();
        },
        handleCurrentChange(val) {
          this.page = val;
          this.getTableData();
        },
        handleGenerateQrCode() {
          if (!this.qrCodeForm.deviceCode) {
            this.$message.warning("请输入设备编号");
            return;
          }

          var vm = this;
          vm.deviceQrCodeLoading = true;
          vm.deviceQrCode = "";
          vm.qrCodeGenerateAttempted = true;

          $.ajax({
            url: "{:url('device/getDeviceCode')}",
            data: {
              deviceid: vm.qrCodeForm.deviceCode,
              page: "pages/index/index",
            },
            type: "POST",
            success: function (res) {
              vm.deviceQrCodeLoading = false;

              // 处理后端可能直接echo json字符串的情况
              if (typeof res === "string") {
                try {
                  res = JSON.parse(res);
                } catch (e) {
                  vm.$message.error("解析响应数据失败");
                  return;
                }
              }

              if (res.code == 1 && res.data) {
                vm.deviceQrCode = res.data;
              } else {
                vm.$message.error(res.msg || "生成二维码失败");
              }
            },
            error: function () {
              vm.deviceQrCodeLoading = false;
              vm.$message.error("网络错误，请稍后重试");
            },
          });
        },
        downloadQrCode() {
          if (!this.deviceQrCode) {
            this.$message.warning("请先生成二维码");
            return;
          }

          // 创建一个临时的a标签用于下载
          const link = document.createElement("a");
          link.href = this.deviceQrCode;
          link.download =
            "设备绑定二维码_" + this.qrCodeForm.deviceCode + ".png";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        handleModify(id) {
          // 修改设备信息的方法（待实现）
          console.log("修改设备ID:", id);
        },
        delLabelClass(id) {
          var vm = this;
          this.$confirm("确认删除该设备?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('device/deleteDevice')}",
                data: {
                  id: id,
                },
                type: "POST",
                success: function (res) {
                  if (res.code == 1) {
                    vm.$message.success("删除成功");
                    vm.getTableData();
                  } else {
                    vm.$message.error(res.msg || "删除失败");
                  }
                },
                error: function () {
                  vm.$message.error("网络错误，请稍后重试");
                },
              });
            })
            .catch(() => {
              // 取消删除操作
            });
        },
      },
      created: function () {
        this.getTableData();
      },
    });
  });
</script>
