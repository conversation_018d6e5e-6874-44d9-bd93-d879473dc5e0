<?php
/**
 * Created by <PERSON>p<PERSON><PERSON><PERSON>.
 * User: Hujun
 * 4/10 0010
 * Time: 14:41
 */

namespace app\store\controller;

use app\store\model\Member as MemberModel;
use app\store\model\MemberCardDetail;
use app\store\model\Membercopy;
use app\store\model\Membersource;
use app\store\model\Membertag;
use app\store\model\Staff as StaffModel;
use app\store\model\Position as PositionModel;
use app\store\model\Comnum as ComnumModel;
use app\store\model\Membertag as MembertagModel;
use app\store\model\Createstore as CreatestoreModel;
use app\store\model\Memberservicelog as MemberservicelogModel;
use app\store\model\Memberlevel as MemberlevelModel;
use app\store\model\MemberBalanceRecord as MemberBalanceRecordModel;
use app\store\model\Membergroplog as MembergroplogModel;
use app\store\model\MemberCard as MemberCardModel;
use app\store\model\MemberCardDetail as MemberCardDetailModel;
use app\store\model\Card as CardModel;
use app\store\model\PayOrder as payOrderModel;
use app\store\model\Membercopy as MembercopyModel;
use app\store\model\Memberimport as MemberimportModel;
use app\store\model\Membersource as MembersourceModel;
use app\store\model\Couponrecord as CouponrecordModel;
use app\store\model\Memberscorelog as MemberscorelogModel;
use app\store\model\MedicalRecord as MedicalRecordModel;
use app\store\model\Memberclub as MemberClubModel;
use app\api\model\UserBindStore as UserBindStoreModel;
use PHPExcel_IOFactory;
use think\Db;
use think\Request;
use think\Session;
use app\pagecommon\controller\Upload as UploadCtrl;
use app\store\model\Memberarchivesset as MemberarchivessetModel;//会员自定义字段设置
use app\pagecommon\controller\Upload;

use app\api\model\Device as DeviceModel;

//<!--[meiye_07_27]-->
/**
 * 会员管理控制器
 * Class Member
 * @package app\store\controller
 */
class Member extends Controller
{

    public function uploadFile()
    {
        try {
            $post = input('post.');
            $file = new Upload;
          
            $list = $file->Image();

            if ($list['code']==1) {
                $data = array(
                    'id' => $list['data']['id'],
                    'file_path' => $list['data']['file_path']
                );
                return $this->ajaxSuccess($data, 'ok');
            } else {
                return $this->ajaxFail('上传失败');
            }
        } catch (\Exception $e) {
            $error = '上传失败'.$e->__toString();
            return $this->ajaxFail($error);
        }
    }
    //查询门店
    public function queryStoreAll($merchantid)
    {
        $CreatestoreModel = new CreatestoreModel;
        //查询门店
        $where['merchantid'] = $merchantid;
        $where['status'] = array("NEQ", 3);
        $field = array(
            'id',
            'storetag',
            'alias',
            'merchantid'
        );
        $storeAll = $CreatestoreModel->getAll($where, $field);
        if (!$storeAll) {
            return false;
        }
        return $storeAll;
    }

    //查询等级
    public function queryLevelAll($merchantid)
    {
        $MemberlevelModel = new MemberlevelModel;
        $wap['merchant_id'] = $merchantid;
        $field = array(
            'id',
            'level_name',
            'growth_value'
        );
        $order = 'growth_value asc';
        $memberLevel = $MemberlevelModel->getAll($wap, $field, $order);
        if (!$memberLevel) {
            return false;
        }
        return $memberLevel;
    }

    //查询来源
    public function querySourceAll($merchantid)
    {
        $MembersourceModel = new MembersourceModel;
        $where['merchant_id'] = array(
            array('eq', $merchantid),
            array('eq', 0),
            'OR'
        );
        $where['status'] = array('neq', 3);
        $field = array(
            'id',
            'source_name',
            'merchant_id'
        );
        $memberSource = $MembersourceModel->getAll($where, $field);
        if (!$memberSource) {
            return false;
        }
        return $memberSource;
    }

    //查询标签
    public function queryTagAll($merchantid)
    {
        $MembertagModel = new MembertagModel;
        $map['merchant_id'] = $merchantid;
        $field = array(
            'id',
            'tag_name',
            'merchant_id'
        );
        $tagAll = $MembertagModel->getTag($map, $field);
        if (!$tagAll) {
            return false;
        }
        return $tagAll;
    }


    public function index()
    {
        $user = Session('userinfo');//
        $this->assign('store', $user['storeid']);
        $merchantid = $this->getmerchantid();
        $storeList = $this->queryStoreAll($merchantid);         // 查询当前商户所有门店列表
        $levelList = $this->queryLevelAll($merchantid);         // 查询会员等级列表
        $sourceList = $this->querySourceAll($merchantid);       // 查询会员来源列表
        $tagList = $this->queryTagAll($merchantid);             // 查询会员所有标签

        $this->assign('storelist', $storeList);
        $this->assign('member_level', $levelList);
        $this->assign('member_source', $sourceList);
        $this->assign('membertag', $tagList);
        return $this->outPut();
    }

    public function vip()
    {
        $user = Session('userinfo');//
        $this->assign('store', $user['storeid']);
        $merchantid = $this->getmerchantid();
        $storeList = $this->queryStoreAll($merchantid);         // 查询当前商户所有门店列表
        $levelList = $this->queryLevelAll($merchantid);         // 查询会员等级列表
        $sourceList = $this->querySourceAll($merchantid);       // 查询会员来源列表
        $tagList = $this->queryTagAll($merchantid);             // 查询会员所有标签

        $this->assign('storelist', $storeList);
        $this->assign('member_level', $levelList);
        $this->assign('member_source', $sourceList);
        $this->assign('membertag', $tagList);
        return $this->outPut();
    }

	//<!--[meiye_10_13]-->
    public function getMemberLevel(){
    	return $this->ajaxSuccess($this->queryLevelAll($this->userinfo['merchantid']));
	}
    //查询新客归属
    public function getAdviser()
    {
        $merchant_id = $this->getmerchantid();
        $StaffModel = new StaffModel;
        $PositionModel = new PositionModel;
        $map['merchantid'] = array(
            array('eq', $merchant_id),
            array('eq', 0),
            'OR'
        );
        $map['position_name'] = array('eq', '新客归属');
        $position = $PositionModel->getFind($map);
        if (isset($position['id']) && !empty($position['id'])) {
            $wap['merchantid'] = $merchant_id;
            $wap['status'] = 1;
            $wap['positionids'] = array(
                array("like", $position['id'] . ",%"),
                array("like", "%," . $position['id']),
                array("like", "%," . $position['id'] . ",%"),
                array("eq", $position['id']),
                "OR"
            );
            $field = 'id,nickname,positionids';
            $adviser = $StaffModel->getAll($wap, $field);
            if ($adviser) {
                return $this->ajaxSuccess($adviser, 'ok');
            } else {
                return $this->ajaxFail('获取失败');
            }
        } else {
            return $this->ajaxFail('数据出错');
        }
    }

    /**
     * 会员列表
     * @return array
     */
    public function getMemberList()
    {
        $user = Session('userinfo');
        $model = new MemberModel;
        $post = input('post.');
        $where = array();
        $where['merchantid'] = $user['merchantid'];//商户 id
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        }
        //会员名称、备注名、会员编号、手机号筛选
        if ($post['member_name'] != '') {
            $where['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['member_name'] . '%');
        }
        if (isset($post['keyword'])&&$post['keyword'] != '') {
            $uwhere['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['keyword'] . '%');
            if ($user['storeid'] != 0) {
            $ubswhere['storeid']=$user['storeid'];
            $ubsfield="userid";
            $UserBindStoreModel=new UserBindStoreModel;
            $userlist=$UserBindStoreModel->getfieldList($ubswhere, $ubsfield);
            $uwhere['id'] = ['in', $userlist];//
            }
    
            $ubsfield="id";
            $MemberModel =new MemberModel;
            $memberlist=$MemberModel->getfieldList($uwhere, $ubsfield);
            $where['id'] = ['in', $memberlist];//
        }
            
        $last = $post['last_time'];
        $score1 = $post['score1'];
        $score2 = $post['score2'];
        //消费频次筛选
        if ($last != '') {
            $monthday = strtotime('-' . $last . 'months');//一个月之内的数据
            $where['last_time'] = array('elt', $monthday);
        }
        if (!empty($post['z_last_time'])) {
            $where['last_time'] = array('elt', $post['z_last_time']);
        }
        if (isset($post['isVip']) && $post['isVip']==1) {
            $where['is_vip'] = 1;
        }
        if (isset($post['is_vip'])&&$post['is_vip']!='') {
            $where['is_vip'] = $post['is_vip'];
    
        }
        //生日
        $birthday = $post['birthday'];
        switch ($birthday) {
//            case 0:  //全部
//                $birthday = '';
//                break;
            case 1:  //今天
                $birthday = date('m-d');
                break;
            case 2:  //明天
                $birthday = date("m-d", strtotime("+1 day"));
                break;
            case 3:  //本周
                $birthday1 = date("m-d");//今天
                $birthday2 = date("m-d", strtotime("last day next week +1 day"));//本周最后一天
                break;
            case 4:  //本月
                $birthday1 = date("m-d");//今天
                $birthday2 = date("m-d", mktime(23, 59, 59, date('m'), date('t'), date('Y')));//本月最后一天
                break;
            case 5:  //下月
                $birthday1 = date("m-d", mktime(0, 0, 0, date("m") + 1, 1, date("Y")));//下月1号
                $birthday2 = date("m-d", mktime(23, 59, 59, date("m") + 2, 0, date("Y")));//下月31号
                break;
        }
        //生日筛选
        if ($birthday != '') {
            $where['birthday'] = array('eq', $birthday);
        }
        if (!empty($birthday1) && !empty($birthday2)) {
            $where['birthday'] = array('between', array($birthday1, $birthday2));
        }
        if (!empty($post['starttime']) && !empty($post['endtime'])) {
            $starttime = date('m-d', strtotime($post['starttime']));
            $endtime = date('m-d', strtotime($post['endtime']));
            $where['birthday'] = array('between', array($starttime, $endtime));
        }
        if (!empty($post['adviser'])) { //新客归属
            $where['adviser'] = array('eq', $post['adviser']);
        }
        //消费次数
        if ($post['counts'] != '') {
            $where['count'] = array('elt', $post['counts']);
        }
        if ($post['z_count'] != '') {
            $where['count'] = array('elt', $post['z_count']);
        }
        //积分区间筛选
        if ($score1 != '' && $score2 != '') {
            $where['score'] = array(array('egt', $score1), array('elt', $score2));
        }
        //门店筛选
        if (!empty($post['store'])) {
            $where['store_id'] = array('eq', $post['store']);
        }
        //来源筛选
        if (!empty($post['source'])) {
            $where['member_source'] = array('eq', $post['source']);
        }
        //查询等级
        $mod = new MemberlevelModel;
        if (!empty($post['level'])) {
            $wh['id'] = $post['level'];
            $level = $mod->getMemberLevel($wh);
            if ($level != '') {
                $low = $level[0]['growth_value'];
                $Rd['growth_value'] = array('gt', $low);
                $Wq = $mod->getMemberLevel($Rd);
                if (count($Wq) > 0) {
                    $high = $Wq[0]['growth_value'];
                } else {
                    $high = '';
                }
                if ($high != '') {
                    $where['growth_value'] = array(array('egt', $low), array('lt', $high));
                } else {
                    $where['growth_value'] = array('egt', $low);
                }
            }
        }
        // 根据标签筛选
        if (!empty($post['tag'])) {
            $tab = $post['tag'];
            $where['tab_id'] = array(
                array('like', '%,' . $tab),
                array('like', $tab . ',%'),
                array('like', '%,' . $tab . ',%'),
                array('eq', $tab),
                'OR'
            );
        }
        $count = $model->getCount($where);
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $append = array('card_num', 'adviserName', 'sourceName', 'Grade');//卡项数量
        $list = $model->getMemberAll($where, $append);
        $storeModel = new CreatestoreModel;
        foreach ($list as $k => &$v) {
            $v['addtime'] = date('Y-m-d H:i:s', $v['addtime']);
            $v['total'] = number_format($v['total'] / 100, 2,'.','');
            $v['balance'] = number_format($v['balance'] / 100, 2,'.','');
            if (!empty($v['birthday_year']) && !empty($v['birthday'])) {
                $v['birthday'] = $v['birthday_year'] . '-' . $v['birthday'];
            } else {
                $v['birthday'] = '--';
            }
            //距离上次消费
            if (!empty($v['last_time'])) {
                $today = time();
                $cnt = $today - $v['last_time'];//与已知时间的差值
                $v['last_time'] = floor($cnt / (3600 * 24)) . ' 天';
            } else {
                $v['last_time'] = '--';
            }
            //查询门店
            $store_id['id'] = $v['store_id'];
            $store = $this->getStoreInfo($v['store_id']);
            if ($store) {
                $v['store_id'] = $store['storetag'];
            } else {
                $v['store_id'] = '总部';
            }
        }
        if ($list !== false) {
            return $this->ajaxTable($list, $count, '获取成功');
        } else {
            $error = $model->getError() ?: '获取错误';
            return $this->renderError($error);
        }
    }

    /**
     * 添加&修改会员入口
     * @return mixed
     */
    public function member()
    {
        $user = Session('userinfo');//获取当前总店信息
        $storeid = $user['storeid'];
        $this->assign('storeid', $storeid);
        $merchantid = $user['merchantid'];
        $comnum = new ComnumModel;//自动生成编号
        $member_number = $comnum->memberNum($merchantid);
        $this->assign('memberNumber', $member_number);

        $storeList = $this->queryStoreAll($merchantid);         // 查询当前商户所有门店列表
        $levelList = $this->queryLevelAll($merchantid);         // 查询会员等级列表
        $sourceList = $this->querySourceAll($merchantid);       // 查询会员来源列表

        $StaffModel = new StaffModel;
        $PositionModel = new PositionModel;

        $map['merchantid'] = array('eq', 0);

        $position = $PositionModel->getFind($map);
        if ($position===false ){
          return $this->ajaxFail('未找到数据');
        }
        $wap['positionids'] = array(
            array("like", $position['id'] . ",%"),
            array("like", "%," . $position['id']),
            array("like", "%," . $position['id'] . ",%"),
            array("eq", $position['id']),
            "OR"
        );


        $wap['merchantid'] = $merchantid;
        $wap['storeid'] = $storeid;
        $wap['status'] = 1;

        $field = array(
            'id',
            'nickname',
            'avatar',
            'groupid',
            'positionids'
        );
        $append = array('StaffImg','groupName');
        $Stafflist = $StaffModel->getAll($wap,$field,$append);
        $this->assign('customerBelong', $Stafflist);

        // // TODO 新客归属哪个业务员
        // $customerBelong = [
        // ['label' => '张三', 'id' => 1],
        // ['label' => 'abc', 'id' => 2]
        // ];
        // $this->assign('customerBelong', json_encode($customerBelong));

        $this->assign('storelist', $storeList);
        $this->assign('member_level', $levelList);
        $this->assign('member_source', $sourceList);
        return $this->outPut('member');
    }

    // 查询一条会员信息
    public function getMemberFind()
    {
        $id = input('post.id');
        $merchant_id = $this->getmerchantid();
        $MemberlevelModel = new MemberlevelModel;
        $MemberModel = new MemberModel;
        $where['id'] = $id;
        $list = $MemberModel->getFind($where);
        if (!empty($list)) {
            if (!empty($list['birthday']) && !empty($list['birthday_year'])) {
                $list['birthday'] = $list['birthday_year'] . '-' . $list['birthday'];
            } else {
                $list['birthday'] = '';
            }
            if ($list['store_id'] == 0) {
                $list['store_id'] = '总部';
            }
        }
        //查询等级
        $map['merchant_id'] = $merchant_id;
        $map['growth_value'] = array('elt', $list['growth_value']);
        $field = '*';
        $order = 'growth_value desc';
        $member_level = $MemberlevelModel->getAll($map, $field, $order); //查询小于
        if (count($member_level) > 0) {
            $min = $member_level[0]['growth_value'];
        } else {
            $min = 0;
        }
        $wap['growth_value'] = array('eq', $min);
        $wap['merchant_id'] = $merchant_id;
        $member = $MemberlevelModel->getFind($wap);//查询所属等级
        $list['growth'] = $member['level_name'];
        if ($list) {
            return $this->ajaxSuccess($list, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    /**
     * 添加修改会员
     * @return mixed
     */
    public function adMember()
    {
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        $model = new MemberModel;
        $Memberlevel = new MemberlevelModel;
        $comnum = new ComnumModel;//自动生成编号
        if (!empty($post['birthday'])) {
            $birthday_year = date('Y', strtotime($post['birthday']));
            $birthday = date('m-d', strtotime($post['birthday']));
        } else {
            $birthday_year = '';
            $birthday = '';
        }
        if (empty($post['member_number'])) {
            $member_number = $comnum->memberNum($merchant_id);
        } else {
            $where['member_number'] = $post['member_number'];
            $member = $model->getFind($where);
            if (!$member) {
                $member_number = $post['member_number'];
            } else {
                return $this->ajaxFail('会员编号重复，请刷新');
            }
        }
        $phone = $post['phone'];
        //判断手机号是否存在
        if (!empty($phone)) {
            $map['phone'] = $phone;
            $map['merchantid'] = $merchant_id;
            $u = $model->getFind($map);
            // var_dump( $u);
            // exit();
            if ($u) {
                $ubs=new UserBindStoreModel();
                $bindata['userid']= $u['id'];
                $bindata['storeid']= $post['store_id'];
                $bindata['merchantid']=$merchant_id;
                $ubs->Add($bindata);
                return $this->ajaxSuccess('添加复用成功');
                // return $this->ajaxFail('该手机号已被注册');
            }
            $storeid = $post['store_id'];
            $wap['growth_value'] = $post['growth_value'];
            $wap['merchant_id'] = $merchant_id;
            $fie = 'id';
            $Level = $Memberlevel->getFind($wap, $fie);

            $data = substr($phone, -6, 6);
            $time = time();
            $key = date('YmdHis', $time);
            $password = encrypt($data, $key);
            if($post['is_vip']==1){
                $post['growth_value'] = $post['growth_value']>0?$post['growth_value']:1;
            }
            $data = array(
                'merchantid' => $merchant_id,
                'member_name' => $post['member_name'],
                'remarks_name' => $post['remarks_name'],
                'sex' => $post['sex'],
                'phone' => $phone,
                'birthday_year' => $birthday_year,
                'birthday' => $birthday,
                'member_number' => $member_number,
                'member_source' => $post['member_source'],
                'level_id' => $Level['id'], // 等级id
                'growth_value' => $post['growth_value'],
                'wechat_number' => $post['wechat_number'],
                'store_id' => $storeid,
                'remarks' => $post['remarks'],
                'address' => $post['address'],
                'password' => $password,
                'addtime' => $time,
                'is_vip'=>$post['is_vip']
            );
            $lst = $model->addMember($data);
            if ($lst) {
                SendPass($phone, $merchant_id, $storeid);// 发送手机短信
                $res['status'] = 1;
                $res['msg'] = '添加成功';
            } else {
                $res['status'] = 0;
                $res['msg'] = '添加失败';
            }
            return $res;
        } else {
            return $this->ajaxFail('手机号错误');
        }
    }

    /**
     * 删除会员 && 批量删除
     * @return mixed
     */
    public function delMember()
    {
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        if (is_array($post['id'])) {
            $where['id'] = array('in', $post['id']);
        } else {
            $where['id'] = $post['id'];
        }
        $where['merchantid'] = $merchant_id;
        $MemberModel = new MemberModel;
        $list = $MemberModel->delMember($where);
        if ($list) {
            return $this->ajaxSuccess($list, '删除成功');
        } else {
            return $this->ajaxFail('删除失败');
        }
    }

    /**
     * 获取标签列表
     * @return array
     */
    public function getTabList()
    {
        $MembertagModel = new MembertagModel;
        $merchant_id = $this->getmerchantid();
        $where['merchant_id'] = $merchant_id;
        $list = $MembertagModel->getTag($where);
        if ($list) {
            return $this->ajaxSuccess($list, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    /**
     * 添加标签
     * @return mixed
     */
    public function adTab()
    {
        $post = input('post.');
        $user = Session('userinfo');
        $MembertagModel = new MembertagModel;
        $where['tag_name'] = $post['tag_name'];
        $where['merchant_id'] = $user['merchantid'];
        $count = $MembertagModel->getCount($where);
        if ($count > 0) {
            return $this->ajaxFail('该标签已存在，请更换名称');
        }
        $data = array(
            'merchant_id' => $user['merchantid'],
            'tag_name' => $post['tag_name'],
            'admin' => $user['nickname'],
            'section' => $user['storetag'],
            'addtime' => time(),
        );
        $list = $MembertagModel->addTag($data);
        if ($list) {
            return $this->ajaxSuccess($list, '添加成功');
        } else {
            return $this->ajaxFail('添加失败');
        }
    }
    /**
     * 添加标签
     * @return mixed
     */
    public function editTabitem()
    {
        $post = input('post.');
        $user = Session('userinfo');
        $MembertagModel = new MembertagModel;
        $where['tag_name'] = $post['tag_name'];
        $where['merchant_id'] = $user['merchantid'];
        $where['id'] = array('neq',$post['id']);
        $count = $MembertagModel->getCount($where);
        if ($count > 0) {
            return $this->ajaxFail('该标签已存在，请更换名称');
        }
        $list = $MembertagModel->where(['id'=>$post['id']])->setField('tag_name',$post['tag_name']);
        if ($list) {
            return $this->ajaxSuccess($list, '编辑成功');
        } else {
            return $this->ajaxFail('编辑失败');
        }
    }

    /**
     * 批量设置标签
     * @return array
     */
    public function setTab()
    {
        $post = input('post.');
        $MemberModel = new MemberModel;
        $merchant_id = $this->getmerchantid();
        $Vipids = $post['ids'];
        $dynamicTags = $post['dynamicTags'];
        if (is_array($Vipids)) {
            $vipidStr = "";
            foreach ($Vipids as $k => &$v) {
                $vipidStr .= $v . ',';
            }
            $vipidSub = substr($vipidStr, 0, -1);
            $where['id'] = array('in', $vipidSub);
        } else {
            $where['id'] = $Vipids;
        }
        $where['merchantid'] = $merchant_id;
        $tabArr = [];
        foreach ($dynamicTags as $k => &$v) {
            $tabArr[] = $v['id'];
        }
        $field = 'id,tab_id';
        $memberTag = $MemberModel->getAll($where, $field);
        foreach ($memberTag as $k => &$v) {
            if ($v['tab_id'] != '') {
                $tav = explode(',', $v['tab_id']);
                $newTagArr = array_unique(array_merge($tabArr, $tav));
                $newStr = implode(",", $newTagArr);
            } else {
                $newStr = implode(",", $tabArr);
            }
            $data['tab_id'] = $newStr;
            $wap['id'] = $v['id'];
            $wap['merchantid'] = $merchant_id;
            $list = $MemberModel->editMember($wap, $data);
        }
        if ($list !== false) {
            return $this->ajaxSuccess($list, '批量设置标签成功');
        } else {
            return $this->ajaxFail('添加失败');
        }
    }

    // 会员详情修改标签
    public function editTab()
    {
        $post = input('post.');
        $MemberModel = new MemberModel;
        $merchant_id = $this->getmerchantid();
        $dynamicTags = $post['dynamicTags'];
        $where['id'] = $post['id'];
        $where['merchantid'] = $merchant_id;
        $tabArr = [];
        foreach ($dynamicTags as $k => &$v) {
            $tabArr[] = $v['id'];
        }
        $data['tab_id'] = implode(",", $tabArr);
        $list = $MemberModel->editMember($where, $data);
        if ($list !== false) {
            return $this->ajaxSuccess($list, '设置标签成功');
        } else {
            return $this->ajaxFail('添加失败');
        }
    }


    /**
     * 详情页
     * @param $name
     * @param $id
     */
    public function det($name, $id)
    {
        if (!empty($id)) {
            $this->assign('id', $id);
        } else {
            return;
        }
        $user = Session('userinfo');//获取当前总店信息
        $merchantid = $user['merchantid'];
        $this->assign('storeid', $user['storeid']); //门店id
        $storeList = $this->queryStoreAll($merchantid);         // 查询当前商户所有门店列表
        $levelList = $this->queryLevelAll($merchantid);         // 查询会员等级列表
        $sourceList = $this->querySourceAll($merchantid);       // 查询会员来源列表

        $this->assign('storelist', $storeList);
        $this->assign('member_level', $levelList);
        $this->assign('member_source', $sourceList);

        // TODO 查询会员职称列表
        $where = array(
            'merchantid'=>$user['merchantid'],
        );
        $MemberClubModel = new MemberClubModel;
        $jobNames =$MemberClubModel->getDataAll($where);
        $this->assign('jobNames', $jobNames);

        $this->outPut('member/details');
    }


    /**
     * 详情页修改
     * @return mixed
     */
    public function edMember()
    {
        $post = input('post.');
        $model = new MemberModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        //修改
        $where['id'] = $post['id'];
        // TODO 添加会员职称jobName
        $data = array(
            'member_name' => $post['member_name'],
            'remarks_name' => $post['remarks_name'],
            'member_number' => $post['member_number'],
            'member_source' => $post['member_source'],
            'growth_value' => $post['growth_value'],
            'jobName' => $post['jobName'],
            'sex' => $post['sex'],
	    'store_id' => $post['store_id'],
	);
        $lst = $model->editMember($where, $data);
        if ($lst) {
            $res['status'] = 1;
            $res['msg'] = '修改成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = '修改失败';
        }
        return $res;
    }

    /**
     * 详情页基本信息修改
     * @return mixed
     */
    public function editMem()
    {
        $post = input('post.');
        $model = new MemberModel;
        $merchant_id = $this->getmerchantid();
        //修改
        $where['id'] = $post['id'];
        $where['merchantid'] = $merchant_id;
        if (!empty($post['birthday'])) {
            $birthday_year = date('Y', strtotime($post['birthday']));
            $birthday = date('m-d', strtotime($post['birthday']));
        }
        $data = array(
            'wechat_number' => $post['wechat_number'],
            'address' => $post['address'],
            'birthday_year' => $birthday_year,
            'birthday' => $birthday,
            'remarks' => $post['remarks'],
        );
        $lst = $model->editMember($where, $data);
        if ($lst) {
            $res['status'] = 1;
            $res['msg'] = '修改成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = '修改失败';
        }
        return $res;
    }

    /**
     * 获得数据
     * @return array
     */
    public function getDetFind()
    {
        $uid = input('post.id');
        $where['id'] = $uid;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $model = new MemberModel;
//        $memLevel = new MemberlevelModel;
        $field = '*';
        $append = array('card_num', 'equity_num', 'vip_balance', 'coupon_num', 'sourceName','jobName');
        $list = $model->getFind($where, $field, $append);
        if (!$list) {
            return $this->ajaxFail('该数据不存在或已被删除');
        }
        $list['card_balance'] = number_format(($list['vip_balance'] - $list['balance']) / 100, 2, '.', '');
        $list['balance'] = number_format($list['balance'] / 100, 2, '.', '');
        $list['vip_balance'] = number_format($list['vip_balance'] / 100, 2, '.', '');
        $list['total'] = number_format($list['total'] / 100, 2, '.', '');
        $list['debt'] = number_format(($list['total_debt']-$list['also_debt']) / 100, 2, '.', '');

        if (!empty($list['addtime'])) {
            $list['addtime'] = date('Y-m-d H:i:s', $list['addtime']);
        }
        if (!empty($list['birthday']) && !empty($list['birthday_year'])) {
            $list['birthday'] = $list['birthday_year'] . '-' . $list['birthday'];
        }
        //查询标签
        $tab['id'] = array('in', $list['tab_id']);
        $tab['merchant_id'] = $merchant_id;
        $tabModel = new MembertagModel;
        $field = '*';
        $tabs = $tabModel->getTag($tab, $field);
        $list['tabInfo'] = $tabs;
        //查询门店
        $storeModel = new CreatestoreModel;
        $store_id['id'] = $list['store_id'];
        $store_id['merchantid'] = $merchant_id;
        $store = $storeModel->getFind($store_id);
        if ($store) {
            $list['store_id'] = $store['storetag'];
        } else {
            $list['store_id'] = '总部';
        }
        //基本档案
        $ArchivesInfo = $this->archivesAet($uid);
        if ($ArchivesInfo) {
            $list['archivesInfo'] = $ArchivesInfo;
        }

        if ($list) {
            return $this->ajaxSuccess($list);
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    /**
     * 查询会员自定义字段设置（自定义会员档案设置）
     */
    public function archivesAet($uid)
    {
        $MemberarchivessetModel = new MemberarchivessetModel;
        $merchantid = $this->getmerchantid();
        $where['merchantid'] = $merchantid;
        $where['uid'] = $uid;
        $field = array(
            'id',
            'merchantid',
            'field_name',
            'field_type',
            'option',
            'sort',
        );
        $order = 'sort desc';
        $append = array('memberInfo');
        $list = $MemberarchivessetModel->getAll($where, $field, $order, $append);

        foreach ($list as $k=>&$v) {
            if ($v['field_type']==2 || $v['field_type']==3){
                $option = htmlspecialchars_decode($v['option']);
                $v['option'] = json_decode($option, true);
                if (isset($v['memberInfo']['keywords'])){
                    $keywords = htmlspecialchars_decode($v['memberInfo']['keywords']);
                    $v['memberInfo']['keywords'] = json_decode($keywords, true);
                    $str = '';
                    foreach ($v['memberInfo']['keywords'] as $kk=>&$vv) {
//                        array_push($arr,$vv['name']);
                        $str .= $vv['name'].'、';
                    }
                    $substr = substr($str,0,-3);
                    $v['memberInfo']['keywords'] = $substr;
                }
            }
        }
        if ($list===false){
            return $this->ajaxFail('获取失败');
        }
        return $list;
    }

    //获得服务日志
    public function getServiceLog()
    {
        $model = new MemberservicelogModel;
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        $where = array(
            'uid' => $post['id'],
            'merchantid' => $merchant_id,
            'status' => 1,
        );
        $count = $model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $field = '*';
        $order = 'addtime desc';
        $append = array('imgArr', 'serviceInfo', 'staffInfo', 'orderInfo');
        $list = $model->getServiceLogs($where, $page, $field, $order, $append);

        if ($list !== false) {
            return array('code' => 1, 'msg' => 'ok', 'count' => $count, 'data' => $list);
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    /**
     * 增减积分
     * @return mixed
     */
    public function editScore()
    {
        $post = input('post.');
        $model = new MemberModel;
        $merchant_id = $this->getmerchantid();
        $where['id'] = $post['id'];
        $where['merchantid'] = $merchant_id;
        $count = $model->getFind($where);
        $score = $count['score'];

        $num = $score + $post['score'];
        if ($num < 0) {
            $res['status'] = 0;
            $res['msg'] = '用户当前积分不足以扣减，请重新输入！';
            return $res;
        }
        $data = array();
        $data['score'] = $num;
        $list = $model->editScore($where, $data);
        if ($list) {
            $res['status'] = 1;
            $res['msg'] = '积分修改成功';
        } else {
            $res['status'] = 0;
            $res['msg'] = '积分修改失败';
        }
        return $res;
    }

    //获取订单详情
    public function getOrderList()
    {
        $post = input("post.");
        $Model = new payOrderModel;
        $Staff = new StaffModel;
        $merchant_id = $this->getmerchantid();
        $where['merchant_id'] = $merchant_id;
        $where['vip_id'] = $post['vip_id'];//会员id
        $count = $Model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $page['ord'] = "order_time desc";
        $field = "*";

        $orderList = $Model->getList($where, $page, $field);
        foreach ($orderList as $k => &$v) {
            $v['receivable'] = number_format($v['receivable'] / 100, 2, '.', '');
            $v['order_time'] = date("Y-m-d H:i:s", $v['order_time']);
            if ($v['cashier_id'] != '') {
                $cashier['id'] = $v['cashier_id'];
                $cashierData = $Staff->getFind($cashier);
                $v['cashier'] = $cashierData['nickname'] ? $cashierData['nickname'] : "--";
            } else {
                $v['cashier'] = "--";
            }
            if ($v['state'] == 1) {
                $v['order_state'] = "待付款";
            } elseif ($v['state'] == 2) {
                $v['order_state'] = "待收货";
            } elseif ($v['state'] == 3) {
                $v['order_state'] = "已发货";
            } elseif ($v['state'] == 4) {
                $v['order_state'] = "已完成";
            } elseif ($v['state'] == 5) {
                $v['order_state'] = "已取消";
            }
        }
        if ($orderList) {
            return $this->ajaxTable($orderList, $count, 'ok');
        }
    }

    // TODO 电子病历分页
    public function getMedicalRecordList()
    {
        // // 录入时间
        // // 主诉
        // // 现病史
        // // 填写人
        // // 备注
        // $mockData = [
        //     [
        //         'id' => 1,
        //         'medicalRecord_createTime' => '2025-01-01',
        //         'medicalRecord_chiefComplaint' => '咽下梗阻感或进行性吞咽困难,咽下梗阻感或进行性吞咽困难,咽下梗阻感或进行性吞咽困难咽下梗阻感或进行性吞咽困难咽下梗阻感或进行性吞咽困难',
        //         'medicalRecord_historyOfPresentIllness' => '脾虚，枕骨后缘淤堵,咽下梗阻感或进行性吞咽困难咽下梗阻感或进行性吞咽困难咽下梗阻感或进行性吞咽困难,咽下梗阻感或进行性吞咽困难',
        //         'medicalRecord_createPersonName' => '王五',
        //         'medicalRecord_remarks' => '备注内容'
        //     ]
        // ];

        // return json([
        //     'code' => 1,
        //     'data' => $mockData,
        //     'count' => count($mockData)
        // ]);
        $userinfo = session('userinfo');
        $merchant_id = $userinfo['merchantid'];
        $storeid = $userinfo['storeid'];
        $model = new MedicalRecordModel;
        $post = input('post.');
        // $merchant_id = $this->getmerchantid();
        $where = array(
            'user_id' => $post['id'],
            'merchantid' => $merchant_id,
            'storeid' => $storeid,
        );

        $count = $model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $field = '*';
        $append = array();//imgArr
        $list = $model->getDataByPage($where, $page, $field, $append);
        if ($list !== false) {
            return array('code' => 1, 'msg' => 'ok', 'count' => $count, 'data' => $list);
        } else {
            $error = $model->error;
            return $this->ajaxFail('获取失败');
        }
    }

    // TODO 获取电子病历详情
    public function getMedicalRecordDetail()
    {
        // medicalRecord_createTime: "",
        // medicalRecord_chiefComplaint: "", // 主诉
        // medicalRecord_historyOfPresentIllness: "", // 现病史
        // medicalRecord_goingToHistory: "", // 即往史
        // medicalRecord_personalHistory: "", // 个人史
        // medicalRecord_familyHistory: "", // 家族史
        // medicalRecord_createPersonName: "", // 填写人
        // medicalRecord_remarks: "", // 备注
        // medicalRecord_imgsPath: [],
        // medicalRecord_imgsUrl: [],
    }


        // 设备列表
        public function getDeviceList()
        {
            $page = input('page');
            $limit = input('limit');
            $DeviceModel = new DeviceModel;
    
            $post = input('post.');
            $page = $post['page'];
            $limit = $post['limit'];
           
          
            $where = array(
                'user_id'=>$post['uid']
            );
            
           
            $count = $DeviceModel->getCount($where);
            $field = '*';
            $append = array();
            $newsList = $DeviceModel->getDataByPage($where,$page,$limit,$field,$append);
            $newsList = $newsList?$newsList:array();
            return $this->ajaxTable($newsList,$count);
        }
    // TODO 获取电子病历新建、修改

    // TODO 获取体检信息
    public function getExaminationInfoData()
    {
        $post = input('post.');
        $user_id=$post['uid'];
        // 查询收缩压的最新非零值
        $systolic = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('systolic', '>', 0)
        ->order('measure_time', 'desc')
        ->value('systolic')?: 0;

        $systolic_time = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('systolic', '>', 0)
        ->order('measure_time', 'desc')
        ->value('measure_time')?: 0;

        // 查询舒张压的最新非零值
        $diastolic = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('diastolic', '>', 0)
        ->order('measure_time', 'desc')
        ->value('diastolic')?: 0;

        $diastolic_time = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('diastolic', '>', 0)
        ->order('measure_time', 'desc')
        ->value('measure_time')?: 0;

        // 查询脉搏率的最新非零值
        $pulse = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('pulse', '>', 0)
        ->order('measure_time', 'desc')
        ->value('pulse')?: 0;

        $pulse_time = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('pulse', '>', 0)
        ->order('measure_time', 'desc')
        ->value('measure_time')?: 0;

        // 查询血糖的最新非零值
        $glucose = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('glucose', '>', 0)
        ->order('measure_time', 'desc')
        ->value('glucose')?: 0;

        $glucose_time = Db::name('blood_pressure')
        ->where('user_id', $user_id)
        ->where('glucose', '>', 0)
        ->order('measure_time', 'desc')
        ->value('measure_time')?: 0;


        $blood_pressure_status="";
        $blood_glucose_status="";
        // 根据血压分类标准判断血压状态
        if ($systolic < 120 && $diastolic < 80) {
            $blood_pressure_status = '正常血压';
        } elseif ($systolic >= 120 && $systolic <= 129 && $diastolic < 80) {
            $blood_pressure_status = '正常高值（偏高）';
        } elseif (($systolic >= 130 && $systolic <= 139) || ($diastolic >= 80 && $diastolic <= 89)) {
            $blood_pressure_status = '高血压（1级）';
        } elseif ($systolic >= 140 || $diastolic >= 90) {
            $blood_pressure_status = '高血压（2级）';
        } else {
            $blood_pressure_status = ' ';
        }

        // 根据血糖分类标准判断血糖状态
        if ($glucose >= 11.1) {
            $blood_glucose_status = '高血糖';
        } elseif ($glucose < 3.9) {
            $blood_glucose_status = '低血糖';
        } elseif($glucose ==0) {
            $blood_glucose_status = '';
        } else{
            $blood_glucose_status = '正常血糖';
        }




        $mockData = [
            [
                'title' => '血压',
                'type' => 'bloodPressure',
                'value' => $systolic.'/'.$diastolic,
                'unit' => 'mmHg',
                'danger' => $blood_pressure_status,
                'date' => $systolic_time,
            ],
            [
                'title' => '血氧',
                'type' => 'bloodOxygenSaturation',
                'value' => '0',
                'unit' => '%',
                'danger' => '',
                'date' => ' ',
            ],
            [
                'title' => '心率',
                'type' => 'heartRate',
                'value' => $pulse,
                'unit' => 'bpm',
                'danger' => '',
                'date' => $pulse_time,
            ],
            [
                'title' => '血糖',
                'type' => 'bloodSugar',
                'value' => $glucose,
                'unit' => 'mmol/L',
                'danger' => $blood_glucose_status,
                'date' => $glucose_time,
            ],
            [
                'title' => '身高',
                'type' => 'height',
                'value' => '0',
                'unit' => 'cm',
                'danger' => '',
                'date' => ' ',
            ],
            [
                'title' => '体重',
                'type' => 'weight',
                'value' => '0',
                'unit' => 'kg',
                'danger' => '',
                'date' => ' ',
            ],
        ];

        return json([
            'code' => 1,
            'data' => $mockData,
            'count' => count($mockData)
        ]);
    }
    // TODO 获取体检信息图表值分页
    public function getExaminationInfoChartData()
    {
        // 入参 type
        $type = input('post.type');


        if($type == 'bloodPressure'){

            $mockData = [
                [
                    "date" => "2023-01-01",
                    "value" => "120/80",
                ],
                [
                    "date"=> "2023-01-02",
                    "value"=> "130/70",
                ],
                [
                    "date" => "2023-01-03",
                    "value" => "111/60",
                ],
                [
                    "date" => "2023-01-04",
                    "value" => "140/90",
                ],
                [
                    "date" => "2023-01-05",
                    "value" => "120/80",
                ],
                [
                    "date" => "2023-01-06",
                    "value" => "130/70",
                ],
                [
                    "date" => "2023-01-07",
                    "value" => "111/60",
                ],
                [
                    "date" => "2023-01-08",
                    "value" => "50/40",
                ],
                [
                    "date" => "2023-01-09",
                    "value" => "130/70",
                ],
                [
                    "date" => "2023-01-10",
                    "value" => "90/40",
                ],
                [
                    "date" => "2023-01-11",
                    "value" => "140/90",
                ],
                [
                    "date" => "2023-01-12",
                    "value" => "224/80",
                ],
                [
                    "date" => "2023-01-13",
                    "value" => "130/70",
                ],
                [
                    "date" => "2023-01-14",
                    "value" => "111/60",
                ],
                [
                    "date" => "2023-01-15",
                    "value" => "140/90",
                ],
            ];
        }else{
            $mockData = [
                [
                    "date" => "2023-01-01",
                    "value" => "120",
                ],
                [
                    "date" => "2023-01-02",
                    "value" => "130",
                ],
                [
                    "date" => "2023-01-03",
                    "value" => "111",
                ],
                [
                    "date" => "2023-01-04",
                    "value" => "140",
                ],
                [
                    "date" => "2023-01-05",
                    "value" => "120",
                ],
                [
                    "date" => "2023-01-06",
                    "value" => "130",
                ],
                [
                    "date" => "2023-01-07",
                    "value" => "111",
                ],
                [
                    "date" => "2023-01-08",
                    "value" => "50",
                ],
                [
                    "date" => "2023-01-09",
                    "value" => "130",
                ],
                [
                    "date" => "2023-01-10",
                    "value" => "90",
                ],
                [
                    "date" => "2023-01-11",
                    "value" => "140",
                ],
                [
                    "date" => "2023-01-12",
                    "value" => "224",
                ],
                [
                    "date" => "2023-01-13",
                    "value" => "130",
                ],
                [
                    "date" => "2023-01-14",
                    "value" => "111",
                ],
                [
                    "date" => "2023-01-15",
                    "value" => "140",
                ],
            ];
        }

        return json([
            "code" => 1,
            "data" => $mockData,
            "count" => count($mockData)
        ]);

    }


    //余额页面
    public function balance($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/balance');
    }
    //余额页面
    public function balancelist()
    {
        $storeid = $this->userinfo['storeid'];
        $this->assign('storeid', $storeid);
        return $this->outPut('member/balancelist');
    }

    /**
     * 余额明细
     * @return array
     */
    public function GetBalanceList()
    {
        $post = input('post.');
        $model = new MemberBalanceRecordModel;
        $merchant_id = $this->getmerchantid();
        if(isset($post['uid'])){
            $where['member_id'] = $post['uid'];
        }
        if(isset($post['storeid']) && $post['storeid']){
            $where['store_id'] = $post['storeid'];
        }
        $where['merchant_id'] = $merchant_id;
        $count = $model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $field = array('*','member_card_id as cardName');
        if(!isset($post['uid'])){
            $field[] = "member_id as Buyer";
        }
        $order = 'addtime desc';
        $Balance = $model->getBalanceAll($where, $page, $field, $order);
        foreach ($Balance as $k => &$v) {
            //查询会员余额
            $v['total'] = number_format($v['total'] / 100, 2, '.', '');
            $v['capital_balance'] = number_format($v['capital_balance'] / 100, 2, '.', '');
            $v['present_balance'] = number_format($v['present_balance'] / 100, 2, '.', '');
        }
        if ($Balance) {
            return $this->ajaxTable($Balance, $count, 'ok');
        } else {
            return $this->ajaxFail('获取失败');
        }

    }

    /**
     * 成长值明细
     * @param $name
     * @param $id
     * @return mixed
     */
    public function grop($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/grop');
    }

    public function getGropList()
    {
        $post = input('post.');
        $model = new MembergroplogModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $where['uid'] = $post['uid'];
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $count = $model->getCount($where);
        $append = array('storeName');
        $field = array(
            'id',
            'growth_value',
            'max_growth_value',
            'reason',
            'addtime',
            'storeid',
            'merchantid',
            'type',
        );
        $order = 'addtime desc';
        $list = $model->getAll($where, $page, $field, $order, $append);
        if ($list !== false) {
            //1购买充值，2购买卡项，3付费会员，4购买产品，5购买服务 10退款
            foreach ($list as $k => &$v) {
                switch ($v['type']) {
                    case 1:
                        $v['gropType'] = '购买充值';
                        break;
                    case 2:
                        $v['gropType'] = '购买卡项';
                        break;
                    case 3:
                        $v['gropType'] = '付费会员';
                        break;
                    case 4:
                        $v['gropType'] = '购买产品';
                        break;
                    case 5:
                        $v['gropType'] = '购买服务';
                        break;
                    case 10:
                        $v['gropType'] = '退款';
                        break;
                }
            }
            return $this->ajaxTable($list, $count, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }


    /**
     * 积分明细
     * @return array
     */
    public function score($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/score');
    }

    public function scoreDetailed()
    {
        $post = input('post.');
        $model = new MemberscorelogModel;
        $user = session('userinfo');
        $where['merchantid'] = $user['merchantid'];
        $where['uid'] = $post['uid'];
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $count = $model->getCount($where);
        $append = array('storeName');
        $field = array(
            'id',
            'score_value',
            'max_score_value',
            'reason',
            'addtime',
            'storeid',
            'merchantid',
            'type',
            'source_type',
            'source_id'
        );
        $order = 'addtime desc';
        $list = $model->getAll($where, $page, $field, $order, $append);

        if ($list !== false) {
            //1购买充值，2购买卡项，3付费会员，4购买产品，5购买服务 10退款
            foreach ($list as $k => &$v) {
                switch ($v['type']) {
                    case 1:
                        $v['scoreType'] = '购买充值';
                        break;
                    case 2:
                        $v['scoreType'] = '购买卡项';
                        break;
                    case 3:
                        $v['scoreType'] = '付费会员';
                        break;
                    case 4:
                        $v['scoreType'] = '购买产品';
                        break;
                    case 5:
                        $v['scoreType'] = '购买服务';
                        break;
                    case 10:
                        $v['scoreType'] = '退款';
                        break;
                }
            }
            return $this->ajaxTable($list, $count, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    /**
     * 会员卡项详情
     * @param $name
     * @param $id
     * @return mixed
     */
    public function card($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/card');
    }

    /**
     * 会员卡项详情
     * @param $name
     * @param $id
     * @return mixed
     */
    public function cardlist()
    {
        $storeid = $this->userinfo['storeid'];
        $this->assign('storeid',$storeid);
        return $this->outPut('member/cardlist');
    }

    //有效卡项
    public function effectiveList()
    {
        $post = input('post.');
        $model = new MemberCardModel;
        $store = new CreatestoreModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $Buyer = true;
        if(isset($post['id'])){
            $where['memberid'] = $post['id'];
            $Buyer = false;
        }
        if(isset($post['cardType']) && $post['cardType']){
            $where['cardtype'] = $post['cardType'];
        }
        if(isset($post['storeid']) && $post['storeid']){
            $where['storeid'] = $post['storeid'];
        }
        $where['status'] = array('eq', 1);
        $where['indate'] = array(
            array('eq', 0),
            array('gt', time()),
            'OR'
        );

        if (isset($post['keyword'])&&$post['keyword'] != '') {
            $uwhere['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['keyword'] . '%');
            if ($post['storeid'] != 0) {
                $ubswhere['storeid']=$post['storeid'];
                $ubsfield="userid";
                $UserBindStoreModel=new UserBindStoreModel;
                $userlist=$UserBindStoreModel->getfieldList($ubswhere, $ubsfield);
                $uwhere['id'] = ['in', $userlist];//
            }
        
            $ubsfield="id";
            $MemberModel =new MemberModel;
            $memberlist=$MemberModel->getfieldList($uwhere, $ubsfield);
            $where['memberid'] = ['in', $memberlist];//
        }
            

        $count = $model->getCount($where);
        $page = $post['page'];
        $limit = $post['limit'];
        $field = '*';
        $append = array('cardName', 'numData', 'StatusName', 'IndateName', 'BuyMoney', 'BuyTimeName');
        if($Buyer){
            $append[] = "Buyer";
        }
        $list = $model->getCardListByPage($where, $page, $limit, $field, $append);
        foreach ($list as $k => &$v) {
            switch ($v['cardtype']) {
                case 1:
                    $v['type'] = '次卡';
                    break;
                case 2:
                    $v['type'] = '充值卡';
                    break;
            }
            // if ($v['canusestore'] == -1) {
            //     $v['storeName'] = '所有门店';
            // } else {
                $wap['id'] = array('in', $v['storeid']);

                // $wap['id'] = array('in', $v['canusestore']);
                $wap['merchantid'] = $merchant_id;
                $wap['status'] = array('eq', 1);
                $field = 'storetag,alias';
                $storeName = $store->getAll($wap, $field);
                $alias = '';
                foreach ($storeName as $item) {
                    $alias .= $item['storetag'] . ',';
                }
                $aliasSub = substr($alias, 0, -1);
                $v['storeName'] = $aliasSub;
            // }
        }
        if ($list) {
            return $this->ajaxTable($list, $count, '查询成功');
        } else {
            return $this->ajaxFail('查询失败');
        }
    }

    //失效卡项
    public function invalidList()
    {
        $post = input('post.');
        $model = new MemberCardModel;
        $store = new CreatestoreModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $Buyer = true;
        if(isset($post['id'])){
            $where['memberid'] = $post['id'];
            $Buyer = false;
        }
        if(isset($post['cardType']) && $post['cardType']){
            $where['cardtype'] = $post['cardType'];
        }
        if(isset($post['storeid']) && $post['storeid']){
            $where['storeid'] = $post['storeid'];
        }
        $time = time();
        $where['_string'] = "status = 2 OR (status = 1 AND indate <> 0 AND indate <{$time})";
        $count = $model->getCount($where);
        $page = $post['page'];
        $limit = $post['limit'];
        $field = '*';
        $append = array('cardName', 'numData', 'StatusName', 'IndateName', 'BuyMoney', 'BuyTimeName');
        if($Buyer){
            $append[] = "Buyer";
        }
        $list = $model->getCardListByPage($where, $page, $limit, $field, $append);
        foreach ($list as $k => &$v) {
            switch ($v['cardtype']) {
                case 1:
                    $v['type'] = '次卡';
                    break;
                case 2:
                    $v['type'] = '充值卡';
                    break;
            }
            if ($v['canusestore'] == -1) {
                $v['storeName'] = '所有门店';
            } else {
                $wap['id'] = array('in', $v['canusestore']);
                $wap['merchantid'] = $merchant_id;
                $wap['status'] = array('eq', 1);
                $field = 'storetag,alias';
                $storeName = $store->getAll($wap, $field);
                $alias = '';
                foreach ($storeName as $item) {
                    $alias .= $item['storetag'] . ',';
                }
                $aliasSub = substr($alias, 0, -1);
                $v['storeName'] = $aliasSub;
            }
        }
        if ($list) {
            return $this->ajaxTable($list, $count, '查询成功');
        } else {
            return $this->ajaxFail('查询失败');
        }
    }
    //查询距离到期还有 x 天的卡项
    public function expiringSoonList()
    {
        // $post = input('post.');
        // $merchant_id = $this->getmerchantid();
        // $MemberCardModel = new MemberCardModel;

        // // 获取传入的 x 参数，表示距离到期还有 x 天
        // if (!isset($post['day']) || !is_numeric($post['day']) || $post['day'] < 0) {
        //     return json(['code' => 400, 'msg' => '参数 x 错误']);
        // }
        // $x = intval($post['day']);
    
        // $time = time();
        // $expireTime = $time + $x * 86400; // 当前时间加上 x 天
    
        // // 查询条件
        // $where = [
        //     'merchantid' => $merchant_id,
        //     'status' => 1, // 状态为正常
        //     'indate' => ['<=', $expireTime] // 有效期小于等于 x 天后的时间
        // ];
        // if(isset($post['storeid']) && $post['storeid']){
        //     $where['storeid'] = $post['storeid'];
        // }
        // // 分页参数
        // $page = isset($post['page']) ? intval($post['page']) : 1;
        // $limit = isset($post['limit']) ? intval($post['limit']) : 10;
    
        // $field = '*';
        // $append = [];//imgArr
        // $list =$MemberCardModel->getCardListByPage($where,$page,$limit,$field,$append);
        // $count = $MemberCardModel->getCount($where);
        // // // 查询数据
        // // $list = Db::name('member_card')
        // //     ->where($where)
        // //     ->page($page, $limit)
        // //     ->select();
    
        // // $count = Db::name('member_card')
        // //     ->where($where)
        // //     ->count();
    
        // if ($list) {
        //     return json(['code' => 200, 'msg' => '查询成功', 'data' => $list, 'count' => $count]);
        // } else {
        //     return json(['code' => 404, 'msg' => '未找到符合条件的卡项']);
        // }



          $post = input('post.');
            // 获取传入的 x 参数，表示距离到期还有 x 天
        if (!isset($post['day']) || !is_numeric($post['day']) || $post['day'] < 0) {
            return json(['code' => 400, 'msg' => '参数 x 错误']);
        }
        $x = intval($post['day']);
    
        $time = time();
        $expireTime = $time + $x * 86400; // 当前时间加上 x 天
          $model = new MemberCardModel;
          $store = new CreatestoreModel;
          $merchant_id = $this->getmerchantid();
          $where['merchantid'] = $merchant_id;
          $Buyer = true;
          if(isset($post['id'])){
              $where['memberid'] = $post['id'];
              $Buyer = false;
          }
          if(isset($post['cardType']) && $post['cardType']){
              $where['cardtype'] = $post['cardType'];
          }
          if(isset($post['storeid']) && $post['storeid']){
              $where['storeid'] = $post['storeid'];
          }
          $where['status'] = array('eq', 1);

          $where['indate'] =['between', [time(), $expireTime]]; // 大于 0 且小于等于 $expireTime
          $count = $model->getCount($where);
          $page = $post['page'];
          $limit = $post['limit'];
          $field = '*';
          $append = array('cardName', 'numData', 'StatusName', 'IndateName', 'BuyMoney', 'BuyTimeName');
          if($Buyer){
              $append[] = "Buyer";
          }
          
          $list = $model->getCardListByPage($where, $page, $limit, $field, $append);
          foreach ($list as $k => &$v) {
              switch ($v['cardtype']) {
                  case 1:
                      $v['type'] = '次卡';
                      break;
                  case 2:
                      $v['type'] = '充值卡';
                      break;
              }
              if ($v['canusestore'] == -1) {
                  $v['storeName'] = '所有门店';
              } else {
                  $wap['id'] = array('in', $v['canusestore']);
                  $wap['merchantid'] = $merchant_id;
                  $wap['status'] = array('eq', 1);
                  $field = 'storetag,alias';
                  $storeName = $store->getAll($wap, $field);
                  $alias = '';
                  foreach ($storeName as $item) {
                      $alias .= $item['storetag'] . ',';
                  }
                  $aliasSub = substr($alias, 0, -1);
                  $v['storeName'] = $aliasSub;
              }
          }
          if ($list) {
              return $this->ajaxTable($list, $count, '查询成功');
          } else {
              return $this->ajaxFail('查询失败');
          }
    }

    //查询在有效时间内还有 y 次到期的卡项
    public function expiringTimesList()
    {
        // $MemberCardDetailModel = new MemberCardDetailModel;

        // $post = input('post.');
        // $merchant_id = $this->getmerchantid();
    
        // // 获取传入的 y 参数，表示剩余次数小于等于 y
        // if (!isset($post['sum']) || !is_numeric($post['sum']) || $post['sum'] < 0) {
        //     return json(['code' => 400, 'msg' => '参数 sum 错误']);
        // }
        // $y = intval($post['sum']);
    
        // $time = time();
    
        // // 查询条件
        // $whereCard = [
        //     'merchantid' => $merchant_id,
        //     'status' => 1, // 状态为正常
        //     'indate' => ['gt', $time], // 永久有效
        // ];
        // if(isset($post['storeid']) && $post['storeid']){
        //     $whereCard['storeid'] = $post['storeid'];
        // }
        // // $whereCard['indate'] = array(
        // //     // array('eq', 0),     
        // //     array('gt', time()),
        // //     'OR'
        // // );
        // // 查询符合条件的卡项 ID
        // $cardIds = Db::name('member_card')
        //     ->where($whereCard)
        //     ->column('id');
    
        // if (empty($cardIds)) {
        //     return json(['code' => 404, 'msg' => '未找到符合条件的卡项']);
        // }
        // // var_dump($cardIds);
        // // exit();
        // // 查询 `member_card_detail` 表中剩余次数小于等于 y 的记录
        // $whereDetail = [
        //     'membercard_id' => ['in', $cardIds],
        //     'num' => ['<=', $y] // 剩余次数小于等于 y
        // ];
    
        // // 分页参数
        // $page = isset($post['page']) ? intval($post['page']) : 1;
        // $limit = isset($post['limit']) ? intval($post['limit']) : 10;
        // $field = 'memberid,card_id,num,usenum,addtime,start_time,isgive,membercard_id';
        // $append = ['Buyer','IndateName','CardName'];//imgArr
        // // 查询数据
        // $list =$MemberCardDetailModel->getSelectByPage($whereDetail,$page,$limit,$field,$append);
        // $count = $MemberCardDetailModel->getCount($whereDetail);

        // if ($list) {
        //     return json(['code' => 200, 'msg' => '查询成功', 'data' => $list, 'count' => $count]);
        // } else {
        //     return json(['code' => 404, 'msg' => '未找到符合条件的卡项']);
        // }
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
    
        $y = intval($post['sum']);
        $whereCard = [
            'merchantid' => $merchant_id,
            'indate' => ['gt', time()], // 永久有效
            'num' => ['between', [1, $y]] // 剩余次数大于 0 且小于等于 y
        ];
     
        // 查询符合条件的卡项 ID
        $cardIds = Db::name('member_card_detail')
            ->where($whereCard)
            ->distinct(true) // 使用 distinct 去重
            ->column('membercard_id');
    
        if (empty($cardIds)) {
            return $this->ajaxFail('查询失败');
        }
 
        //  var_dump($cardIds);
        //  exit();
        // $post = input('post.');
        $model = new MemberCardModel;
        $store = new CreatestoreModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $Buyer = true;
        if(isset($post['id'])){
            $where['memberid'] = $post['id'];
            $Buyer = false;
        }
        if(isset($post['cardType']) && $post['cardType']){
            $where['cardtype'] = $post['cardType'];
        }
        if(isset($post['storeid']) && $post['storeid']){
            $where['storeid'] = $post['storeid'];
        }
        $where['status'] = array('eq', 1);
        $where['id'] =['in', $cardIds];
        $where['indate'] = array('gt', time());
        $count = $model->getCount($where);
        $page = $post['page'];
        $limit = $post['limit'];
        $field = '*';
        $append = array('cardName', 'numData', 'StatusName', 'IndateName', 'BuyMoney', 'BuyTimeName');
        if($Buyer){
            $append[] = "Buyer";
        }
        $list = $model->getCardListByPage($where, $page, $limit, $field, $append);
        foreach ($list as $k => &$v) {
            switch ($v['cardtype']) {
                case 1:
                    $v['type'] = '次卡';
                    break;
                case 2:
                    $v['type'] = '充值卡';
                    break;
            }
            if ($v['canusestore'] == -1) {
                $v['storeName'] = '所有门店';
            } else {
                $wap['id'] = array('in', $v['canusestore']);
                $wap['merchantid'] = $merchant_id;
                $wap['status'] = array('eq', 1);
                $field = 'storetag,alias';
                $storeName = $store->getAll($wap, $field);
                $alias = '';
                foreach ($storeName as $item) {
                    $alias .= $item['storetag'] . ',';
                }
                $aliasSub = substr($alias, 0, -1);
                $v['storeName'] = $aliasSub;
            }
        }
        if ($list) {
            return $this->ajaxTable($list, $count, '查询成功');
        } else {
            return $this->ajaxFail('查询失败');
        }
    }


    //会员卡详情
    public function cardDetails()
    {
        $post = input('post.');
        $model = new MemberCardModel;
        $merchant_id = $this->getmerchantid();
        $where['merchantid'] = $merchant_id;
        $where['id'] = $post['id'];
        $where['status'] = array('neq', 3);
        $field = '*';
        $append = array('cardName', 'numData', 'StatusName', 'IndateName', 'BuyMoney', 'BuyTimeName');
        $list = $model->getFind($where, $field, $append);
        if(is_object($list)){
            $list = $list->toArray();
        }
        if (count($list) > 0) {
            switch ($list['cardtype']) {
                case 1:
                    $list['type'] = '次卡';
                    break;
                case 2:
                    $list['type'] = '充值卡';
                    break;
            }
            $list['totalbalance'] = number_format($list['totalbalance'] / 100, 2, '.', '');
            $list['usebalance'] = number_format($list['usebalance'] / 100, 2, '.', '');
            $list['capitalbalance'] = number_format($list['capitalbalance'] / 100, 2, '.', '');
            $list['usecapitalbalance'] = number_format($list['usecapitalbalance'] / 100, 2, '.', '');
            $list['presentbalance'] = number_format($list['presentbalance'] / 100, 2, '.', '');
            $list['usepresentbalance'] = number_format($list['usepresentbalance'] / 100, 2, '.', '');
        }
        if ($list) {
            return $this->ajaxSuccess($list, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    //权益
    public function equity($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/equity');
    }

    //会员权益
    public function getEquity()
    {
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        $MemberCardModel = new MemberCardModel;
        $MemberCardDetailModel = new MemberCardDetailModel;
        $store = new CreatestoreModel;
        $card = new CardModel;
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $time = time();
        if ($post['types'] == 1) {
            $where = array(
                'memberid' => $post['id'],
                'merchantid' => $merchant_id,
                // 状态
                'status' => 1,
                // 激活
                'isactivate' => 1,
                // 有效期
                'indate' => array(
                    array('eq', 0),
                    array('gt', $time),
                    'OR'
                ),
            );
        } else {
            $where = array(
                'memberid' => $post['id'],
                'merchantid' => $merchant_id,
                // 状态
                'status' => array('neq', 3),
                // 激活
                'isactivate' => 1,
                // 有效期
                'indate' => array(
                    array('neq', 0),
                    array('elt', $time),
                    'and'
                ),
            );
        }
        $field = array(
            'id', 'cardid', 'cardtype', 'allnum', 'once_cardtype', 'indate', 'card_name as memberCardName'
        );
        $append = array(
//            'card_info'
        );
        $cardInfo = $MemberCardModel->getSearch($where, $field, $append);
        if ($cardInfo === false) {
            $error = $MemberCardModel->error;
            return $this->ajaxFail($error);
        }
        $membercards = array();
        foreach ($cardInfo as $k => $v) {
            $membercards[] = $v['id'];
        }
        if ($post['types'] == 2) {
            $where2 = array(
                'memberid' => $post['id'],
                'merchantid' => $merchant_id,
                // 状态
                'status' => array('neq', 3),
                // 激活
                'isactivate' => 1,
                'cardid'=>array('lt',0)
            );
            $cardInfo2 = $MemberCardModel->getSearch($where2, $field, $append);
            if($cardInfo2){
                foreach ($cardInfo2 as $k => $v) {
                    $membercards[] = $v['id'];
                }
                $cardInfo = array_merge($cardInfo,$cardInfo2);
            }
        }
        $equityWhere = array(
            'memberid' => $post['id'],
            'merchantid' => $merchant_id,
            'goods_type' => 1,
            'membercard_id' => array('in', $membercards),
        );
        $equityWhere['indate'] = $where['indate'];
        $fiel = '*';
        $append = array('IndateName', 'maxnum');
        $equityWhere['_sql'] = '(( card_type = 1 and (num = -1 or ( num - usenum > 0 ))) or card_type <> 1 )';
        $count = $MemberCardDetailModel->getCount($equityWhere);
//        $equityWhere['cardinfo'] = $cardInfo;
        unset($equityWhere['_sql']);
        $data = $MemberCardModel->getMSEquityDetails($equityWhere, $page, $fiel, $append);
        foreach ($data as $k => &$v) {
            $v['addtime'] = date('Y-m-d H:i:s', $v['addtime']);
            //服务名称（权益名称）
            $goodsinfo = $this->goodsId2goodsInfo($v['goods_id']);
            if (count($goodsinfo) > 0) {
                $v['goodsName'] = $goodsinfo['service_name'];
            } else {
                $v['goodsName'] = '--';
            }
            //查询权益来源
            if ($v['card_type'] == 1) {
                switch ($v['once_cardtype']) {
                    case 1:
                        $v['source'] = '有限次卡';
                        break;
                    case 2:
                        $v['source'] = '无限次卡';
                        break;
                    case 3:
                        $v['source'] = '通卡';
                        break;
                }
            } else if ($v['card_type'] == 2) {
                $v['source'] = '充值卡';
            }
            $v['card_name'] = $this->membercardId2cardName($v['membercard_id'],$cardInfo);
            //适用门店
            if ($v['canuserstore'] == -1) {
                $v['storeName'] = '所有门店';
            } else {
                $wap['id'] = array('in', $v['canuserstore']);
                $wap['merchantid'] = $merchant_id;
//                $wap['status'] = array('eq', 1);
                $field = 'storetag,alias';
                $storeName = $store->getAll($wap, $field);
                $alias = '';
                foreach ($storeName as $item) {
                    $alias .= $item['storetag'] . ',';
                }
                $aliasSub = substr($alias, 0, -1);
                $v['storeName'] = $aliasSub;
            }
        }
        if ($data) {
            return $this->ajaxTable($data, $count, '查询成功');
        } else {
            return $this->ajaxFail('查询失败');
        }
    }

    protected $goodsInfoData = array();

    private function goodsId2goodsInfo($goods_id)
    {
        $goodsInfoData = $this->goodsInfoData;
        foreach ($goodsInfoData as $k => $v) {
            if ($v['goods_id'] == $goods_id) {
                return $v['goodsInfo'];
            }
        }
        $MemberCardDetailModel = new MemberCardDetailModel;
        if ($this->userinfo['storeid']) {
            $map = array('goods_id' => $goods_id);
            $goodsinfo = $MemberCardDetailModel->getgoodsinfo($this->userinfo['storeid'], $map);
            if (count($goodsinfo) > 0) {
                $this->goodsInfoData[] = array(
                    'goods_id' => $goods_id,
                    'goodsInfo' => $goodsinfo
                );
                return $goodsinfo;
            }
        }
        $map = array('goods_id' => $goods_id);
        $goodsinfo = $MemberCardDetailModel->getgoodsinfo2($map);
        if (count($goodsinfo) > 0) {
            $this->goodsInfoData[] = array(
                'goods_id' => $goods_id,
                'goodsInfo' => $goodsinfo
            );
            return $goodsinfo;
        } else {
            $this->goodsInfoData[] = array(
                'goods_id' => $goods_id,
                'goodsInfo' => false
            );
            return false;
        }
    }

    private function membercardId2cardName($id,$data){
        foreach ($data as $k=>$v){
            if($id==$v['id']){
                return $v['memberCardName'];
            }
        }
        return '--';
    }
    //优惠券
    public function coupon($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/coupon');
    }

    public function getCouponInfo()
    {
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        $CouponrecordModel = new CouponrecordModel;
        $CreatestoreModel = new CreatestoreModel;
        switch ($post['type']) {
            case 1:
                $where['endtime'] = array('egt', time());//未使用
                $where['status'] = array('neq', 3);//未使用
                break;
            case 2:
                $where['status'] = array('eq', 3);//已使用
                break;
            case 3:
                $where['endtime'] = array('elt', time());//已过期
                $where['status'] = array('neq', 3);//已过期
                break;
        }
        $where['memberid'] = $post['id'];
        $where['merchantid'] = $merchant_id;
        $count = $CouponrecordModel->getCount($where);
        $order = 'get_time desc';

        $field = array('*');
        $append = array('faceValue', 'expireTime', 'costInfo', 'applyGoods');
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $couponInfo = $CouponrecordModel->getAll($where, $field, $order, $append);
        foreach ($couponInfo as $k => &$v) {
            $v['reduce_money'] = number_format($v['reduce_money'] / 100, 2, '.', '');
            $wap['id'] = array('in', $v['use_store']);
            $wap['merchantid'] = $merchant_id;
            $wap['status'] = array('neq', 3);
            $field = array('storetag');
            $storeInfo = $CreatestoreModel->getAll($wap, $field);
            $str = '';
            foreach ($storeInfo as $item) {
                $str .= $item['storetag'] . '、';
            }
            $v['storeInfo'] = substr($str, 0, -3);
        }
        if ($couponInfo !== false) {
            return $this->ajaxTable($couponInfo, $count, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }


    /**
     * 会员欠款记录
     * @return array
     */
    public function arrears($name, $id)
    {
        $this->assign('id', $id);
        return $this->outPut('member/arrears');
    }

    /**
     * 查询会员欠款记录（订单）
     * @return array
     */
    public function getArrearsInfo()
    {
        $post = input('post.');
        $user = session("userinfo");
        $payOrderModel = new payOrderModel;
        $StaffModel = new StaffModel;
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        }
        $where['merchant_id'] = $user['merchantid'];
        $where['vip_id'] = $post['id'];
        if (isset($post['is_debt'])){
            switch ($post['is_debt']){
                case 1:
                    $where['is_debt'] = 1;
                    $where['is_refund'] = 2;
                    break;
                case 2:
                    $where['is_debt'] = array(
                        array('eq',1),
                        array('eq',2),
                        'OR'
                    );
                    $where['_sql'] = "( is_debt = 2 or (is_debt = 1 and is_refund = 1))";
                    break;
            }
        }
        $count = $payOrderModel->getCount($where);
        $order = "order_time desc";
        $where['page'] = $post['page'];
        $where['limit'] = $post['limit'];
        $field = array(
            'id',
            'receivable',
            'is_refund',
            'is_debt',
            'debt_value',
            'collection_time',
            'merchant_id',
            'vip_id',
            'store_id',
            'time',
            'cashier_id'
        );
        $append = array('storeTag','vip');
        $orderList = $payOrderModel->getOrderList($where, $field, $order,$append);
        foreach ($orderList as $k => &$v) {
            $v['receivable'] = number_format($v['receivable'] / 100, 2, '.', '');
            $v['debt_value'] = number_format($v['debt_value'] / 100, 2, '.', '');
            $v['collection_time'] = date("Y-m-d H:i:s", $v['collection_time']);// 付款时间
            $v['time'] = date("Y-m-d H:i:s", $v['time']);// is_debt==2 还款时间
            if ($v['cashier_id'] != '') {
                $cashier['id'] = $v['cashier_id'];
                $cashierData = $StaffModel->getFind($cashier);
                $v['cashier'] = $cashierData['nickname'] ? $cashierData['nickname'] : "--";
            } else {
                $v['cashier'] = "--";
            }
        }
        return $this->ajaxTable($orderList, $count, 'ok');
    }

    /**********************------会员导入部分------******************************/

    public function import()
    {
        return $this->outPut('member/import');
    }

    //导出会员
    public function importMember()
    {
        $get = input('get.');
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        vendor("PHPExcel.Classes.PHPExcel.Worksheet.Drawing");
        $objPHPExcel = new \PHPExcel();
        //$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        //$objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);

        $user = session('userinfo');
        $memberModel = new MemberModel;
        $storeModel = new CreatestoreModel;
        $sourceModel = new MembersourceModel;
        $where['merchantid'] = $user['merchantid'];
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        }
        if (isset($get['isVip']) && $get['isVip']==1) {
            $where['is_vip'] = 1;
        }
        //消费频次筛选
        if ($get['last_time'] != '') {
            $monthday = strtotime('-' . $get['last_time'] . 'months');//一个月之内的数据
            $where['last_time'] = array('elt', $monthday);
        }
        if (!empty($get['z_last_time'])) {
            $where['last_time'] = array('elt', $get['z_last_time']);
        }
        //生日
        $birthday = $get['birthday'];
        switch ($birthday) {
            case 1:  //今天
                $birthday = date('m-d');
                break;
            case 2:  //明天
                $birthday = date("m-d", strtotime("+1 day"));
                break;
            case 3:  //本周
                $birthday1 = date("m-d");//今天
                $birthday2 = date("m-d", strtotime("last day next week +1 day"));//本周最后一天
                break;
            case 4:  //本月
                $birthday1 = date("m-d");//今天
                $birthday2 = date("m-d", mktime(23, 59, 59, date('m'), date('t'), date('Y')));//本月最后一天
                break;
            case 5:  //下月
                $birthday1 = date("m-d", mktime(0, 0, 0, date("m") + 1, 1, date("Y")));//下月1号
                $birthday2 = date("m-d", mktime(23, 59, 59, date("m") + 2, 0, date("Y")));//下月31号
                break;
        }
        //生日筛选
        if ($birthday != '') {
            $where['birthday'] = array('eq', $birthday);
        }
        if (!empty($birthday1) && !empty($birthday2)) {
            $where['birthday'] = array('between', array($birthday1, $birthday2));
        }
        if (!empty($get['starttime']) && !empty($get['endtime'])) {
            $starttime = date('m-d', strtotime($get['starttime']));
            $endtime = date('m-d', strtotime($get['endtime']));
            $where['birthday'] = array('between', array($starttime, $endtime));
        }
        if (!empty($get['adviser'])) { //新客归属
            $where['adviser'] = array('eq', $get['adviser']);
        }
        //消费次数
        if ($get['counts'] != '') {
            $where['count'] = array('elt', $get['counts']);
        }
        if ($get['z_count'] != '') {
            $where['count'] = array('elt', $get['z_count']);
        }
        //门店筛选
        if (!empty($get['store'])) {
            $where['store_id'] = array('eq', $get['store']);
        }
        //来源筛选
        if (!empty($get['source'])) {
            $where['member_source'] = array('eq', $get['source']);
        }
        //查询等级
        $mod = new MemberlevelModel;
        if (!empty($get['level'])) {
            $wh['id'] = $get['level'];
            $level = $mod->getMemberLevel($wh);
            if ($level != '') {
                $low = $level[0]['growth_value'];
                $Rd['growth_value'] = array('gt', $low);
                $Wq = $mod->getMemberLevel($Rd);
                if (count($Wq) > 0) {
                    $high = $Wq[0]['growth_value'];
                } else {
                    $high = '';
                }
                if ($high != '') {
                    $where['growth_value'] = array(array('egt', $low), array('lt', $high));
                } else {
                    $where['growth_value'] = array('egt', $low);
                }
            }
        }
        // 根据标签筛选
        if (!empty($get['tag'])) {
            $tab = $get['tag'];
            $where['tab_id'] = array(
                array('like', '%,' . $tab),
                array('like', $tab . ',%'),
                array('like', '%,' . $tab . ',%'),
                array('eq', $tab),
                'OR'
            );
        }
        $field = array(
            'id',
            'member_name',
            'remarks_name',
            'sex',
            'phone',
            'birthday',
            'birthday_year',
            'member_number',
            'member_source',
            'score',
            'store_id',
            'wechat_number',
            'remarks',
            'addtime',
            'growth_value',
            'balance',
            'pic',
        );
        $sql = $memberModel->getAll($where, $field);
        foreach ($sql as $ka => &$va) {
            $va['addtime'] = date('Y-m-d H:i:s', $va['addtime']);
            $va['balance'] = number_format($va['balance'] / 100, 2,'.','');
            switch ($va['sex']) {
                case 1:
                    $va['sex'] = '男';
                    break;
                case 2:
                    $va['sex'] = '女';
                    break;
                default:
                    $va['sex'] = '未知';
                    break;
            }

            if (!empty($va['birthday_year']) && !empty($va['birthday'])) {
                $va['birthday'] = $va['birthday_year'] . '-' . $va['birthday'];
            } else {
                $va['birthday'] = '';
            }

            //查询等级
            $wap['growth_value'] = array('elt', $va['growth_value']);
            $wap['merchant_id'] = $user['merchantid'];
            $member_level = $memberModel->getMemberLevel($wap);
            $cou = count($member_level);
            $va['levelname'] = $member_level[$cou - 1]['level_name'];

            //查询门店
            $storeWhere = array(
                'id' => array('eq', $va['store_id']),
                'merchantid' => $user['merchantid'],
            );
            $storeField = array('id', 'storetag');
            $storeInfo = $storeModel->getFinds($storeWhere, $storeField);
            if ($storeInfo !== false) {
                $va['storename'] = $storeInfo['storetag'];
            } else {
                $va['storename'] = '';
            }

            //查询来源
            $source['id'] = array('eq', $va['member_source']);
            $member_source = $sourceModel->getFind($source);
            $va['sourcename'] = $member_source['source_name'];
            $va['pic'] = $va['pic']?$va['pic']:'';
            $va['phone'] = $va['phone']?strval($va['phone']):'';
       }
        $rowConfig = array(
            array('A','会员昵称','member_name'),
            array('B','备注名','remarks_name'),
            array('C','手机号','phone'),
            array('D','性别','sex'),
            array('E','生日','birthday'),
            array('F','会员积分','score'),
            array('G','余额（元）','balance'),
            array('H','会员等级','levelname'),
            array('I','微信号','wechat_number'),
            array('J','会员备注','remarks'),
            array('K','归属门店','storename'),
            array('L','会员来源','sourcename'),
            array('M','成为会员时间','addtime'),
            array('N','会员编号','member_number'),
            array('O','会员头像','pic'),
        );
        foreach ($rowConfig as $k=>$v){
            $objPHPExcel->getActiveSheet()->getColumnDimension($v[0])->setWidth(24);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue($v[0].'1', $v[1]);
        }
        // 设置表头信息
        $count = count($sql);  //计算有多少条数据
        /*实例化插入图片类*/
        // $objDrawing = new \PHPExcel_Worksheet_Drawing();
        for ($i = 2; $i <= $count + 1; $i++) {
            $objPHPExcel->getActiveSheet()->getRowDimension($i)->setRowHeight(88);
            foreach ($rowConfig as $k=>$v){
                if($v[2]=='pic'){
                    $cell_counter = $i - 2;
                    $pic = $sql[$i - 2][$v[2]];
                    if(strstr($pic, 'wx.qlogo')){
                        $avatar = substr($pic,strripos($pic,"//")+2);     //截取微信头像中的https：//
                        $pic  =  $this->wx_image($avatar);
                    }
                    try {
                        if ($pic) {
                            // 图片生成
                            $objDrawing[$cell_counter] = new \PHPExcel_Worksheet_MemoryDrawing();
                            // 截取图片的格式，用不同的方法
                            $end[$cell_counter] = substr($pic, -3);

                            if ($end[$cell_counter] == 'jpg' || $end[$cell_counter] == 'peg') {
                                $img[$cell_counter] = @imagecreatefromjpeg($pic);
                            } else if ($end[$cell_counter] == 'png') {
                                $img[$cell_counter] = @imagecreatefrompng($pic);
                            } else if ($end[$cell_counter] == 'gif') {
                                $img[$cell_counter] = @imagecreatefromgif($pic);
                            } else {
                                continue;
                            }
                            $objDrawing[$cell_counter]->setImageResource($img[$cell_counter]);

                            $objDrawing[$cell_counter]->setRenderingFunction(\PHPExcel_Worksheet_MemoryDrawing::RENDERING_DEFAULT);//渲染方法

                            $objDrawing[$cell_counter]->setMimeType(\PHPExcel_Worksheet_MemoryDrawing::MIMETYPE_DEFAULT);
                            // // 设置宽度高度
                            $objDrawing[$cell_counter]->setHeight(40);//照片高度
                            $objDrawing[$cell_counter]->setWidth(60); //照片宽度
                            // /*设置图片要插入的单元格*/
                            $objDrawing[$cell_counter]->setCoordinates($v[0] . $i);
                            // // 图片偏移距离
                            $objDrawing[$cell_counter]->setOffsetX(8);
                            $objDrawing[$cell_counter]->setOffsetY(8);
                            $objDrawing[$cell_counter]->setWorksheet($objPHPExcel->getActiveSheet());
                        }
                    } catch (\Exception $e) {
                        continue;
                    }
                    continue;
                }
                $objPHPExcel->getActiveSheet()->setCellValue($v[0] . $i, $sql[$i - 2][$v[2]]);

            }
        }
        $objPHPExcel->getActiveSheet()->setTitle('会员信息');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员信息.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    /**
     * @param $url  微信图片地址
     * @return string  正常图片地址
     */
    public function wx_image($url){
        $header = array(
            'User-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:45.0) Gecko/20100101 Firefox/45.0',
            'Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding: gzip, deflate',);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);curl_setopt($curl, CURLOPT_ENCODING, 'gzip');
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        $data = curl_exec($curl);$code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        if ($code == 200) {//把URL格式的图片转成base64_encode格式的！
            $imgBase64Code = "data:image/jpeg;base64," . base64_encode($data);
        }
        $img_content=$imgBase64Code;//图片内容
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $img_content, $result)) {
            $type = $result[2];//得到图片类型png?jpg?gif?
            $fileData  =   base64_decode(str_replace($result[1], '', $img_content));
            $image_name = "headimg.".$type;
            $upload_dir = './upload/img/';
            if (!file_exists($upload_dir)){
                mkdir( $upload_dir, 0777, true );
            }
            $filedir = $upload_dir . '/' . $image_name;
            $src     = './upload/img/' . $image_name;
            if (file_put_contents($filedir,$fileData)) {
                return $src;
            }
        }
    }

    //获取导入记录信息
    public function getMemberImport()
    {
        $post = input('post.');
        $merchant_id = $this->getmerchantid();
        $importModel = new MemberimportModel;
        $MembercopyModel = new MembercopyModel;
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $where['merchantid'] = $merchant_id;
        $count = $importModel->getCount($where);
        $field = '*';
        $order = 'addtime desc';
        $list = $importModel->getAll($where, $page, $field, $order);
        if ($list) {
            foreach ($list as $k => &$v) {
                $wap['import_id'] = $v['id'];
                $wap['merchantid'] = $merchant_id;
                $errorCount = $MembercopyModel->getCount($wap);
                $v['errorNum'] = $errorCount;
                $v['successNum'] = $v['total_num'] - $errorCount;
            }
            return $this->ajaxTable($list, $count, '获取成功');
        } else {
            return $this->ajaxFail('获取失败');
        }
    }

    //下载模版
    public function download()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        //查询门店id信息
        $storeModel = new CreatestoreModel;
        $merchant_id = $this->getmerchantid();
        $storeWhere = array(
            'status' => array('eq', 1),
            'merchantid' => $merchant_id,
        );
        $storeField = array('id', 'storetag');
        $storeInfo = $storeModel->getAll($storeWhere, $storeField);
        $storeString = '';
        if ($storeInfo !== false) {
            foreach ($storeInfo as $k => &$v) {
                $storeString .= $v['storetag'] . '：' . $v['id'] . '；';
            }
        }
        //会员等级信息
        $levelModel = new MemberlevelModel;
        $levelWhere = array(
            'merchant_id' => $merchant_id,
        );
        $levelInfo = $levelModel->getMemberLevel($levelWhere);
        $levelString = '';
        if ($levelInfo) {
            foreach ($levelInfo as $ka => &$va) {
                $levelString .= $va['level_name'] . '：' . $va['id'] . '；';
            }
        }

        //查询来源
        $sourceModel = new Membersource;
        $sourceWhere = array(
            'status' => array('neq', 3),
            'merchant_id' => array(
                array('eq', $merchant_id),
                array('eq', 0),
                'OR'
            )
        );
        $sourceField = array('id', 'source_name');
        $sourceInfo = $sourceModel->getAll($sourceWhere, $sourceField);
        $sourceString = '';
        if ($sourceInfo !== false) {
            foreach ($sourceInfo as $kz => &$vz) {
                $sourceString .= $vz['source_name'] . '：' . $vz['id'] . '；';
            }
        }

        $strArr = "qwertyuiopasdfghjklzxcvbnm";
		$strArr = strtoupper($strArr);
		$strArr = str_split($strArr);
		sort($strArr);
		$columnArr = [];
		for($i=0;$i<12;$i++){
			$columnArr[] = $strArr[$i];
		}
		foreach ($columnArr as $k=>$v){
			$objPHPExcel->getActiveSheet()->getColumnDimension($v)->setWidth(28);
		}
		// 设置表头信息
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '会员昵称')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '最长支持20个字')
            ->setCellValue('A3', '备注名')
            ->setCellValue('B3', '建议填写客户真实姓名')
            ->setCellValue('C3', '最长支持20个字')
            ->setCellValue('A4', '手机号')
            ->setCellValue('B4', '必填项。')
            ->setCellValue('C4', '请填写正确手机号。如果中已存在，则该条数据不会被导入')
            ->setCellValue('A5', '性别')
            ->setCellValue('B5', '请填写：男、女。')
            ->setCellValue('C5', '不填写默认为未知')
            ->setCellValue('A6', '生日')
            ->setCellValue('B6', '请输入8位日期。')
            ->setCellValue('C6', '如：2017-09-01')
            ->setCellValue('A7', '会员积分')
            ->setCellValue('B7', '请输入正整数，不填则默认 0')
            ->setCellValue('A8', '余额')
            ->setCellValue('B8', '单位：元，最多保留两位小数')
            ->setCellValue('A9', '会员等级')
            ->setCellValue('B9', '必填项。如果设有非会员，请注意非会员与会员的区分，详情可了解会员等级')
            ->setCellValue('C9', '请填入您在系统中设置的等级ID：' . $levelString)
            ->setCellValue('A10', '微信号')
            ->setCellValue('B10', '最多16个字符')
            ->setCellValue('A11', '备注')
            ->setCellValue('B11', '最多200个字')
            ->setCellValue('A12', '归属门店')
            ->setCellValue('B12', '请输入门店对应的部门ID')
            ->setCellValue('C12', '总部：0；' . $storeString)
            ->setCellValue('D12', '如果默认为总部请填写：0')
            ->setCellValue('A13', '会员来源')
            ->setCellValue('B13', '请输入系统已有的来源名称')
            ->setCellValue('C13', '请填入您在系统中设置的来源名称ID：' . $sourceString)
            ->setCellValue('A15', '请在下面的表格填入客户数据，一行为一条数据')
            ->setCellValue('A16', '会员昵称（必填）')
            ->setCellValue('B16', '备注名（建议填写客户真实姓名）')
            ->setCellValue('C16', '手机号（必填）')
            ->setCellValue('D16', '性别')
            ->setCellValue('E16', '生日')
            ->setCellValue('F16', '会员积分')
            ->setCellValue('G16', '余额（元，最多保留两位小数）')
            ->setCellValue('H16', '会员等级（必填，要跟系统里的一致）')
            ->setCellValue('I16', '微信号')
            ->setCellValue('J16', '会员备注')
            ->setCellValue('K16', '归属门店（必填）')
            ->setCellValue('L16', '会员来源(必填)');

        $objPHPExcel->getActiveSheet()->setTitle('会员导入模版');      // 设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   // 设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   // 通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    //导出导入失败的信息
    public function outexcel()
    {
        $id = input('get.id');
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 实例化完了之后就先把数据库里面的数据查出来
        $MembercopyModel = new MembercopyModel;
        $merchant_id = $this->getmerchantid();
        $where = array(
            'import_id' => $id,
            'merchantid' => $merchant_id,
        );
        $sql = $MembercopyModel->getList($where);
        // 设置表头信息
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '会员昵称')
            ->setCellValue('B1', '备注名')
            ->setCellValue('C1', '手机号')
            ->setCellValue('D1', '性别')
            ->setCellValue('E1', '生日')
            ->setCellValue('F1', '会员积分')
            ->setCellValue('G1', '余额')
            ->setCellValue('H1', '会员等级')
            ->setCellValue('I1', '微信号')
            ->setCellValue('J1', '会员备注')
            ->setCellValue('K1', '归属门店')
            ->setCellValue('L1', '会员来源');
        $i = 2;  //定义一个i变量，目的是在循环输出数据是控制行数
        $count = count($sql);  //计算有多少条数据
        for ($i = 2; $i <= $count + 1; $i++) {
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $sql[$i - 2]['member_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $sql[$i - 2]['remarks_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $sql[$i - 2]['phone']);
            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $sql[$i - 2]['sex']);
            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $sql[$i - 2]['birthday']);
            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $sql[$i - 2]['score']);
            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $sql[$i - 2]['balance']);
            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $sql[$i - 2]['level_id']);
            $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $sql[$i - 2]['wechat_number']);
            $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $sql[$i - 2]['remarks']);
            $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $sql[$i - 2]['store_id']);
            $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $sql[$i - 2]['member_source']);
        }
        $objPHPExcel->getActiveSheet()->setTitle('会员导入失败列表');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员导入失败列表.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    public function testajax()
    {
        $file = Request::instance()->file('file');
        if ($file) {
            $file_types = explode(".", $_FILES ['file'] ['name']); // ["name"] => string(25) "excel文件名.xls"

            $file_type = $file_types [count($file_types) - 1];//xls后缀

            $file_name = $file_types [count($file_types) - 2];//xls去后缀的文件名
            /*判别是不是.xls文件，判别是不是excel文件*/
            if (strtolower($file_type) != "xls" && strtolower($file_type) != "xlsx") {
                return $this->ajaxFail('不是Excel文件，重新上传');
            }
            $user = session('userinfo');
            $str = $user['merchantid'] . '_' . $user['storeid'];
            //移到/public/uploads/excel/下
            $info = $file->move('upload' . DS . 'excel', $file_name . '_' . $str);
            Session::set('fileName', $file_name . '_' . $str . '.' . $file_type);
        }
    }

    public function importExcel()
    {
        $model = new MemberModel;
        $MembercopyModel = new MembercopyModel;
        $comnum = new ComnumModel;//自动生成编号
        $levelModel = new MemberlevelModel;//
        $importModel = new MemberimportModel;//
        $merchant_id = $this->getmerchantid();
        vendor('PHPExcel.Classes.PHPExcel');
        vendor('PHPExcel.Classes.PHPExcel.PHPExcel_IOFactory');
        vendor('PHPExcel.Classes.PHPExcel.PHPExcel_Cell');
        //实例化PHPExcel
        $objPHPExcel = new \PHPExcel();
        $user = session('userinfo');
        $file = session('fileName');
        if ($file == '') {
            return $this->ajaxFail('请先上传excel文件！');
        }
        $file_path = 'upload/excel/' . $file;//上传后的EXCEL路径
        $re = $this->actionRead($file_path, 'utf-8');
        array_splice($re, 1, 0);
        /*将数组的键改为自定义名称*/
        try {
            Db::startTrans();
            $keys = array(
                'member_name',
                'remarks_name',
                'phone',
                'sex',
                'birthday',
                'score',
                'balance',
                'level_id',
                'wechat_number',
                'remarks',
                'store_id',
                'member_source'
            );
            foreach ($re as $i => $vals) {
                $re[$i] = array_combine($keys, $vals);
            }
            //添加导入记录
            $total_num = count($re);
            $importData = array(
                'merchantid' => $merchant_id,
                'addtime' => time(),
                'total_num' => $total_num,
                'admin' => $user['nickname'],
                'department' => $user['storetag'],
                'position' => $user['group_name']
            );
            $importID = $importModel->adImport($importData);//遍历数组写入数据库
            for ($i = 0; $i < count($re); $i++) {
                unset($re[$i]['img']);
                $data = $re[$i];
                $importData = $re[$i];
                $member_number = $comnum->memberNum($merchant_id);//会员编号
                $map['phone'] = $data['phone'];
                $map['merchantid'] = $merchant_id;
                $count = $model->getCount($map);
                //判断手机号是否注册过，如果有则跳过导入
                if ($count <= 0) {
                    if ($data['sex'] == '男') {
                        $data['sex'] = 1;
                    } elseif ($data['sex'] == '女') {
                        $data['sex'] = 2;
                    } else {
                        $data['sex'] = 3;
                    }
                    //查询成长值
                    $levelWhere = array(
                        'id' => (int)$data['level_id'],
                        'merchant_id' => $merchant_id,
                    );
                    $levelField = 'growth_value';
                    $levelFind = $levelModel->getFind($levelWhere, $levelField);
                    if (!$levelFind) {
//                        $data['level_id'] = 1;
                        $data['growth_value'] = 0;
                        $data['is_vip'] = 0;
                    } else {
                        $data['growth_value'] = $levelFind['growth_value'];
                        if ($levelFind['growth_value'] == 0){
                            $data['is_vip'] = 0;
                        }else{
                            $data['is_vip'] = 1;
                        }
                    }
                    //密码手机号后六位
                    $time = time();
                    $pass = substr($data['phone'], -6, 6);
                    $key = date('YmdHis', $time);
                    $data['password'] = encrypt($pass, $key);
                    //生日
                    if (!empty($data['birthday'])) {
                        $data['birthday_year'] = date('Y', strtotime($data['birthday']));
                        $data['birthday'] = date('m-d', strtotime($data['birthday']));
                    } else {
                        $data['birthday'] = '';
                        $data['birthday_year'] = '';
                    }
                    $data['merchantid'] = $merchant_id;
                    if (!empty($data['balance'])) {
                        $data['balance'] = $data['balance'] * 100;
                    }
                    $data['member_number'] = $member_number;
                    $data['addtime'] = $time;
                    $list = $model->addMember($data);
                    if (!$list) {
                        $importData['addtime'] = time();
                        $importData['merchantid'] = $merchant_id;
                        $importData['import_id'] = $importID;
                        $MembercopyModel->adMemberCopy($importData);
                    }
                } else {
                    $importData['addtime'] = time();
                    $importData['merchantid'] = $merchant_id;
                    $importData['import_id'] = $importID;
                    $MembercopyModel->adMemberCopy($importData);
                }
            }
            $wh['id'] = $importID;
            $wh['merchantid'] = $merchant_id;
            $field = 'id,total_num';
            $list = $importModel->getfind($wh, $field);
            if ($list) {
                $wap['import_id'] = $list['id'];
                $wap['merchantid'] = $merchant_id;
                $errorCount = $MembercopyModel->getCount($wap);
                $errorNum = $errorCount;
                $successNum = $list['total_num'] - $errorCount;
                $success = '成功导入' . $successNum . '条，失败' . $errorNum . '条';
                Session::set('fileName', '');
                Db::commit();
                return $this->ajaxSuccess($list, $success);
            } else {
                Db::commit();
                return $this->ajaxFail('获取导入信息失败');
            }
        } catch (\Exception $e) {
            $error = $e->getMessage();
            Db::rollback();
            return $this->ajaxFail($error);
        }
    }

    public function actionRead($filename, $encode = 'utf-8')
    {
        $objPHPE = new \PHPExcel();
        $file_types = explode(".", $filename);
        $file_type = $file_types [count($file_types) - 1];//xls后缀
        if ($file_type == 'xlsx') {
            $PHPReader = new \PHPExcel_Reader_Excel2007($objPHPE);//实例化PHPExcel类
        } else {
            $PHPReader = new \PHPExcel_Reader_Excel5($objPHPE);//实例化PHPExcel类
        }
//        $PHPReader->setReadDataOnly(true);
        $objPHPExcel = $PHPReader->load($filename);
        $objWorksheet = $objPHPExcel->getActiveSheet();
//        $p_w_picpathInfo = $this->extractImageFromWorksheet($objWorksheet, ROOT_PATH . "public/uploads/school/");
//        return $p_w_picpathInfo;
        $highestRow = $objWorksheet->getHighestRow();
        $highestColumn = $objWorksheet->getHighestColumn();
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
        $excelData = array();
        for ($row = 17; $row <= $highestRow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $excelData[$row][] = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            }
        }
        return $excelData;
    }


    function extractImageFromWorksheet($worksheet, $basePath)
    {
        $file = session('fileName');
        if ($file == '') {
            return $this->ajaxFail('请先上传excel文件！');
        }
        $file_path = 'upload/excel/' . $file;//上传后的EXCEL路径
        $result = array();
        $p_w_picpathFileName = "";
        $UploadCtrl = new UploadCtrl;
        foreach ($worksheet->getDrawingCollection() as $drawing) {
            $xy = $drawing->getCoordinates();
            $path = $file_path;

            if ($drawing instanceof \PHPExcel_Worksheet_Drawing) {

                $filename = $drawing->getPath();
                $p_w_picpathFileName = $drawing->getIndexedFilename();

                $tmp = explode(".", $p_w_picpathFileName);
                $tmp[0] = md5(microtime(true));
                $tmp_fileName = implode(".", $tmp);


                $path = $path . $tmp_fileName;

                $res = file_get_contents($filename);


                $result[$xy] = $path;
                $boo = copy($filename, $path);

            }
        }
        return $result;
    }

    function sendStreamFile($url, $res)
    {
        if ($res) {
            $opts = array(
                'http' => array(
                    'method' => 'POST',
                    'header' => 'content-type:multipart/form-data',
                    'content' => array(111)
                )
            );
//            pre($opts);
            $context = stream_context_create($opts);
            $response = file_get_contents($url, false, $context);
            return $response;
        } else {
            return false;
        }
    }

    public function doUploadPic(){
        $MemberModel = new MemberModel;
        $memberId = input('post.vip_id');
        $pic = input('post.pic');
        $res = $MemberModel->editData(array('id'=>$memberId,'pic'=>$pic));
        if($res){
            return $this->ajaxSuccess();
        } else{
            return $this->ajaxFail();
        }
    }
}