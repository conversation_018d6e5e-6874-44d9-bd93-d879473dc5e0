<?php

namespace app\store\controller;

use app\store\model\Createstore as StoreModel;
use app\store\model\PayOrder as payOrderModel;
use app\store\model\PayOrder;
use app\store\model\Staff as StaffModel;
use app\store\model\Logistics as LogisticsModel;
use think\Db;
use think\Request;
use app\api\controller\Pay as PayCtrl;
use app\store\model\DeductStaffCommission as DeStaComModel;
use app\common\model\ExpressInquiry as ExpressInquiryModel;
use app\adminmei\model\LogisticsConfig as LogisticsConfigModel;
use app\api\controller\Orderlist as OrderlistCtrl;
use app\api\controller\Vip as VipCtrl;
use app\store\model\MemberCard as MemberCardModel;
use app\store\model\Orderoperatelog as OrderoperatelogModel;
use app\store\model\OrderDetails as OrderDetailsModel;
use app\store\model\Member as MemberModel;
use app\push\controller\Push as pushObj; // 语音推送 || 公众号推送

use app\api\model\UserBindStore as UserBindStoreModel;
// 自动收货设置
use app\store\model\Storeglobal as StoreglobalModel;
use PHPExcel_IOFactory;

use app\store\model\Couponrecord as CouponRecordModel;

class Order extends Controller
{
    //订单首页
    public function index()
    {
        $user = session("userinfo");
        if ($user) {
            $this->assign("user", $user);
        } else {
            $this->assign("user", '');
        }
        return $this->outPut();
    }

    //获取门店
    public function getStoreList()
    {
        $post = input("post.");
        if ($post['storeid'] != 0) {
            $where['id'] = $post['storeid'];
        }
        $where['merchantid'] = $post['merchantid'];
        $where['status'] = array("neq", 3);
        $Model = new StoreModel;
        $storeList = $Model->getList($where);
        if ($storeList) {
            return $this->ajaxSuccess($storeList, 'ok');
        } else {
            return $this->ajaxFail("错误");
        }
    }

    //获取订单
    public function getOrderList()
    {
        $post = input("post.");
        $user = session("userinfo");
        $storeid=$user['storeid'];
        $Model = new payOrderModel;
        $Staff = new StaffModel;
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        } else {
            if (isset($post['storeid']) && $post['storeid']) {
                $where['store_id'] = $post['storeid'];
            }
        }
        $page['ord'] = "order_time desc";
        $where['merchant_id'] = $user['merchantid'];
        if (isset($post['isVerify']) && $post['isVerify'] != -1) {
            $where['is_verify'] = $post['isVerify'];
        }
        if (!empty($post['StartTime']) || !empty($post['EndTime'])) {
            $startTime = substr($post['StartTime'], 0, -3);
            $endTime = substr($post['EndTime'], 0, -3);
            if (!empty($startTime) && !empty($endTime)) {
                $str_time = $startTime . "," . $endTime;
                $dateTime = array("between", $str_time);
            } elseif (!empty($startTime)) {
                $dateTime = array("egt", $startTime);
            } elseif (!empty($endTime)) {
                $dateTime = array("elt", $endTime);
            }
            if ($post['dateType'] == 1) {
                $where['order_time'] = $dateTime; //下单时间筛选
            } else if($post['dateType'] == 2){
                $where['collection_time'] = $dateTime; //付款时间筛选
            }else if($post['dateType'] == 3){  // 耗材领料单据
                if (isset($post['isVerify']) && $post['isVerify'] ==0) {

                }else if(isset($post['isVerify']) && $post['isVerify'] ==2){
                    $page['ord'] = "time desc";
                }
            }
        }
        if (!empty($post['Payment_method'])) {
            $where['payment_method'] = $post['Payment_method'];
        }
        //订单编号查询
        if (!empty($post['order_number'])) {
            $where['order_number'] = $post['order_number'];
        }
        if (!empty($post['order_type'])) {
            if(isset($post['dateType']) && $post['dateType']==3){
                // 耗材领料单据
                $where['type'] = array('in','1,2');         //  品相单据
                $where['dispatch_type'] = 0;                 //  配送类型：0，非配送，1，到店自提，2，配送。
                $where['state'] = array('in','1,4');        //  状态 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
                if (!empty($post['order_number'])) {
                    $where['state'] = array('in','1,4,5');  //  状态 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
                }
            }else{
                $where['type'] = $post['order_type'];
            }
        }
            if (isset($post['keyword'])&&$post['keyword'] != '') {
                  $uwhere['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['keyword'] . '%');
                  if ($storeid != 0) {
                    $ubswhere['storeid']=$storeid;
                    $ubsfield="userid";
                    $UserBindStoreModel=new UserBindStoreModel;
                    $userlist=$UserBindStoreModel->getfieldList($ubswhere, $ubsfield);
                    $uwhere['id'] = ['in', $userlist];//
                  }
            
                  $ubsfield="id";
                  $MemberModel =new MemberModel;
                  $memberlist=$MemberModel->getfieldList($uwhere, $ubsfield);
                  $where['vip_id'] = ['in', $memberlist];//
                }
            
        // 0 全部，1，待付款 2，待发货，3，已发货，4，已完成，5，已取消，6，已退款，7待还款，8，已还款
        if (!empty($post['switchs'])) {
            if (in_array($post['switchs'],[3,4,5])) {
                $page['ord'] = "time desc";
            } elseif ($post['switchs'] == 2) {
                $page['ord'] = "collection_time desc";
            }
            switch ($post['switchs']){
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                    $where['state'] = $post['switchs'];
                break;
                case 6:
                    // 已退款
                    $where['state'] = array('in','2,3,4');
                    $where['is_refund'] = 1;
                    break;
                case 7:
                    // 待还款
                    $where['state'] = 4;
                    $where['is_debt'] = 1;
                    break;
                case 8:
                    // 已还款
                    $where['state'] = 4;
                    $where['is_debt'] = 2;
                    break;
                default:
                    break;
            }
        }
        if (isset($post['isRefund']) && $post['isRefund'] && !isset($where['is_refund'])) {
            $where['is_refund'] = $post['isRefund'];
        }
        $count = $Model->getCount($where);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $field = array('*');
        $msg = json_encode($where);
        $orderList = $Model->getList($where, $page, $field);
        $orderStateArr = array(
            '1'=>  '待付款', '2'=>  '待发货',
            '3'=>  '已发货', '4'=>  '已完成', '5'=>  '已取消'
        );
        foreach ($orderList as $k => &$v) {
            $v['receivable'] = number_format($v['receivable'] / 100, 2, '.', '');
            $v['order_time'] = date("Y-m-d H:i:s", $v['order_time']);
            if(isset($post['dateType']) && $post['dateType']==3){
                $v['time'] = $v['time']?date("Y-m-d H:i:s", $v['time']):'';
            }
            if (!empty($v['address_info'])) {
                $v['address_info'] = json_decode(htmlspecialchars_decode($v['address_info']));
            }
            $cashier = '--';
            if ($v['cashier_id'] != '') {
                $cashierData = $Staff->getFind(array('id'=>$v['cashier_id']));
                $cashier = ($cashierData && isset($cashierData['nickname']))?$cashierData['nickname']:$cashier;
            }
            $v['cashier'] = $cashier;
            if(isset($orderStateArr[$v['state']])){
                $v['order_state'] = $orderStateArr[$v['state']];
            }
            /* 赠送数据*/
            $presentData = array();
            if(isset($v['present_value']) && $v['present_value']>0){
                $presentData = $Model->getPresentDataAttr($v['id'],$v,1);
            }
            $v['presentData'] = $presentData;
        }
        return $this->ajaxTable($orderList, $count, $msg);
    }

    //详情页
    public function seorder()
    {
        $id = input("get.id");
        $user = session('userinfo');
        $this->assign("user_group", $user['user_group']);
        if ($id) {
            $this->assign("id", $id);
        } else {
            $this->assign("id", '');
        }

        $this->outPut("seeorder");
    }

    //修改备注
    public function EditRemarks()
    {
        $post = input("post.");
        $Model = new payOrderModel;
        $where['id'] = $post['id'];
        $Edit['remarks'] = $post['remarks'];
        $Ed = $Model->updates($where, $Edit);
        if ($Ed) {
            return $this->ajaxSuccess("修改成功");
        } else {
            return $this->ajaxFail("失败");
        }
    }

    //修改取消订单
    public function cancelOrder()
    {
        $userInfo = session("userinfo");
        $merchantId = $userInfo['merchantid'];
        Request::instance()->post(array(
            'merchantid' => $merchantId
        ));
        $OrderlistCtrl = new OrderlistCtrl;
        $res = $OrderlistCtrl->newCancelOrder();
        return $res;
    }

    //订单详情
    public function getOrderDetails()
    {
        $user = session("userinfo");
        $post = input("post.");
        $Model = new PayOrderModel;
        $Staff = new StaffModel;
        $where['id'] = $post['id'];
        $where['merchant_id'] = $user['merchantid'];
        if ($user['storeid']) {
            $where['store_id'] = $user['storeid'];
        }
        $orderDetails = $Model->getFind($where);
        $orderDetails['order_time'] = date("Y-m-d H:i:s", $orderDetails['order_time']);
        $orderDetails['collection_time'] = $orderDetails['collection_time'] ? date("Y-m-d H:i:s", $orderDetails['collection_time']) : '';
        $orderDetails['receivable'] = number_format($orderDetails['receivable'] / 100, 2, '.', '');
        if (!empty($orderDetails['address_info'])) {
            $orderDetails['address_info'] = json_decode(htmlspecialchars_decode($orderDetails['address_info']));
        }
        if (!empty($orderDetails['time'])) {
            $orderDetails['time'] = date("Y-m-d H:i:s", $orderDetails['time']);
        }
        if ($orderDetails['cashier_id'] != '') {
            $cashier['id'] = $orderDetails['cashier_id'];
            $cashierData = $Staff->getFind($cashier);
            $orderDetails['cashier'] = $cashierData['nickname'] ? $cashierData['nickname'] : "--";
        } else {
            $orderDetails['cashier'] = "--";
        }
        if ($orderDetails['state'] == 1) {
            $orderDetails['order_state'] = "待付款";
        } elseif ($orderDetails['state'] == 2) {
            $orderDetails['order_state'] = "待发货";
        } elseif ($orderDetails['state'] == 3) {
            $orderDetails['order_state'] = "已发货";
        } elseif ($orderDetails['state'] == 4) {
            $orderDetails['order_state'] = "已完成";
        } elseif ($orderDetails['state'] == 5) {
            $orderDetails['order_state'] = "已取消";
        }
        if(isset($orderDetails['present_value']) && $orderDetails['present_value']>0){
            $orderDetails['needImgUrl'] = 1; // 能获取图片
            $orderDetails['needUseNum'] = 1; // 能获取使用次数
            $orderDetails['presentData'] = $Model->getPresentDataAttr($orderDetails['id'],$orderDetails);
        }
        if ($orderDetails) {
            return $this->ajaxSuccess($orderDetails, 'ok');
        } else {
            return $this->ajaxFail("查询失败");
        }
    }


    public function EditAddress()
    {
        $post = input("post.");
        $Model = new PayOrderModel;
        $where['id'] = $post['id'];
        $Edit['address_info'] = $post['address'];
        $Ed = $Model->updates($where, $Edit);
        if ($Ed) {
            return $this->ajaxSuccess("修改成功");
        } else {
            return $this->ajaxFail("修改失败");
        }
    }

    //获取快递公司
    public function getLogistics()
    {
        $Model = new LogisticsModel;
        $list = $Model->getList();
        if ($list) {
            return $this->ajaxSuccess($list, "ok");
        } else {
            return $this->ajaxFail("失败");
        }
    }

    //物流信息
    public function ExpressData()
    {
        $post = input("post.");
        $where['id'] = $post['logisticsId'];
        $data['courier_num'] = $post['courier_num'];
        $ExpressInquiryModel = new ExpressInquiryModel;
        $LogisticsConfigModel = new LogisticsConfigModel;
        $LogisticsModel = new LogisticsModel;
        $config = $LogisticsConfigModel->getFind(array());
        $logistics = $LogisticsModel->getFind($where);
        $list = $ExpressInquiryModel->synquery($config, $logistics, $data);
        if ($list) {
            return $this->ajaxSuccess($list, 'ok');
        } else {
            return $this->ajaxFail("失败");
        }
    }

    //发货操作
    public function EditDelivery()
    {
        $post = input("post.");
        $Model = new payOrderModel;
        $OrderDetails = new OrderDetailsModel;
        $membermodel = new MemberModel; // 会员表
        $where['id'] = $post['orderid'];
        $field = array(
            'id',
            'vip_id',
            'merchant_id',
            'store_id',
            'state',
            'address_info',
            'is_refund'
        );
        $one = $Model->getFinds($where, $field);
        if (!$one) {
            return $this->ajaxFail('单据未找到');
        } else if ($one['state'] != 2) {
            $msg = '取货';
            if ($post['dispatch_type'] == 2) {
                $msg = '发货';
            }
            return $this->ajaxFail("不是待{$msg}订单，请确认");
        } else if ($one['is_refund'] != 2) {
            return $this->ajaxFail("订单已退款，请确认");
        }
        if ($post['type'] == 2) {
            $StoreglobalModel = new StoreglobalModel;
            $data = array(
                'map' => array('merchantid' => $one['merchant_id'], 'storeid' => 0)
            );
            $set = $StoreglobalModel->getFindData($data);
            $Edit['state'] = 3;
            $Edit['delivery'] = $post['delivery'];
            $Edit['courier_num'] = $post['courier_num'];
            $Edit['logistics'] = $post['logistics'];
            $Edit['time'] = time();
            if ($set && isset($set['auto_receiving_day']) && $set['auto_receiving_day'] > 0) {
                $Edit['auto_receiving_time'] = $Edit['time'] + $set['auto_receiving_day'] * 86400;
            }
            $Ed = $Model->updates($where, $Edit);
            if ($Ed) {
                // 查询openid 及发送公众号模版消息
                $uid = $one['vip_id'];
                $merchantid = $one['merchant_id'];
                $memberMap['id'] = $uid;
                $vip = $membermodel->getFind($memberMap); // 会员信息
                //查询预约名称
                $wap['order_id'] = $one['id'];
                $Fi = array('name');
                $goodsname = $OrderDetails->getFind($wap, $Fi);
                if (isset($goodsname['name'])) {
                    $sername = $goodsname['name'];
                } else {
                    $sername = '无';
                }
                if (isset($one['address_info']) && !empty($one['address_info'])) {
                    $orderDetails = json_decode(htmlspecialchars_decode($one['address_info']), true);
                    $address = $orderDetails['site'] ? $orderDetails['site'] : '';
                } else {
                    $address = '';
                }
                //查询物流信息
                if (isset($post['logistics']) && $post['logistics'] != '') {
                    $Wh['id'] = $post['logistics'];
                    $LogisticsModel = new LogisticsModel;
                    $LogField = array('name');
                    $Logistics = $LogisticsModel->getFind($Wh, $LogField);
                    $LogisticsName = $Logistics['name'];
                } else {
                    $LogisticsName = '--';
                }
                if (!empty($vip['wxopenid'])) {
                    if(self::PUSH_STATUS==1){
                        $addTaskData = array(
                            'type'=>3,
                            'merchantid'=>$one['merchant_id'],
                            'uid'=>$one['vip_id'],
                            'storeid'=>$one['store_id'],
                            'order_id'=>$one['id'],                 //  订单id（预约单和订单）
                            'order_type'=>1,                        // 单据类型：1，订单，2，预约单
                        );
                        $this->addTask($addTaskData);
                    }else {
                        $wxpushObj = new pushObj;
                        $model = 4; // 1.预约通知 2.取消预约 3.购物成功 4.订单已发货
                        $wxData = array(
                            "courier_num" => $post['courier_num'],  // 快递单号
                            "logistics" => $LogisticsName,  // 快递公司
                            'goodsname' => $sername, // 商品名称
                            'address' => $address, // 地址
                            'store_id' => $one['store_id'],
                        );
                        $wxpushObj->wxpush($uid, $merchantid, $wxData, $model);
                    }
                }
                return $this->ajaxSuccess("发货成功");
            } else {
                return $this->ajaxFail("发货失败");
            }
        } else {
            $Edit['state'] = 4;
            $Edit['time'] = time();
            $Ed = $Model->updates($where, $Edit);
            if ($Ed) {
                if(self::PUSH_STATUS==1){
                    $addTaskData = array(
                        'type'=>6,
                        'merchantid'=>$one['merchant_id'],
                        'uid'=>$one['vip_id'],
                        'storeid'=>$one['store_id'],
                        'order_id'=>$one['id'],                 //  订单id（预约单和订单）
                        'order_type'=>1,                        // 单据类型：1，订单，2，预约单
                    );
                    $this->addTask($addTaskData);
                }
                return $this->ajaxSuccess("取货成功");
            } else {
                return $this->ajaxFail("取货失败");
            }
        }

    }

    //获取时间
    public function GetTime()
    {
        $num = input("post.num");
        $data['StartTime'] = strtotime(date("Y-m-d 00:00:00", strtotime("-" . $num . " day"))) * 1000;
        $data['EndTime'] = time() * 1000;
        return $this->ajaxSuccess($data, "ok");
    }

    public function orderRefund()
    {
        $post = input("post.");
        $userInfo = session("userinfo");
        $merchantId = $userInfo['merchantid'];
        $storeid = $userInfo['storeid'];
        if (!$storeid) {
            $storeid = $post['storeid'];
        }
        if ($userInfo['user_group'] == 3) {
            // 商家管理员
            $cashier_Id = 0;
        } else {
            $cashier_Id = $userInfo['group_id'];
            // 查询员工是否有权限删除及删除操作密码验证
            $StaffModel = new StaffModel;
            $wap['merchantid'] = $merchantId;
            $wap['storeid'] = $storeid;
            $wap['id'] = $cashier_Id;
            $field = 'id,addtime,is_refund,password';
            $staffInfo = $StaffModel->getFind($wap,$field);
            if ($staffInfo['is_refund'] != 1){
                return $this->ajaxFail('您没有退款操作权限，请联系管理员开启');
            }else{
                if (empty($staffInfo['password'])){
                    return $this->ajaxFail('请设置退款权限密码');
                }
            }
            $data = $post['password'];
            $key = date('YmdHis', $staffInfo['addtime']);
            $password1 = encrypt($data, $key);
            if ($password1 != $staffInfo['password']){
                return $this->ajaxFail('密码错误！');
            }
        }
        Request::instance()->post(array(
            'merchantid' => $merchantId,
            'storeid' => $storeid,
            'cashierId' => $cashier_Id,
            'shift_no' => $post['shift_no'],
        ));
        $post = input("post.");
        $PayCtrl = new PayCtrl;
        $res = $PayCtrl->refund();
        return $res;
    }

    /*
     * 查看修改员工业绩页面
     **/
    public function deduct()
    {
        $data = input('get.');
        $orderNo = $data['orderNo'];
        $this->assign('orderNo', $orderNo);
        $user = session('userinfo');
        $StaffModel = new StaffModel;
        if ($user['user_group'] == 4) {
            $where['userid'] = $user['id'];
            $where['merchantid'] = $user['merchantid'];
            $field = array('is_edit_authority');
            $staffInfo = $StaffModel->getFind($where, $field);
            if (isset($staffInfo['is_edit_authority'])) {
                $is_edit_authority = $staffInfo['is_edit_authority'];
            } else {
                $is_edit_authority = 0;
            }
        } else {
            $is_edit_authority = 0;
        }
        $this->assign('edit_authority', $is_edit_authority);
        $this->outPut("deduct");
    }

    /*
     * 获取员工业绩提成数据
     * */
    public function getOrderDeductData()
    {
        $data = input('post.');
        $orderNo = $data['orderNo'];
        $userInfo = session('userinfo');
        try {
            $where = array(
                'merchant_id' => $userInfo['merchantid'],
                'order_number' => $orderNo
            );
            $PayorderModel = new PayorderModel;
            $field = 'id,order_number,type,store_id as storeid,order_time,receivable,manually,merchant_id as merchantid';
            $append = array(
                'performance'
            );
            $data = $PayorderModel->getOrderDeductData($where, $field, $append);
            if (!$data) {
                $msg = $PayorderModel->error;
                $msg = $msg ? $msg : '订单不存在';
                return $this->ajaxFail($msg);
            }
            return $this->ajaxSuccess($data);
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return $this->ajaxFail($this->error);
        }
    }

    public function getStaff()
    {
        $data = input('post.');
        $userInfo = session('userinfo');
        try {
            $where = array(
                'merchantid' => $userInfo['merchantid'],
                'storeid' => $data['storeid'],
                'is_dimission' => 1,
                'status' => 1
            );
            if (isset($data['isTech']) && $data['isTech'] == 2) {
                $where['is_craftsmen'] = 2;
            }
            $StaffModel = new StaffModel;
            $field = 'id,nickname,is_craftsmen as isTech,job_num';
            $append = array();
            $data = $StaffModel->getAll($where, $field, $append);
            if (!$data) {
                $msg = $StaffModel->error;
                $msg = $msg ? $msg : '未找到数据';
                return $this->ajaxFail($msg);
            }
            return $this->ajaxSuccess($data);
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return $this->ajaxFail($this->error);
        }
    }

    public function saveDeductData()
    {

        try {
            Db::startTrans();
            $post = input('post.');
            $post['addArr'] = json_decode(htmlspecialchars_decode($post['addArr']), true);
            $post['delArr'] = json_decode(htmlspecialchars_decode($post['delArr']), true);
            $post['saveArr'] = json_decode(htmlspecialchars_decode($post['saveArr']), true);
//            dump($post);die;
            $orderNo = $post['orderNo'];
            $userInfo = session('userinfo');
            $PayorderModel = new PayorderModel;
            $field = 'id,order_number,type,store_id as storeid,order_time,receivable,manually,merchant_id as merchantid';
            $append = array();
            $where = array(
                'merchant_id' => $userInfo['merchantid'],
                'order_number' => $orderNo
            );
            $data = $PayorderModel->getOrderDeductData($where, $field, $append);
            if (!$data) {
                Db::rollback();
                $msg = $PayorderModel->error;
                $msg = $msg ? $msg : '订单不存在';
                return $this->ajaxFail($msg);
            }
            $DeStaComModel = new DeStaComModel;
            $staffNameAddStr = '';
            if (count($post['addArr']) > 0) {
                foreach ($post['addArr'] as $k => &$v) {
                    $staffNameAddStr .= $v['staffName'] . '、';
                    unset($v['id']);
                    $v['order_time'] = intval($v['order_time']);
                    unset($v['staffName']);
                    $v['performance'] = round($v['performance'] * 100, 0);
                    $v['performance_proportion'] = round($v['performance_proportion'] * 100, 0);
                    $v['commission'] = round($v['commission'] * 100, 0);
                    $v['commission_proportion'] = round($v['commission_proportion'] * 100, 0);

                }
                $res1 = $DeStaComModel->addAll($post['addArr']);
                if (!$res1) {
                    Db::rollback();
                    $msg = $DeStaComModel->error;
                    $msg = $msg ? $msg : '新增失败';
                    return $this->ajaxFail($msg);
                }
            }
            $staffNameDelStr = '';
            if (count($post['delArr']) > 0) {
                $delArr = array();
                foreach ($post['delArr'] as $kd => $vd) {
                    $staffNameDelStr .= $vd['staffName'] . '、';
                    $delArr[] = array(
                        'id' => $vd['id'],
                        'status' => 2
                    );
                }
                $res1 = $DeStaComModel->dosaveAll($delArr);
                if (!$res1) {
                    Db::rollback();
                    $msg = $DeStaComModel->error;
                    $msg = $msg ? $msg : '删除失败';
                    return $this->ajaxFail($msg);
                }
            }
            $staffNameSaveStr = '';
            if (count($post['saveArr']) > 0) {
                $saveArr = array();
                foreach ($post['saveArr'] as $k => $v) {
                    $staffNameSaveStr .= $v['staffName'] . '、';
                    //$item['order_time'] = intval($v['order_time']);
                    unset($v['staffName']);
                    $item = array();
                    $item['id'] = $v['id'];
                    $item['performance'] = round($v['performance'] * 100, 0);
                    $item['performance_proportion'] = round($v['performance_proportion'] * 100, 0);
                    $item['commission'] = round($v['commission'] * 100, 0);
                    $item['commission_proportion'] = round($v['commission_proportion'] * 100, 0);
                    $saveArr[] = $item;
                }
                $res1 = $DeStaComModel->dosaveAll($saveArr);
                if (!$res1) {
                    Db::rollback();
                    $msg = $DeStaComModel->error;
                    $msg = $msg ? $msg : '新增失败';
                    return $this->ajaxFail($msg);
                }
            }
            Db::commit();
            //操作日志
            $addArrCount = count($post['addArr']);
            $delArrCount = count($post['delArr']);
            $saveArrCount = count($post['saveArr']);
            $storeName = $this->storeName($userInfo['storeid']);
            self::$logMsg = $storeName . ' 删除 ' . $delArrCount . ' 条业绩提成，新增 ' . $addArrCount . ' 条业绩提成，保存 ' . $saveArrCount . ' 条业绩提成。';
            //员工业绩修改操作日志
            if ($staffNameAddStr != '') {
                $staffNameAddStr = '添加了 ' . substr($staffNameAddStr, 0, -3) . ' 的员工业绩。';
            }
            if ($staffNameDelStr != '') {
                $staffNameDelStr = '删除了 ' . substr($staffNameDelStr, 0, -3) . ' 的员工业绩。';
            }
            if ($staffNameSaveStr != '') {
                $staffNameSaveStr = '保存了 ' . substr($staffNameSaveStr, 0, -3) . ' 的员工业绩。';
            }
            $OrderoperatelogModel = new OrderoperatelogModel;
            $logData['order_id'] = $data['id'];
            $logData['merchantid'] = $userInfo['merchantid'];
            $logData['operator'] = $userInfo['nickname'];
            $logData['type'] = 1;
            $logData['addtime'] = time();
            $logData['content'] = $staffNameAddStr . $staffNameDelStr . $staffNameSaveStr;
            $OrderoperatelogModel->addLog($logData);
            // 操作日志结束

            return $this->ajaxSuccess('设置成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error = $e->getMessage();
            return $this->ajaxFail($this->error);
        }
    }

    /*
     * 获取会员卡项详情
     * */
    public function getVipCardData()
    {
        $post = input("post.");
        $userInfo = $this->userinfo;
        $merchantId = $userInfo['merchantid'];
        $storeid = $userInfo['storeid'];
        if (!$storeid) {
            $storeid = $post['storeid'];
        }
        if(!$storeid){
			return $this->ajaxFail('店铺id不能为空');
		}
        $orderType = 0;
		$memberId = $post['memberId'];
		$orderId = $post['order_id'];
        if(isset($post['order_type']) && $post['order_type']==7){
			$orderType = 7;
        	// 获客活动获取详情
			$where = array(
				'plan_type'=>1,
				'plan_order_id'=>$orderId,
				'memberid'=>$memberId
			);
			$field = array(
				'*'
			);
			$order = 'get_time desc';
			$append = array(
				'expireTime'
			);
			$couponData = (new CouponRecordModel)->getAll($where,$field,$order,$append);
		}else{
			$couponData = array();
		}
        $MemberCardModel = new MemberCardModel;
        $where = array(
            'merchantid' => $merchantId,
            'memberid' => $memberId,
            'order_id' => $orderId
        );
        $one = $MemberCardModel->getFind($where, 'id');
        if ($one) {
        	$cardId = $one['id'];
			Request::instance()->post(array(
				'merchantid' => $merchantId,
				'storeid' => $storeid,
				'memberId' => $memberId,
				'id' => $cardId,
			));
			$VipCtrl = new VipCtrl;
			$res = $VipCtrl->getVipCardData();
			if (is_string($res)){
				$res = json_decode($res,true);
			}
			$res['couponData'] = $couponData;
			return $res;
        }
        if($orderType==7){
        	$res = $this->ajaxSuccess([]);
			$res['couponData'] = $couponData;
			return $res;
		}
		return $this->ajaxFail('未找到订单购买的会员卡项');
    }

    //导出订单列表
    public function importOrderList()
    {
        $get = input('get.');
        $user = session('userinfo');
        $Model = new payOrderModel;
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        } else {
            if (isset($get['sid']) && $get['sid'] != 0) {
                $where['store_id'] = $get['sid'];
            }
        }
        $where['merchant_id'] = $user['merchantid'];

        $startTime = strtotime($get['start']);
        $endTime = strtotime($get['end'])+86399;

        if ($get['d_type'] == 1) {
            $where['order_time'] =   array("between", $startTime.','.$endTime);; //下单时间筛选
        } else {
            $where['collection_time'] =   array("between", $startTime.','.$endTime);; //付款时间筛选
        }
        if ($get['type'] != 0){
            $where['state'] = $get['type'];
        }
        if ($get['o_type'] != 0) {
            $where['type'] = $get['o_type'];
        }
        $Staff = new StaffModel;
        $field = "*";
        $order = "";
        $append = array(
            'orderInfo',
            'goodsInfo',
            'storeTag',
            'vip',
            'dispatchFee',//运费
            'logisticsName',//快递公司名称
            'payment',//
            'refundPayment',//
        );
        $sql = $Model->getOrderList($where, $field, $order, $append);
        $title = '订单明细报表';
        $cellName = array(
            array(
                'storeTag', '门店名称', '24',1
            ),
            array(
                'order_number', '订单号', '24',1
            ),
            array(
                'type', '订单类型', '24',1
            ),
            array(
                'state', '订单状态', '24',1
            ),
            array(
                'source_type', '订单来源', '24',1
            ),
            array(
                'order_time', '下单日期', '24',1
            ),
            array(
                'collection_time', '收款日期', '24',1
            ),
            array(
                'time', '完成日期', '24',1
            ),
            array(
                'cashier_id', '收银员', '24',1
            ),
            array(
                'is_refund', '是否退款', '24',1
            ),
            array(
                'orderInfo.name', '商品名称', '24'
            ),
            array(
                'orderInfo.type', '商品类型', '24'
            ),
            array(
                'orderInfo.price', '单价', '24'
            ),
            array(
                'orderInfo.num', '数量', '24'
            ),
            array(
                'orderInfo.Craftsman', '理疗师', '24'
            ),
            array(
                'orderInfo.cashier', '销售', '24'
            ),
            array(
                'orderInfo.equity_type', '权益类型', '24'
            ),
            array(
                'orderInfo.reduceprice', '优惠金额', '24'//优惠金额
            ),
            array(
                'receivable', '金额', '24',1
            ),
            array(
                'wxPay', '微信支付', '24',1
            ),
            array(
                'aliPay', '支付宝支付', '24',1
            ),
            array(
                'cashPay', '现金支付', '24',1
            ),
            array(
                'xcxPay', '小程序微信支付', '24',1
            ),
            array(
                'balancePay', '会员余额支付', '24',1
            ),
            array(
                'otherPay', '自定义记账', '24',1
            ),
            array(
                'deduction', '抵扣权益优惠', '24',1
            ),
            array(
                'manually', '手动改价优惠', '24',1
            ),
            array(
                'dismoney', '折扣权益优惠', '24',1
            ),
            array(
                'is_debt', '是否欠款', '24',1
            ),
            array(
                'debt_value', '欠款金额', '24',1
            ),
            array(
                'remarks', '订单备注', '24',1
            ),
            array(
                'customer_remark', '买家留言', '24',1
            ),
            array(
                'member_name', '会员姓名', '24',1
            ),
            array(
                'member_phone', '会员手机号', '24',1
            ),
            array(
                'member_number', '会员编号', '24',1
            ),
            array(
                'member_grade', '会员等级', '24',1
            ),
            array(
                'addressName', '收货人', '24',1
            ),
            array(
                'addressTel', '收货电话', '24',1
            ),
            array(
                'addressInfo', '收货地址', '24',1
            ),
            array(
                'logisticsName', '物流公司', '24',1
            ),
            array(
                'courier_num', '物流单号', '24',1
            ),
        );
        $i = 3;
        $arr = array();
        foreach ($sql as $k1=>$v1) {
            //计算合并单元格数值
            $v1['start'] = $i;
            $i+=count($v1['orderInfo'])-1;
            $v1['end'] = $i;
            $i++;
            $v1['receivable'] = number_format($v1['receivable'] / 100, 2, '.', '');
            //订单类型: 1服务 2产品（1,2品项） 3售卡 4充值 5充卡 6 快速收款
            switch ($v1['type']){
                case 1:
                    $v1['type'] = '品项';
                    break;
                case 2:
                    $v1['type'] = '品项';
                    break;
                case 3:
                    $v1['type'] = '售卡';
                    break;
                case 4:
                    $v1['type'] = '充值';
                    break;
                case 5:
                    $v1['type'] = '充卡';
                    break;
                case 6:
                    $v1['type'] = '快速收款';
                    break;
				case 7:
					$v1['type'] = '获客活动';
					break;
            }
            $v1['order_time'] = date('Y-m-d H:i:s',$v1['order_time']);//下单时间
            if (isset($v1['collection_time']) && !empty($v1['collection_time'])){
                $v1['collection_time'] = date('Y-m-d H:i:s',$v1['collection_time']);//付款时间
            }else{
                $v1['collection_time'] = '--';
            }
            if (isset($v1['time']) && !empty($v1['time'])){
                $v1['time'] = date('Y-m-d H:i:s',$v1['time']);//完成时间
            }else{
                $v1['time'] = '--';
            }

            //订单状态: 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
            switch ($v1['state']){
                case 1:
                    $v1['state'] = '待付款';
                    break;
                case 2:
                    $v1['state'] = '待发货';
                    break;
                case 3:
                    $v1['state'] = '已发货';
                    break;
                case 4:
                    $v1['state'] = '已完成';
                    break;
                case 5:
                    $v1['state'] = '已取消';
                    break;
            }
            if ($v1['is_refund'] == 1){//是否退款
                $v1['is_refund'] = '是';
            }else{
                $v1['is_refund'] = '否';
            }

            if ($v1['remarks']==''){  //订单备注
                $v1['remarks'] = '--';
            }
            if ($v1['customer_remark']==''){  //订单留言/顾客备注
                $v1['customer_remark'] = '--';
            }
            if ($v1['cashier_id'] != '') {
                $cashier['id'] = $v1['cashier_id'];
                $cashierData = $Staff->getFind($cashier);
                $v1['cashier_id'] = isset($cashierData['nickname']) ? $cashierData['nickname'] : "--";
            } else {
                $v1['cashier_id'] = "--";
            }
            //会员信息
            if ($v1['vip_id']!=0){
                if (isset($v1['vip']['member_name'])){
                    $v1['member_name'] = $this->removeEmoji($v1['vip']['member_name']);
                }else{
                    $v1['member_name'] = '--';
                }
                $v1['member_number'] =  isset($v1['vip']['member_number'])?$v1['vip']['member_number']:'--';
                $v1['member_phone'] =  isset($v1['vip']['phone'])?$v1['vip']['phone']:'--';
                $v1['member_grade'] =  isset($v1['vip']['Grade'])?$v1['vip']['Grade']:'--';
            }else{
                $v1['member_name'] = '--';
                $v1['member_number'] = '--';
                $v1['member_phone'] = '--';
                $v1['member_grade'] = '--';
            }
            if (!empty($v1['address_info'])) {
                $address_info = json_decode(htmlspecialchars_decode($v1['address_info']), true);
                $v1['addressName'] = isset($address_info['name']) ? $address_info['name'] : '--';
                $v1['addressTel'] =  isset($address_info['tel']) ? $address_info['tel'] : '--';
                $v1['addressInfo'] =  isset($address_info['site']) ? $address_info['site'] : '--';
            }else{
                $v1['addressName'] = '--';
                $v1['addressTel'] = '--';
                $v1['addressInfo'] = '--';
            }
            //快递信息
            if ($v1['logisticsName'] == ''){
                $v1['logisticsName'] = '--';
            }
            if ($v1['courier_num'] == ''){
                $v1['courier_num'] = '--';
            }
            //支付方式及金额 1微信 2支付宝 3现金 4小程序微信 5会员余额 10其他   大于10----自定义记账
            $v1['wxPay'] = 0;//微信
            $v1['aliPay'] = 0;//支付宝
            $v1['cashPay'] = 0;//现金
            $v1['xcxPay'] = 0;//小程序微信
            $v1['balancePay'] = 0;//会员余额
            $v1['otherPay'] = 0;//自定义记账
            if (count($v1['payment'])>0){
                foreach ($v1['payment'] as $ka=>&$kz) {
                    switch ($kz['payType']){
                        case 1:
                            $v1['wxPay'] += $kz['trade_amount'];
                            break;
                        case 2:
                            $v1['aliPay'] += $kz['trade_amount'];
                            break;
                        case 3:
                            $v1['cashPay'] += $kz['trade_amount'];
                            break;
                        case 4:
                            $v1['xcxPay'] += $kz['trade_amount'];
                            break;
                        case 5:
                            $v1['balancePay'] += $kz['trade_amount'];
                            break;
                        default:
                            $v1['otherPay'] += $kz['trade_amount'];
                            break;
                    }
                }
            }
            // 是否欠款
            switch ($v1['is_debt']){
                case 0:
                    $v1['is_debt'] = '否';
                    break;
                case 1:
                    $v1['is_debt'] = '是';
                    break;
                case 2:
                    $v1['is_debt'] = '已还款';
                    break;
            }
            $v1['debt_value'] = number_format($v1['debt_value'] / 100, 2, '.', '');

            $item = $v1;
            unset($item['orderInfo']);
            foreach ($v1['orderInfo'] as $k2=>$v2) {
                if ($k2>0){
                    $item['orderInfo'] = 0;
                }
                switch ($v2['type']){ //1 服务 2产品 3卡项 4充值  5 直接收款
                    case 1:
                        $v2['type'] = '服务';
                        break;
                    case 2:
                        $v2['type'] = '产品';
                        break;
                    case 3:
                        $v2['type'] = '卡项';
                        break;
                    case 4:
                        $v2['type'] = '充值';
                        break;
                    case 5:
                        $v2['type'] = '直接收款';
                        break;
                }
                switch ($v2['equity_type']){ //权益类型 1 无权益 2折扣 3抵扣 4手动改价'
                    case 1:
                        $v2['equity_type'] = '无权益';
                        break;
                    case 2:
                        $v2['equity_type'] = '折扣优惠';
                        break;
                    case 3:
                        $v2['equity_type'] = '会员卡抵扣1次';
                        break;
                    case 4:
                        $v2['equity_type'] = '手动改价';
                        break;
                    default:
                        $v2['equity_type'] = '--';
                        break;
                }

                foreach ($v2 as $k3=>$v3) {
                    $item['orderInfo.'.$k3] = $v3;
                }
                $arr[] = $item;
            }
        }
        $this->exportOrderExcel($title, $cellName, $arr);

    }

    public function exportOrderExcel($title, $cellName, $data)
    {
        //引入核心文件
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        //定义配置
        $topNumber = 2;//表头有几行占用
        $xlsTitle = iconv('utf-8', 'gb2312', $title);//文件名称
        $fileName = $title . date('_YmdHis');//文件名称
        $cellKey = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
            'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM',
            'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ'
        );

        //写在处理的前面（了解表格基本知识，已测试）
//     $objPHPExcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(20);//所有单元格（行）默认高度
//     $objPHPExcel->getActiveSheet()->getDefaultColumnDimension()->setWidth(20);//所有单元格（列）默认宽度
//     $objPHPExcel->getActiveSheet()->getRowDimension('1')->setRowHeight(30);//设置行高度
//     $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(30);//设置列宽度
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);//设置文字大小
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);//设置是否加粗
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);// 设置文字颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//设置文字居左（HORIZONTAL_LEFT，默认值）中（HORIZONTAL_CENTER）右（HORIZONTAL_RIGHT）
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);//设置填充颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->getStartColor()->setARGB('FF7F24');//设置填充颜色

        //处理表头标题
        $objPHPExcel->getActiveSheet()->mergeCells('A1:' . $cellKey[count($cellName) - 1] . '1');//合并单元格（如果要拆分单元格是需要先合并再拆分的，否则程序会报错）
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A1', '订单信息');
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        //处理表头
        foreach ($cellName as $k => $v) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue($cellKey[$k] . $topNumber, $v[1]);//设置表头数据
//            $objPHPExcel->getActiveSheet()->freezePane($cellKey[$k] . ($topNumber + 1));//冻结窗口
            $objPHPExcel->getActiveSheet()->getStyle($cellKey[$k] . $topNumber)->getFont()->setBold(true);//设置是否加粗
            $objPHPExcel->getActiveSheet()->getStyle($cellKey[$k] . $topNumber)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
            if (isset($v[2]) && $v[2] > 0)//大于0表示需要设置宽度
            {
                $objPHPExcel->getActiveSheet()->getColumnDimension($cellKey[$k])->setWidth($v[2]);//设置列宽度
            }
        }
        //处理数据
        foreach ($data as $k => $v) {
            foreach ($cellName as $k1 => $v1) {
                $objPHPExcel->getActiveSheet()->setCellValue($cellKey[$k1] . ($k + 1 + $topNumber), $v[$v1[0]]);
                if ($v['end'] > 0) {
                    if (isset($v1[3]) && $v1[3] == 1)//这里表示合并单元格
                    {
                        $objPHPExcel->getActiveSheet()->mergeCells($cellKey[$k1] . $v['start'] . ':' . $cellKey[$k1] . $v['end']);
                        $objPHPExcel->getActiveSheet()->getStyle($cellKey[$k1] . $v['start'])->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
                    }
                }
            }
        }
        //导出execl
        header('pragma:public');
        header('Content-type:application/vnd.ms-excel;charset=utf-8;name="' . $xlsTitle . '.xlsx"');
        header("Content-Disposition:attachment;filename=$fileName.xlsx");//attachment新窗口打印inline本窗口打印
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
		ob_end_clean();
        $objWriter->save('php://output');
        exit;
    }

    // 过滤表情字符
    private function removeEmoji($clean_text)
    {
        // 方法1：只取合法字符
        preg_match_all("/[\x{4e00}-\x{9fa5}|0-9|a-z|A-Z|_]/u", $clean_text, $matches);
        $clean_text = isset($matches[0]) ? implode('', $matches[0]) : '';


        // 方法2：去除表情字符，但测试过程中发现无法出去“国旗”表情
        // Match Emoticons
        $regexEmoticons = '/[\x{1F600}-\x{1F64F}]/u';
        $clean_text = preg_replace($regexEmoticons, '', $clean_text);

        // Match Miscellaneous Symbols and Pictographs
        $regexSymbols = '/[\x{1F300}-\x{1F5FF}]/u';
        $clean_text = preg_replace($regexSymbols, '', $clean_text);

        // Match Transport And Map Symbols
        $regexTransport = '/[\x{1F680}-\x{1F6FF}]/u';
        $clean_text = preg_replace($regexTransport, '', $clean_text);

        // Match Miscellaneous Symbols
        $regexMisc = '/[\x{2600}-\x{26FF}]/u';
        $clean_text = preg_replace($regexMisc, '', $clean_text);

        // Match Dingbats
        $regexDingbats = '/[\x{2700}-\x{27BF}]/u';
        $clean_text = preg_replace($regexDingbats, '', $clean_text);

        return $clean_text;
    }

    // 跨店订单
    public function across(){
        $user = session("userinfo");
        if ($user) {
            $this->assign("user", $user);
        } else {
            $this->assign("user", '');
        }
        return $this->outPut();
    }

    public function getAcrossOrderList(){
        $post = input("post.");
        $user = session("userinfo");
        $Model = new payOrderModel;
        $Staff = new StaffModel;
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        } else {
            if (isset($post['storeid']) && $post['storeid']) {
                $where['store_id'] = $post['storeid'];
            }
        }
        $page['ord'] = "order_time desc";
        $where['merchant_id'] = $user['merchantid'];
        if (isset($post['isVerify']) && $post['isVerify'] != -1) {
            $where['is_verify'] = $post['isVerify'];
        }
        if (!empty($post['StartTime']) || !empty($post['EndTime'])) {
            $startTime = substr($post['StartTime'], 0, -3);
            $endTime = substr($post['EndTime'], 0, -3);
            if (!empty($startTime) && !empty($endTime)) {
                $str_time = $startTime . "," . $endTime;
                $dateTime = array("between", $str_time);
            } elseif (!empty($startTime)) {
                $dateTime = array("egt", $startTime);
            } elseif (!empty($endTime)) {
                $dateTime = array("elt", $endTime);
            }
            if ($post['dateType'] == 1) {
                $where['order_time'] = $dateTime; //下单时间筛选
            } else if($post['dateType'] == 2){
                $where['collection_time'] = $dateTime; //付款时间筛选
            }else if($post['dateType'] == 3){  // 耗材领料单据
                if (isset($post['isVerify']) && $post['isVerify'] ==0) {

                }else if(isset($post['isVerify']) && $post['isVerify'] ==2){
                    $page['ord'] = "time desc";
                }
            }
        }
        if (!empty($post['Payment_method'])) {
            $where['payment_method'] = $post['Payment_method'];
        }
        //订单编号查询
        if (!empty($post['order_number'])) {
            $where['order_number'] = $post['order_number'];
        }
        if (!empty($post['order_type'])) {
            if(isset($post['dateType']) && $post['dateType']==3){
                // 耗材领料单据
                $where['type'] = array('in','1,2');         //  品相单据
                $where['dispatch_type'] = 0;                 //  配送类型：0，非配送，1，到店自提，2，配送。
                $where['state'] = array('in','1,4');        //  状态 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
                if (!empty($post['order_number'])) {
                    $where['state'] = array('in','1,4,5');  //  状态 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
                }
            }else{
                $where['type'] = $post['order_type'];
            }
        }
        // 0 全部，1，待付款 2，待发货，3，已发货，4，已完成，5，已取消，6，已退款，7待还款，8，已还款
        if (!empty($post['switchs'])) {
            if (in_array($post['switchs'],[3,4,5])) {
                $page['ord'] = "time desc";
            } elseif ($post['switchs'] == 2) {
                $page['ord'] = "collection_time desc";
            }
            switch ($post['switchs']){
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                    $where['state'] = $post['switchs'];
                    break;
                case 6:
                    // 已退款
                    $where['state'] = array('in','2,3,4');
                    $where['is_refund'] = 1;
                    break;
                case 7:
                    // 待还款
                    $where['state'] = 4;
                    $where['is_debt'] = 1;
                    break;
                case 8:
                    // 已还款
                    $where['state'] = 4;
                    $where['is_debt'] = 2;
                    break;
                default:
                    break;
            }
        }
        if (isset($post['isRefund']) && $post['isRefund'] && !isset($where['is_refund'])) {
            $where['is_refund'] = $post['isRefund'];
        }
        $newWhere = array();
        foreach ($where as $k=>$v){
            $newWhere['order.'.$k] = $v;
        }
        $post['consumeType'] = intval($post['consumeType']);
        if($post['consumeType']===1){
            //本店会员跨店消费
            $newWhere['vip.store_id'] = $where['store_id'];
            $newWhere['order.store_id'] = array('neq',$where['store_id']);
        }elseif ($post['consumeType']===2){
            //跨店会员本店消费
            $newWhere['vip.store_id'] = array('neq',$where['store_id']);
            $newWhere['order.store_id'] = $where['store_id'];
        }elseif ($post['consumeType']===3){
            //本店会员本店消费
            $newWhere['vip.store_id'] = $where['store_id'];
            $newWhere['order.store_id'] = $where['store_id'];
        }
        $count = $Model->getAcrossCount($newWhere);
        $page['page'] = $post['page'];
        $page['limit'] = $post['limit'];
        $page['ord'] = 'order.'.$page['ord'];
        $field = array('order.*');
        $orderList = $Model->getAcrossList($newWhere, $page, $field);
        $msg = json_encode($newWhere);
        $orderStateArr = array(
            '1'=>  '待付款', '2'=>  '待发货',
            '3'=>  '已发货', '4'=>  '已完成', '5'=>  '已取消'
        );
        foreach ($orderList as $k => &$v) {
            $v['receivable'] = number_format($v['receivable'] / 100, 2, '.', '');
            $v['order_time'] = date("Y-m-d H:i:s", $v['order_time']);
            if(isset($post['dateType']) && $post['dateType']==3){
                $v['time'] = $v['time']?date("Y-m-d H:i:s", $v['time']):'';
            }
            if (!empty($v['address_info'])) {
                $v['address_info'] = json_decode(htmlspecialchars_decode($v['address_info']));
            }
            $cashier = '--';
            if ($v['cashier_id'] != '') {
                $cashierData = $Staff->getFind(array('id'=>$v['cashier_id']));
                $cashier = ($cashierData && isset($cashierData['nickname']))?$cashierData['nickname']:$cashier;
            }
            $v['cashier'] = $cashier;
            if(isset($orderStateArr[$v['state']])){
                $v['order_state'] = $orderStateArr[$v['state']];
            }
            /* 赠送数据*/
            $presentData = array();
            if(isset($v['present_value']) && $v['present_value']>0){
                $presentData = $Model->getPresentDataAttr($v['id'],$v,1);
            }
            $v['presentData'] = $presentData;
        }
        return $this->ajaxTable($orderList, $count, $msg);
    }

    public function importAcrossOrder(){
        $get = input('get.');
        $user = session('userinfo');
        $Model = new payOrderModel;
        if ($user['storeid'] != 0) {
            $where['store_id'] = $user['storeid'];
        } else {
            if (isset($get['sid']) && $get['sid'] != 0) {
                $where['store_id'] = $get['sid'];
            }
        }
        $where['merchant_id'] = $user['merchantid'];

        $startTime = strtotime($get['start']);
        $endTime = strtotime($get['end'])+86399;

        if ($get['d_type'] == 1) {
            $where['order_time'] =   array("between", $startTime.','.$endTime);; //下单时间筛选
        } else {
            $where['collection_time'] =   array("between", $startTime.','.$endTime);; //付款时间筛选
        }
        if ($get['type'] != 0){
            $where['state'] = $get['type'];
        }
        if ($get['o_type'] != 0) {
            $where['type'] = $get['o_type'];
        }
        $newWhere = array();
        foreach ($where as $k=>$v){
            $newWhere['order.'.$k] = $v;
        }
        $get['consumeType'] = intval($get['consumeType']);
        $title = '订单明细报表';
        if($get['consumeType']===1){
            //本店会员跨店消费
            $newWhere['vip.store_id'] = $where['store_id'];
            $newWhere['order.store_id'] = array('neq',$where['store_id']);
            $title = '本店会员跨店消费订单明细报表';
        }elseif ($get['consumeType']===2){
            //跨店会员本店消费
            $newWhere['vip.store_id'] = array('neq',$where['store_id']);
            $newWhere['order.store_id'] = $where['store_id'];
            $title = '跨店会员本店消费订单明细报表';
        }elseif ($get['consumeType']===3){
            //本店会员本店消费
            $newWhere['vip.store_id'] = $where['store_id'];
            $newWhere['order.store_id'] = $where['store_id'];
            $title = '本店会员本店消费订单明细报表';
        }
        $Staff = new StaffModel;
        $field = array('order.*');
        $order = "order.id asc";
        $append = array(
            'orderInfo',
            'goodsInfo',
            'storeTag',
            'vip',
            'dispatchFee',//运费
            'logisticsName',//快递公司名称
            'payment',//
            'refundPayment',//
        );
        $sql = $Model->getAcrossOrderList($newWhere, $field, $order, $append);
        $cellName = array(
            array(
                'storeTag', '消费门店', '24',1
            ),
            array(
                'member_store', '归属门店', '24',1
            ),
            array(
                'member_name', '会员姓名', '24',1
            ),
            array(
                'member_phone', '会员手机号', '24',1
            ),
            array(
                'member_number', '会员编号', '24',1
            ),
            array(
                'member_grade', '会员等级', '24',1
            ),
            array(
                'order_number', '订单号', '24',1
            ),
            array(
                'type', '订单类型', '24',1
            ),
            array(
                'state', '订单状态', '24',1
            ),
            array(
                'source_type', '订单来源', '24',1
            ),
            array(
                'order_time', '下单日期', '24',1
            ),
            array(
                'collection_time', '收款日期', '24',1
            ),
            array(
                'time', '完成日期', '24',1
            ),
            array(
                'cashier_id', '收银员', '24',1
            ),
            array(
                'is_refund', '是否退款', '24',1
            ),
            array(
                'orderInfo.name', '商品名称', '24'
            ),
            array(
                'orderInfo.type', '商品类型', '24'
            ),
            array(
                'orderInfo.price', '单价', '24'
            ),
            array(
                'orderInfo.num', '数量', '24'
            ),
            array(
                'orderInfo.Craftsman', '理疗师', '24'
            ),
            array(
                'orderInfo.cashier', '销售', '24'
            ),
            array(
                'orderInfo.equity_type', '权益类型', '24'
            ),
            array(
                'orderInfo.reduceprice', '优惠金额', '24'//优惠金额
            ),
            array(
                'receivable', '金额', '24',1
            ),
            array(
                'wxPay', '微信支付', '24',1
            ),
            array(
                'aliPay', '支付宝支付', '24',1
            ),
            array(
                'cashPay', '现金支付', '24',1
            ),
            array(
                'xcxPay', '小程序微信支付', '24',1
            ),
            array(
                'balancePay', '会员余额支付', '24',1
            ),
            array(
                'otherPay', '自定义记账', '24',1
            ),
            array(
                'deduction', '抵扣权益优惠', '24',1
            ),
            array(
                'manually', '手动改价优惠', '24',1
            ),
            array(
                'dismoney', '折扣权益优惠', '24',1
            ),
            array(
                'is_debt', '是否欠款', '24',1
            ),
            array(
                'debt_value', '欠款金额', '24',1
            ),
            array(
                'remarks', '订单备注', '24',1
            ),
            array(
                'customer_remark', '买家留言', '24',1
            ),
            array(
                'addressName', '收货人', '24',1
            ),
            array(
                'addressTel', '收货电话', '24',1
            ),
            array(
                'addressInfo', '收货地址', '24',1
            ),
            array(
                'logisticsName', '物流公司', '24',1
            ),
            array(
                'courier_num', '物流单号', '24',1
            ),
        );
        $i = 3;
        $arr = array();
        foreach ($sql as $k1=>$v1) {
            //计算合并单元格数值
            $v1['start'] = $i;
            $i+=count($v1['orderInfo'])-1;
            $v1['end'] = $i;
            $i++;
            $v1['receivable'] = number_format($v1['receivable'] / 100, 2, '.', '');
            //订单类型: 1服务 2产品（1,2品项） 3售卡 4充值 5充卡 6 快速收款
            switch ($v1['type']){
                case 1:
                    $v1['type'] = '品项';
                    break;
                case 2:
                    $v1['type'] = '品项';
                    break;
                case 3:
                    $v1['type'] = '售卡';
                    break;
                case 4:
                    $v1['type'] = '充值';
                    break;
                case 5:
                    $v1['type'] = '充卡';
                    break;
                case 6:
                    $v1['type'] = '快速收款';
                    break;
            }
            $v1['order_time'] = date('Y-m-d H:i:s',$v1['order_time']);//下单时间
            if (isset($v1['collection_time']) && !empty($v1['collection_time'])){
                $v1['collection_time'] = date('Y-m-d H:i:s',$v1['collection_time']);//付款时间
            }else{
                $v1['collection_time'] = '--';
            }
            if (isset($v1['time']) && !empty($v1['time'])){
                $v1['time'] = date('Y-m-d H:i:s',$v1['time']);//完成时间
            }else{
                $v1['time'] = '--';
            }

            //订单状态: 1待付款  2.待发货 3.已发货 4.已完成 5.已取消
            switch ($v1['state']){
                case 1:
                    $v1['state'] = '待付款';
                    break;
                case 2:
                    $v1['state'] = '待发货';
                    break;
                case 3:
                    $v1['state'] = '已发货';
                    break;
                case 4:
                    $v1['state'] = '已完成';
                    break;
                case 5:
                    $v1['state'] = '已取消';
                    break;
            }
            if ($v1['is_refund'] == 1){//是否退款
                $v1['is_refund'] = '是';
            }else{
                $v1['is_refund'] = '否';
            }

            if ($v1['remarks']==''){  //订单备注
                $v1['remarks'] = '--';
            }
            if ($v1['customer_remark']==''){  //订单留言/顾客备注
                $v1['customer_remark'] = '--';
            }
            if ($v1['cashier_id'] != '') {
                $cashier['id'] = $v1['cashier_id'];
                $cashierData = $Staff->getFind($cashier);
                $v1['cashier_id'] = isset($cashierData['nickname']) ? $cashierData['nickname'] : "--";
            } else {
                $v1['cashier_id'] = "--";
            }
            //会员信息
            if ($v1['vip_id']!=0){
                if (isset($v1['vip']['member_name'])){
                    $v1['member_name'] = $this->removeEmoji($v1['vip']['member_name']);
                }else{
                    $v1['member_name'] = '--';
                }
                $v1['member_number'] =  isset($v1['vip']['member_number'])?$v1['vip']['member_number']:'--';
                $v1['member_phone'] =  isset($v1['vip']['phone'])?$v1['vip']['phone']:'--';
                $v1['member_grade'] =  isset($v1['vip']['Grade'])?$v1['vip']['Grade']:'--';
                $storeInfo = $this->getStoreInfo($v1['vip']['store_id']);
                if($storeInfo && isset($storeInfo['storetag'])){
                    $v1['member_store'] = $storeInfo['storetag'];
                }else{
                    $v1['member_store'] = '--';
                }
            }else{
                $v1['member_name'] = '--';
                $v1['member_number'] = '--';
                $v1['member_phone'] = '--';
                $v1['member_grade'] = '--';
                $v1['member_store'] = '--';
            }
            if (!empty($v1['address_info'])) {
                $address_info = json_decode(htmlspecialchars_decode($v1['address_info']), true);
                $v1['addressName'] = isset($address_info['name']) ? $address_info['name'] : '--';
                $v1['addressTel'] =  isset($address_info['tel']) ? $address_info['tel'] : '--';
                $v1['addressInfo'] =  isset($address_info['site']) ? $address_info['site'] : '--';
            }else{
                $v1['addressName'] = '--';
                $v1['addressTel'] = '--';
                $v1['addressInfo'] = '--';
            }
            //快递信息
            if ($v1['logisticsName'] == ''){
                $v1['logisticsName'] = '--';
            }
            if ($v1['courier_num'] == ''){
                $v1['courier_num'] = '--';
            }
            //支付方式及金额 1微信 2支付宝 3现金 4小程序微信 5会员余额 10其他   大于10----自定义记账
            $v1['wxPay'] = 0;//微信
            $v1['aliPay'] = 0;//支付宝
            $v1['cashPay'] = 0;//现金
            $v1['xcxPay'] = 0;//小程序微信
            $v1['balancePay'] = 0;//会员余额
            $v1['otherPay'] = 0;//自定义记账
            if (count($v1['payment'])>0){
                foreach ($v1['payment'] as $ka=>&$kz) {
                    switch ($kz['payType']){
                        case 1:
                            $v1['wxPay'] += $kz['trade_amount'];
                            break;
                        case 2:
                            $v1['aliPay'] += $kz['trade_amount'];
                            break;
                        case 3:
                            $v1['cashPay'] += $kz['trade_amount'];
                            break;
                        case 4:
                            $v1['xcxPay'] += $kz['trade_amount'];
                            break;
                        case 5:
                            $v1['balancePay'] += $kz['trade_amount'];
                            break;
                        default:
                            $v1['otherPay'] += $kz['trade_amount'];
                            break;
                    }
                }
            }
            // 是否欠款
            switch ($v1['is_debt']){
                case 0:
                    $v1['is_debt'] = '否';
                    break;
                case 1:
                    $v1['is_debt'] = '是';
                    break;
                case 2:
                    $v1['is_debt'] = '已还款';
                    break;
            }
            $v1['debt_value'] = number_format($v1['debt_value'] / 100, 2, '.', '');

            $item = $v1;
            unset($item['orderInfo']);
            foreach ($v1['orderInfo'] as $k2=>$v2) {
                if ($k2>0){
                    $item['orderInfo'] = 0;
                }
                switch ($v2['type']){ //1 服务 2产品 3卡项 4充值  5 直接收款
                    case 1:
                        $v2['type'] = '服务';
                        break;
                    case 2:
                        $v2['type'] = '产品';
                        break;
                    case 3:
                        $v2['type'] = '卡项';
                        break;
                    case 4:
                        $v2['type'] = '充值';
                        break;
                    case 5:
                        $v2['type'] = '直接收款';
                        break;
                }
                switch ($v2['equity_type']){ //权益类型 1 无权益 2折扣 3抵扣 4手动改价'
                    case 1:
                        $v2['equity_type'] = '无权益';
                        break;
                    case 2:
                        $v2['equity_type'] = '折扣优惠';
                        break;
                    case 3:
                        $v2['equity_type'] = '会员卡抵扣1次';
                        break;
                    case 4:
                        $v2['equity_type'] = '手动改价';
                        break;
                    default:
                        $v2['equity_type'] = '--';
                        break;
                }

                foreach ($v2 as $k3=>$v3) {
                    $item['orderInfo.'.$k3] = $v3;
                }
                $arr[] = $item;
            }
        }
        $this->exportOrderExcel($title, $cellName, $arr);
    }

    // 收入账单
    public function incomebill()
    {
        return $this->outPut();
    }
}