<div class="layui-fluid" id="MemberEquityData" v-cloak>
  <el-card
    class="box-card"
    style="min-height: 100%"
    v-loading="MemberEquityDataLoading"
  >
    <div class="flex flex-wrap items-center gap-x-4 gap-y-2 mb-4">
      <div class="flex items-center space-x-2" v-if="!isStore">
        <div>选择门店</div>
        <el-select
          v-model="storeid"
          size="small"
          placeholder="请选择"
          @change="handleStoreClick(1)"
        >
          <el-option
            v-for="item in storeList"
            :key="item.id"
            :label="item.storetag"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="flex items-center space-x-2">
        <div>选择类型</div>
        <el-select
          v-model="checkedType"
          size="small"
          placeholder="请选择"
          @change="handleStoreClick(2)"
        >
          <el-option :key="0" :label="'全部类型'" :value="0"> </el-option>
          <el-option
            v-for="item in typeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="flex items-center space-x-2">
        <div class="shrink-0">客户信息</div>
        <el-input
          placeholder="可输入客户名、备注名、手机、会员号"
          v-model="keyword"
          size="small"
          clearable
          @keyup.enter.native="handleCurrentChange(1)"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="handleCurrentChange(1)"
          ></el-button>
        </el-input>
      </div>
      <div class="flex items-center space-x-2">
        <div>选择日期</div>
        <el-button
          :type="(MemberEquityDetailType==0&&!MemberEquityDetailTime)?'primary':''"
          size="small"
          @click="checkTime(0)"
          >今天
        </el-button>
        <el-button
          :type="(MemberEquityDetailType==1&&!MemberEquityDetailTime)?'primary':''"
          size="small"
          @click="checkTime(1)"
          >昨天
        </el-button>
        <el-tooltip
          class="item"
          effect="dark"
          content="包括今天的7天"
          placement="top"
        >
          <el-button
            :type="(MemberEquityDetailType==2&&!MemberEquityDetailTime)?'primary':''"
            size="small"
            @click="checkTime(2)"
            >近7天
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="包括今天的本月"
          placement="top"
        >
          <el-button
            :type="(MemberEquityDetailType==3&&!MemberEquityDetailTime)?'primary':''"
            size="small"
            @click="checkTime(3)"
            >本月
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="开始日期的00:00:00 到 结束日期的24:00:00"
          placement="top"
        >
          <el-date-picker
            size="small"
            v-model="MemberEquityDetailTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-tooltip>
      </div>
    </div>
    <div>
      <el-table :data="MemberEquityDetailData" style="width: 100%">
        <el-table-column prop="goodsName" label="交易内容" width="250">
          <template slot-scope="scope">
            <el-row v-if="scope.row.goodsInfo" type="flex">
              <div class="shrink-0 mr-2">
                <el-image
                  :preview-src-list="[scope.row.goodsInfo.imgUrl]"
                  style="width: 50px; height: 50px"
                  :src="scope.row.goodsInfo.imgUrl"
                  fit="contain"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;
                  align-items: flex-start;
                "
              >
                <span v-text="scope.row.goodsInfo.name"></span>
                <span v-if="scope.row.goodsInfo.sku_name"
                  >（{{scope.row.goodsInfo.sku_name}}）</span
                >
              </div>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column prop="typeName" label="权益类型">
          <template slot-scope="scope">
            <el-tag
              size="mini"
              :type="scope.row.typeName == '使用权益' ? 'primary' : 'danger'"
              >{{scope.row.typeName}}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column width="125" prop="memberInfo" label="客户信息">
          <template slot-scope="scope">
            <div v-if="scope.row.memberInfo && scope.row.memberInfo.id">
              <div
                class="text-sm text-primary cursor-pointer hover:text-primary/80 w-fit"
                @click="toVip(scope.row.memberInfo.id)"
              >
                {{scope.row.memberInfo.member_name}}
              </div>
              <div class="text-xs text-gray">
                {{scope.row.memberInfo.phone}}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="cardInfo" label="权益卡项">
          <template slot-scope="scope">
            <div v-if="scope.row.cardInfo && scope.row.cardInfo.id">
              <p v-text="scope.row.cardInfo.card_name"></p>
            </div>
            <p v-else v-text="'等级权益'"></p>
          </template>
        </el-table-column>
        <el-table-column prop="promotionName" label="权益内容">
        </el-table-column>
        <el-table-column prop="promotionPrice" label="权益金额">
        </el-table-column>
        <el-table-column width="177" prop="addtime" label="时间">
        </el-table-column>
        <el-table-column align="center" width="100" label="操作">
          <template slot-scope="scope">
            <div class="flex flex-col">
              <div
                class="cursor-pointer text-primary hover:text-primary/80"
                @click="seeOrder(scope.row.order_id)"
              >
                查看订单
              </div>
              <div>
                <el-button @click="handleReport(scope.row.order_id)" size="mini"
                  >填写报告
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div
        class="block"
        style="margin-top: 20px; overflow: hidden"
        v-if="MemberEquityDetailTotal>0"
      >
        <el-pagination
          background
          style="float: right"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="MemberEquityDetailPage"
          :page-sizes="[10, 20, 30, 40,100]"
          :page-size="MemberEquityDetailLimit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="MemberEquityDetailTotal"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
  <el-dialog
    title="新建报告"
    :visible.sync="isShowReportDialog"
    width="600px"
    :append-to-body="true"
  >
    <el-form
      :model="reportForm"
      label-width="100px"
      :rules="rules"
      ref="reportFormRef"
    >
      <el-form-item label="报告内容" prop="content">
        <el-input
          type="textarea"
          v-model="reportForm.content"
          placeholder="请输入报告内容"
          :rows="10"
        ></el-input>
      </el-form-item>
    </el-form>
    <div>快速填写模板：</div>
    <div class="flex flex-wrap text-sm gap-2 mt-4">
      <div
        class="text-gray rd-lg px-2 border-1 border-solid border-gray-300 cursor-pointer"
        v-for="(item,index) in reportSummarizeList"
        :key="index"
        @click="handleReportSummarize(item)"
      >
        {{item}}
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="isShowReportDialog = false">取 消</el-button>
      <el-button type="primary" @click="handleReportConfirm">确 定</el-button>
    </span>
  </el-dialog>
</div>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    var vm;
    new Vue({
      el: "#MemberEquityData",
      data() {
        return {
          imgLoadError: "/assets/common/images/img_load_error.png",
          staffImgLoadError: "assets/img/touxoang.png",
          storeid: {$storeid}, // 当前店铺
          isStore: {$isStore}, // 店铺身份
          storeList: [], // 店铺列表
          hasGetStoreList: false, // 是否已经获取店铺列表
          activeName: "",
          checktime: "",
          keyword: "",
          isShowReportDialog: false,
          MemberEquityDataLoading: false,
          MemberEquityDetailData: [],
          MemberEquityDetailPage: 1,
          MemberEquityDetailLimit: 10,
          MemberEquityDetailTotal: 0,
          MemberEquityDetailType: 0,
          MemberEquityDetailTime: "",
          checkedType: 0,
          reportFormRef: null,
          typeList: [
            {id: 1, name: "使用权益"},
            {id: 2, name: "回退权益"},
          ],
          pickerOptions: {
            shortcuts: [
              {
                text: "最近一周",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近一个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近三个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit("pick", [start, end]);
                },
              },
            ],
          },
          reportForm: {
            content: "",
          },
          rules: {
            content: [
              {required: true, message: "请输入报告内容", trigger: "blur"},
            ],
          },
          reportSummarizeList: [
            "客户对本次服务效果满意，重点缓解了[部位]的疲劳/疼痛问题。",
            "客户反馈手法力度适中，全程配合良好，无不适反应。",
            "客户主动表示会再次预约，对服务细节表示认可。",
            "客户提出[具体需求/偏好]，已记录档案以便后续优化服务。",
            "客户对[某项技术/附加服务]特别满意，建议推广至其他项目。",
            "本次服务重点处理[部位]，采用[手法/技术]，客户肌肉紧张度明显降低。",
            "发现客户[身体特征，如劳损点/体态问题]，已提供专业建议。",
            "服务过程中客户[反应描述，如耐受度高/需调整力度]，已及时适配。",
            "本次耗时[时长]，较计划提前/延迟[时间]，原因说明：[简要说明]。",
            "需跟进事项：[如客户预约复诊、推荐居家护理动作等]。",
          ],
        };
      },
      methods: {
        toVip(id) {
          var lhash = "/Member/det/det/" + id;
          // window.open("#" + lhash);
          location.hash = lhash;
        },
        seeOrder(id) {
          // window.open("#/Order/seorder/id=" + id);
          location.hash = "/Order/seorder/id=" + id;
        },
        imgError(item, n) {
          if (n) {
            item.staffAvatar = vm.staffImgLoadError;
          } else {
            item.imgUrl = vm.imgLoadError;
          }
        },
        getStoreList() {
          vm.MemberEquityDataLoading = true;
          $.ajax({
            url: "{:url('TabStore/getStoreList')}",
            data: {},
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.storeList = res.data;
                vm.MemberEquityDataLoading = false;
                if (res.data.length > 0) {
                  vm.storeid = res.data[0]["id"];
                  vm.staffStoreid = res.data[0]["id"];
                  vm.activeName = "MemberEquityDetail";
                } else {
                  vm.$message({
                    message: "请先添加一个有效店铺",
                    type: "info",
                  });
                }
              }
            },
          });
        },
        checkTime(num) {
          vm.MemberEquityDetailType = num;
          vm.MemberEquityDetailTime = "";
          vm.MemberEquityDetailPage = 1;
          vm.getMemberEquityDetailData();
        },
        handleStoreClick(num) {
          if (num == 1) {
            vm.checkedStaffId = 0;
          }
          vm.MemberEquityDetailPage = 1;
          vm.getMemberEquityDetailData();
        },
        getMemberEquityDetailData() {
          if (vm.MemberEquityDetailTime) {
            var startTime = vm.MemberEquityDetailTime[0];
            var endTime = vm.MemberEquityDetailTime[1];
          } else {
            var startTime = 0;
            var endTime = 0;
          }
          vm.MemberEquityDataLoading = true;
          $.ajax({
            url: "{:url('getMemberEquityDetailData')}",
            data: {
              storeId: vm.storeid,
              page: vm.MemberEquityDetailPage,
              limit: vm.MemberEquityDetailLimit,
              type: vm.MemberEquityDetailType,
              startTime: startTime,
              endTime: endTime,
              checkedType: vm.checkedType,
              keyword: vm.keyword,
            },
            type: "POST",
            success(res) {
              vm.MemberEquityDetailTotal = res.count;
              vm.MemberEquityDetailData = res.data;
              vm.MemberEquityDataLoading = false;
            },
          });
        },
        handleSizeChange(val) {
          this.MemberEquityDetailPage = 1;
          this.MemberEquityDetailLimit = val;
          this.getMemberEquityDetailData();
        },
        handleCurrentChange(val) {
          this.MemberEquityDetailPage = val;
          this.getMemberEquityDetailData();
        },
        handleReport(id) {
          // TODO 未做绑定订单
          this.isShowReportDialog = true;
        },
        handleReportConfirm() {
          const vm = this;
          vm.$refs.reportFormRef.validate((valid) => {
            if (valid) {
              $.ajax({
                url: "{:url('store/staff/addWorkSummary')}",
                data: {
                  staff_id: vm.storeid,
                  content: vm.reportForm.content,
                  imgid: "",
                },
                type: "POST",
                success(res) {
                  if (res.code == 1) {
                    vm.isShowReportDialog = false;
                    vm.$message({
                      message: "报告提交成功",
                      type: "success",
                    });
                  }
                },
              });
            }
          });
        },
        handleReportSummarize(item) {
          this.reportForm.content = this.reportForm.content + item;
        },
      },
      created() {
        vm = this;
        // vm.getAllStaff();
        if (!vm.isStore) {
          vm.getStoreList();
        } else {
          vm.activeName = "MemberEquityDetail";
        }
      },
      watch: {
        isShowReportDialog(n) {
          if (!n) {
            this.reportForm.content = "";
          }
        },
        activeName(n) {
          switch (n) {
            case "MemberEquityDetail":
              vm.getMemberEquityDetailData();
              break;
          }
        },
        checktime(n) {
          console.log(n);
        },
        MemberEquityDetailTime(n) {
          if (n) {
            vm.MemberEquityDetailPage = 1;
            vm.getMemberEquityDetailData();
          }
        },
      },
    });
  });
</script>
<style>
  .tips_gray {
    width: 580px;
    line-height: 17px;
    font-size: 12px;
    margin-top: 8px;
    color: rgb(153, 153, 153);
  }
</style>

