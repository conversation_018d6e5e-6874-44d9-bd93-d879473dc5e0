<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-04-19
 * Time: 14:17
 */

namespace app\store\controller;

use app\api\controller\Xcx;
use app\store\model\ProductLabelClass as ProductLabelClassModel;
use think\Request;
use think\Validate;
use app\store\model\ProductSku as ProductSkuModel;
use app\store\model\Product as ProductModel;
use app\store\model\ProductSkuAttr as ProductSkuAttrModel;
use app\store\model\StoreProduct as StoreProductModel;
use app\store\model\StoreProductSkuAttr as StoreProductSkuAttrModel;
use app\store\model\ServiceConsumables as ServiceConsumablesModel;
use app\store\model\Storeglobal as StoreglobalModel;

use app\store\model\Productunwrap as ProductunwrapModel;
use app\store\model\Hotsearch as HotsearchModel; // 产品单位

//<!--[meiye_09_01]-->
class Product extends Controller
{
    public function index()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $storeid = $userinfo['storeid'];
        if ($storeid && $storeid != 0) {
            $this->assign('isstore', 1);
            $this->assign('productlisttab', 'second');
        } else {
            $this->assign('isstore', 0);
            $this->assign('productlisttab', 'first');
        }
        if (isset($userinfo['user_group']) && $userinfo['user_group'] == 3) {
            $this->assign('isMerchant', 1);
        } else {
            $this->assign('isMerchant', 0);
        }
        $this->assign('storeid', $storeid);
        $newProductUnit = $this->newProductUnit();
        $this->assign('unit', $newProductUnit);
        $this->outPut();
    }
    public function auto_addToStore($id)
    {
        $userinfo = $this->userinfo;
        // $id = input('post.product_id');
        $ProductModel = new ProductModel;
        $product = $ProductModel->getFind(array(
            'id' => $id,
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '1,2,6,7')
        ), '*');
        if (!$product) {
            return $this->ajaxFail('所选商品未找到，请确认');
        }
        if ($userinfo['storeid'] == 0) {
            return $this->ajaxFail('只有门店能将商品加入本店');
        }
        $addData = array(
            'product_id' => $product['id'],
            'status' => $product['status'],
            'merchant_id' => $userinfo['merchantid'],
            'store_id' => $userinfo['storeid'],
        );
        $StoreProductModel = new StoreProductModel;
        $find = $StoreProductModel->getFind($addData, 'id');
        if ($find) {
            return $this->ajaxFail('已添加');
        }
//        $addData['material_id'] = $product['img_id'];
//        $addData['price'] = $product['img_id'];
//        $addData['product_name'] = $product['product_name'];
        $addData['issku'] = $product['issku'];
        if ($product['issku'] == 1) {
            // 规格产品
            $ProductSkuAttrModel = new ProductSkuAttrModel;
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'product_id' => $product['id'],
                'status' => 1
            );
            $productSkuData = $ProductSkuAttrModel->getSkuData($where);
            $skuAddData = array();
            if ($productSkuData) {
                // 组装门店商品 规格添加数组
                foreach ($productSkuData as $k => $v) {
                    $skuitem = array(
                        'product_id' => $product['id'],
                        'merchant_id' => $userinfo['merchantid'],
                        'store_id' => $userinfo['storeid'],
                        'product_sku_attr_id' => $v['id'],
                        'store_product_id' => 0
                    );
                    $skuAddData[] = $skuitem;
                }
            }
        } else {
            $skuAddData = array();
        }
        $Add = $StoreProductModel->add($addData, $skuAddData);
        if ($Add) {
            return $this->ajaxSuccess('加入成功');
        } else {
            $msg = $StoreProductModel->error;
            return $this->ajaxFail($msg);
        }
    }
    public function addToStore()
    {
        $userinfo = $this->userinfo;
        $id = input('post.product_id');
        $ProductModel = new ProductModel;
        $product = $ProductModel->getFind(array(
            'id' => $id,
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '1,2,6,7')
        ), '*');
        if (!$product) {
            return $this->ajaxFail('所选商品未找到，请确认');
        }
        if ($userinfo['storeid'] == 0) {
            return $this->ajaxFail('只有门店能将商品加入本店');
        }
        $addData = array(
            'product_id' => $product['id'],
            'status' => $product['status'],
            'merchant_id' => $userinfo['merchantid'],
            'store_id' => $userinfo['storeid'],
        );
        $StoreProductModel = new StoreProductModel;
        $find = $StoreProductModel->getFind($addData, 'id');
        if ($find) {
            return $this->ajaxFail('已添加');
        }
//        $addData['material_id'] = $product['img_id'];
//        $addData['price'] = $product['img_id'];
//        $addData['product_name'] = $product['product_name'];
        $addData['issku'] = $product['issku'];
        if ($product['issku'] == 1) {
            // 规格产品
            $ProductSkuAttrModel = new ProductSkuAttrModel;
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'product_id' => $product['id'],
                'status' => 1
            );
            $productSkuData = $ProductSkuAttrModel->getSkuData($where);
            $skuAddData = array();
            if ($productSkuData) {
                // 组装门店商品 规格添加数组
                foreach ($productSkuData as $k => $v) {
                    $skuitem = array(
                        'product_id' => $product['id'],
                        'merchant_id' => $userinfo['merchantid'],
                        'store_id' => $userinfo['storeid'],
                        'product_sku_attr_id' => $v['id'],
                        'store_product_id' => 0
                    );
                    $skuAddData[] = $skuitem;
                }
            }
        } else {
            $skuAddData = array();
        }
        $Add = $StoreProductModel->add($addData, $skuAddData);
        if ($Add) {
            return $this->ajaxSuccess('加入成功');
        } else {
            $msg = $StoreProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /*
     * 添加到仓库
     * */
    public function addToWarehouse()
    {
        $userinfo = $this->userinfo;
        $id = input('post.product_id');
        $ProductModel = new ProductModel;
        $product = $ProductModel->getFind(array(
            'id' => $id,
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '1,2,6,7')
        ), '*');
        if (!$product) {
            return $this->ajaxFail('所选商品未找到，请确认');
        }
        if ($userinfo['storeid'] != 0) {
            return $this->ajaxFail('只有总部能将商品加入仓库');
        }
        $addData = array(
            'product_id' => $product['id'],
            'merchant_id' => $userinfo['merchantid'],
            'store_id' => -100,
            'status' => $product['status'],
        );
        $StoreProductModel = new StoreProductModel;
        $find = $StoreProductModel->getFind($addData, 'id');
        if ($find) {
            return $this->ajaxFail('已添加');
        }
        $addData['issku'] = $product['issku'];
        if ($product['issku'] == 1) {
            // 规格产品
            $ProductSkuAttrModel = new ProductSkuAttrModel;
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'product_id' => $product['id'],
                'status' => 1
            );
            $productSkuData = $ProductSkuAttrModel->getSkuData($where);
            $skuAddData = array();
            if ($productSkuData) {
                // 组装门店商品 规格添加数组
                foreach ($productSkuData as $k => $v) {
                    $skuitem = array(
                        'product_id' => $product['id'],
                        'merchant_id' => $userinfo['merchantid'],
                        'store_id' => -100,
                        'product_sku_attr_id' => $v['id'],
                        'store_product_id' => 0
                    );
                    $skuAddData[] = $skuitem;
                }
            }
        } else {
            $skuAddData = array();
        }
        $Add = $StoreProductModel->add($addData, $skuAddData);
        if ($Add) {
            return $this->ajaxSuccess('加入成功');
        } else {
            $msg = $StoreProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /*
     * 批量添加产品到门店
     * */
    public function batchAddToStore()
    {
        $userinfo = $this->userinfo;

        $batchProduct = input('post.batchProduct');
        $batchProduct = htmlspecialchars_decode($batchProduct);
        $batchProduct = json_decode($batchProduct, true);
        $ProductModel = new ProductModel;
        $StoreProductModel = new StoreProductModel;
        if ($userinfo['storeid'] == 0) {
            return $this->ajaxFail('只有门店能将商品加入本店');
        }
        $addSuccess = 0;
        foreach ($batchProduct as $key => $value) {
            $id = $value['id'];
            $product = $ProductModel->getFind(array(
                'id' => $id,
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '1,2,6,7')
            ), '*');
            if (!$product) {
                continue;
            }
            $addData = array(
                'product_id' => $product['id'],
                'merchant_id' => $userinfo['merchantid'],
                'store_id' => $userinfo['storeid'],
                'status' => $product['status'],
            );
            $find = $StoreProductModel->getFind($addData, 'id');
            if ($find) {
                continue;
            }
            $addData['issku'] = $product['issku'];
            if ($product['issku'] == 1) {
                // 规格产品
                $ProductSkuAttrModel = new ProductSkuAttrModel;
                $where = array(
                    'merchant_id' => $userinfo['merchantid'],
                    'product_id' => $product['id'],
                    'status' => 1
                );
                $productSkuData = $ProductSkuAttrModel->getSkuData($where);
                $skuAddData = array();
                if ($productSkuData) {
                    // 组装门店商品 规格添加数组
                    foreach ($productSkuData as $k => $v) {
                        $skuitem = array(
                            'product_id' => $product['id'],
                            'merchant_id' => $userinfo['merchantid'],
                            'store_id' => $userinfo['storeid'],
                            'product_sku_attr_id' => $v['id'],
                            'store_product_id' => 0
                        );
                        $skuAddData[] = $skuitem;
                    }
                }
            } else {
                $skuAddData = array();
            }
            $Add = $StoreProductModel->add($addData, $skuAddData);
            if ($Add) {
                $addSuccess++;
            }
        }
        if ($addSuccess > 0) {
            if ($addSuccess == count($batchProduct)) {
                return $this->ajaxSuccess('加入成功');
            } else {
                return $this->ajaxSuccess('加入成功' . $addSuccess . '项');
            }
        } else {
            return $this->ajaxFail('加入失败');
        }
    }

    /*
     * 批量添加产品到仓库
     * */
    public function batchAddToWarehouse()
    {
        $userinfo = $this->userinfo;
        $batchProduct = input('post.batchProduct');
        $batchProduct = htmlspecialchars_decode($batchProduct);
        $batchProduct = json_decode($batchProduct, true);
        $ProductModel = new ProductModel;
        $StoreProductModel = new StoreProductModel;
        if ($userinfo['storeid'] != 0) {
            return $this->ajaxFail('只有总部能将商品加入仓库');
        }
        $addSuccess = 0;
        foreach ($batchProduct as $key => $value) {
            $id = $value['id'];
            $product = $ProductModel->getFind(array(
                'id' => $id,
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '1,2,6,7')
            ), '*');
            if (!$product) {
                continue;
            }
            $addData = array(
                'product_id' => $product['id'],
                'merchant_id' => $userinfo['merchantid'],
                'store_id' => -100,
                'status' => $product['status'],
            );
            $find = $StoreProductModel->getFind($addData, 'id');
            if ($find) {
                continue;
            }
            $addData['issku'] = $product['issku'];
            if ($product['issku'] == 1) {
                // 规格产品
                $ProductSkuAttrModel = new ProductSkuAttrModel;
                $where = array(
                    'merchant_id' => $userinfo['merchantid'],
                    'product_id' => $product['id'],
                    'status' => 1
                );
                $productSkuData = $ProductSkuAttrModel->getSkuData($where);
                $skuAddData = array();
                if ($productSkuData) {
                    // 组装门店商品 规格添加数组
                    foreach ($productSkuData as $k => $v) {
                        $skuitem = array(
                            'product_id' => $product['id'],
                            'merchant_id' => $userinfo['merchantid'],
                            'store_id' => -100,
                            'product_sku_attr_id' => $v['id'],
                            'store_product_id' => 0
                        );
                        $skuAddData[] = $skuitem;
                    }
                }
            } else {
                $skuAddData = array();
            }
            $Add = $StoreProductModel->add($addData, $skuAddData);
            if ($Add) {
                $addSuccess++;
            }
        }
        if ($addSuccess > 0) {
            if ($addSuccess == count($batchProduct)) {
                return $this->ajaxSuccess('加入成功');
            } else {
                return $this->ajaxSuccess('加入成功' . $addSuccess . '项');
            }
        } else {
            return $this->ajaxFail('加入失败');
        }
    }


    public function addproduct()
    {
        $newProductUnit = $this->newProductUnit();
        $this->assign('unit', $newProductUnit);
        $this->outPut();
    }

    public function step($name, $step)
    {
        $newProductUnit = $this->newProductUnit();
        $this->assign('unit', $newProductUnit);
        if ($step == 'detail' || is_numeric($step)) {
            $storeid = $step == 'detail' ? 0 : $step;
            $this->assign('productId', $name);
            $this->assign('step', 'detail');
            $this->assign('storeid', $storeid);
            $this->outPut('/product/detail');
        } else if ($step == 'edit') {
            $storeid = $this->userinfo['storeid'];
            $this->assign('productId', $name);
            $this->assign('step', 'edit');
            $this->assign('storeid', $storeid);
            if ($storeid != 0) {
                $this->outPut('/product/storeedit');
            } else {
                $this->outPut('/product/edit');
            }
        } else if ($step == 'editor') {
            $storeid = $this->userinfo['storeid'];
            $this->assign('productId', $name);
            $this->assign('step', 'edit');
            $this->assign('storeid', $storeid);
            if ($storeid != 0) {
                $this->outPut('/product/storeedit');
            } else {
                $this->outPut('/product/editor');
            }
        } else if ($step == 'consumablesedit') {
            $storeid = $this->userinfo['storeid'];
            $this->assign('productId', $name);
            $this->assign('step', 'edit');
            $this->assign('storeid', $storeid);
            if ($storeid == 0) {
                $this->outPut('/product/consumablesedit');
            } else {
                $this->outPut('/Error/auth');
            }
        }
    }

    public function getProductDetail()
    {
        $userinfo = $this->userinfo;
        $productid = input('post.productId');
        $storeid = input('post.storeid');
        if ($storeid == 0) {
            /*总部数据详情*/
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '1,2'),
                'id' => $productid
            );
            $ProductModel = new ProductModel;
            $append = array(
                'imgarr',
                'videoarr',
                's_price',
                's_cost_price',
                's_freight',
                's_class',
                's_label',
                'totalnum',
                'totalsell',
                'editskuinfo',
                'videoarr2'
            );
            $data = $ProductModel->getFind($where, '*', $append);
            if ($data) {
                if ($data['detail']) {
                    $data['detail'] = htmlspecialchars_decode($data['detail']);
                } else {
                    $data['detail'] = '';
                }
                return $this->ajaxSuccess($data, '查询成功');
            } else {
                return $this->ajaxFail('未找到详情数据');
            }
        } else {
            /*门店产品数据详情*/
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '1,2')
//                'id'=>$productid
            );
            $StoreProductModel = new StoreProductModel;
            $newwhere = array();
            foreach ($where as $k => $v) {
                $newwhere['p.' . $k] = $v;
            }
            $newwhere['s.store_id'] = $storeid;
            $newwhere['s.merchant_id'] = $userinfo['merchantid'];
            $newwhere['s.status'] = array('in', '1,2');
            $newwhere['p.id'] = $productid;
            $append = array(
                'imgarr',
                's_price',
                's_class',
                's_cost_price',
                's_freight',
                's_label',
                'totalnum',
                'totalsell',
                'imgarr2',
                'videoarr2',
                'editskuinfo'
            );
            $data = $StoreProductModel->getStoreProductData($newwhere, $append);
            if ($data) {
                if ($data['detail']) {
                    $data['detail'] = htmlspecialchars_decode($data['detail']);
                } else {
                    $data['detail'] = '';
                }
                return $this->ajaxSuccess($data, '查询成功');
            } else {
                return $this->ajaxFail('未找到详情数据');
            }
        }
    }

    public function getProductData()
    {
        $userinfo = $this->userinfo;
        $productid = input('post.productID');
        $storeid = input('post.storeid');
        if ($storeid == 0) {
            $where = array(
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '1,2'),
                'id' => $productid
            );
            $ProductModel = new ProductModel;
            $append = array(
                'imgarr2',
                'videoarr2',
                'editskuinfo'
            );
            $data = $ProductModel->getFind($where, '*', $append);
            if ($data) {
                return $this->ajaxSuccess($data, '查询成功');
            } else {
                return $this->ajaxFail('未找到详情数据');
            }
        }
    }

    public function getProductList()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $user_storeid = $userinfo['storeid'];
        $limit = $post['limit'];
        $page = $post['page'];
        $storeid = $post['storeid'];
        $keyword = $post['keyword'];
        $classKeyword = $post['classKeyword'];
        $labelKeyword = $post['labelKeyword'];
        $statusKeyword = $post['statusKeyword'];
        $ProductModel = new ProductModel();
        if (isset($post['serviceBind']) && $post['serviceBind'] == 1) {
            // 服务绑定耗材
            $where = array(
                'merchant_id' => $merchantid,
               
                'issku' => 2,
                'consumable_type' => 1,
                'consumable_class' => array('in', '1,2,3')
            );
            if ($classKeyword) {
                $where['product_classid'] = $classKeyword;
            }
            if ($keyword) {
                $where['product_name'] = array('like', '%' . $keyword . '%');
            }
        } else {
            // 其他查询
            $where = array(
                'status' => array('in', '1,2'),
                'storeid'=>['in',[0,$post['storeid']]],
                'merchant_id' => $merchantid
                
            );
            if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {

                if(isset($post['consumablesType'])){
                    if($post['consumablesType']==1){
                        // 普通耗材
                        $where['status'] = array('in', '6,7');
                        if ($statusKeyword) {
                            $statusKeyword += 5;
                        }
                    }else{
                        $where['status'] = array('in', '1,2');
                        $where['consumable_class'] = array('neq', '0');
                    }
                }else{
                    $where['status'] = array('in', '6,7');
                    if ($statusKeyword) {
                        $statusKeyword += 5;
                    }
                }
            }
            if (isset($post['consumableType']) && $post['consumableType'] == 1) {
                $where['consumable_type'] = 1; // 小包装耗材
            }
            if ($classKeyword) {
                $where['product_classid'] = $classKeyword;
            }
            if ($statusKeyword) {
                $where['status'] = $statusKeyword;
            }
            if ($keyword) {
                $where['product_name'] = array('like', '%' . $keyword . '%');
            }
            if ($labelKeyword) {
                $where['product_labelids'] = array(
                    array('like', $labelKeyword . ',%'),
                    array('like', '%,' . $labelKeyword),
                    array('like', '%,' . $labelKeyword . ',%'),
                    array('eq', $labelKeyword),
                    'OR'
                );
            }
        }
        if ($storeid) {
            
            // 查看门店数据
            $StoreProductModel = new StoreProductModel(1);
            $newwhere = array();
            if (isset($where['status'])) {
                unset($where['status']);
            }
            if (isset($where['product_name'])) {
                unset($where['product_name']);
            }
            foreach ($where as $k => $v) {
                $newwhere['p.' . $k] = $v;
            }
            $newwhere['s.store_id'] = $storeid;
            $newwhere['s.merchant_id'] = $merchantid;
            if ($statusKeyword == 1) {
                $newwhere['s.status'] = 1;
                $newwhere['p.status'] = 1;
            } else if ($statusKeyword == 2) {
                $newwhere['_sql'] = ' s.status = 2 OR p.status = 2 ';
            } else if ($statusKeyword == 6) {
                $newwhere['s.status'] = 6;
                $newwhere['p.status'] = 6;
            } else if ($statusKeyword == 7) {
                $newwhere['_sql'] = ' s.status = 7 OR p.status = 7 ';
            } else {
                if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {
                    if(isset($post['consumablesType'])){
                        if($post['consumablesType']==1){
                            // 普通耗材
                            $newwhere['s.status'] = array('in', '6,7');
                            $newwhere['p.status'] = array('in', '6,7');
                        }else{
                            $newwhere['s.status'] = array('in', '1,2');
                            $newwhere['p.status'] = array('in', '1,2');
                            $where['p.consumable_class'] = array('neq', '0');
                        }
                    }else{
                        $newwhere['s.status'] = array('in', '6,7');
                        $newwhere['p.status'] = array('in', '6,7');
                    }
                } else {
                    $newwhere['s.status'] = array('in', '1,2');
                    $newwhere['p.status'] = array('in', '1,2');
                }
            }
            if ($keyword) {
                if (isset($newwhere['_sql'])) {
                    $newwhere['_sql'] = '(' . $newwhere['_sql'] . ')' . " AND (s.product_name like '%{$keyword}%' OR p.product_name like '%{$keyword}%') ";
                } else {
                    $newwhere['_sql'] = "s.product_name like '%{$keyword}%' OR p.product_name like '%{$keyword}%' ";
                }
            }
            if (isset($post['isSellOnLine']) && $post['isSellOnLine'] == 1) {
                $newwhere['p.sell_online'] = 1;
            }
            $append = array(
                'imgarr',
                's_price',
                's_class',
                's_label',
                'totalnum',
                'totalsell',
                'form'
            );
            if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                $append[] = 'freightInfo';
            }
            // 库存预警
            if (isset($post['stockWarning']) && $post['stockWarning'] == 1) {
                unset($append['s_class']);
                unset($append['s_label']);
                $append[] = 'stockWarningStatus';
                $append[] = 'stockWarningNum';
                $StoreglobalModel = new StoreglobalModel;
                $storeGlobalSetMap = array(
                    'map' => array(
                        'merchantid' => $merchantid,
                        'storeid' => $post['storeid']
                    )
                );
                $storeGlobalSet = $StoreglobalModel->getFindData($storeGlobalSetMap);
                if (is_object($storeGlobalSet)) {
                    $storeGlobalSet = $storeGlobalSet->toArray();
                }
                if (!$storeGlobalSet || $storeGlobalSet['stock_warning'] == 0) {
                    return $this->ajaxSuccess(array(), '未开启库存预警');
                }
                $extraData = array(
                    'storeGlobalSet' => $storeGlobalSet
                );
                $StoreProductModel = $StoreProductModel->setExtraData($extraData); // 设置额外的数据 使用于 获取器
                // 库存预警类型
                if (isset($post['warningType']) && $post['warningType']) {
                    $warningTypeSql = '';
                    switch ($post['warningType']) {
                        case 1:
                            $warningTypeSql = 's.stock_warning in (2,3)';
                            break;
                        case 2:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum <{$storeGlobalSet['default_warning_min_num']}) OR (s.stock_warning = 1 AND s.totalnum < s.default_warning_min_num)";
                            break;
                        case 3:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum >{$storeGlobalSet['default_warning_max_num']}) OR (s.stock_warning = 1 AND s.totalnum > s.default_warning_max_num)";
                            break;
                        case 4:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum <{$storeGlobalSet['default_warning_max_num']} AND s.totalnum >{$storeGlobalSet['default_warning_min_num']}) OR (s.stock_warning = 1 AND s.totalnum < s.default_warning_max_num AND s.totalnum > s.default_warning_min_num)";
                            break;
                        default:
                            break;
                    }
                    if (isset($newwhere['_sql'])) {
                        $newwhere['_sql'] = '(' . $newwhere['_sql'] . ')' . " AND " . $warningTypeSql;
                    } else {
                        $newwhere['_sql'] = $warningTypeSql;
                    }
                }
            }
            $order = "s.sort desc,s.addtime desc";
            if (!isset($post['skuType']) || (isset($post['skuType']) && $post['skuType'] == 0)) {
                $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append,$order);
                $count = $StoreProductModel->getCount($newwhere);
            } else if (isset($post['skuType']) && $post['skuType'] == 1) {
                $newwhere['p.issku'] = 2; // 非规格产品
                $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append,$order);
                $count = $StoreProductModel->getCount($newwhere);
            } else {
                $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
                // 库存预警
                if (isset($post['stockWarning']) && $post['stockWarning'] == 1) {
                    $StoreProductSkuAttrModel = $StoreProductSkuAttrModel->setExtraData($extraData);
                }
                $data = $StoreProductSkuAttrModel->getStoreProductSkuDataByPage($newwhere, $page, $limit, $append,$order);
                $count = $StoreProductSkuAttrModel->getStoreProductSkuCount($newwhere);
            }
          
            foreach ($data as $k => &$v) {
                if ($v['form'] == $storeid) {
                    $v['form_store'] = 1;
                } else {
                    $v['form_store'] = 0;
                }
            }
            return $this->ajaxTable($data, $count);
        } else {
           
            if (isset($post['cardType']) && $post['cardType'] == -100) {
                $user_storeid = -100;
            }
            if ($user_storeid && !$storeid) {
             
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell',
					'hasAddStoreList'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
				$extraData = array();
				if(isset($post['storeStatus']) && $post['storeStatus']==1){
					$extraData['storeStatus'] = 1;
				}
                $data = $ProductModel
					->setExtraData($extraData)
					->getListByPage($where, $page, $limit, $append);
                // 总部数据(门店查看)
                $StoreProductModel = isset($StoreProductModel) ? $StoreProductModel : new StoreProductModel;
                // 设置是否加入门店数据
                foreach ($data as $k => &$v) {
                    $find = $StoreProductModel->getFind(array('product_id' => $v['id'], 'store_id' => $user_storeid), 'id');
                    $v['isadd'] = $find ? 1 : 0;
                }
            } else {
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell',
                    'hasAddStoreList'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
				$extraData = array();
				if(isset($post['storeStatus']) && $post['storeStatus']==1){
					$extraData['storeStatus'] = 1;
				}
                $where['storeid']=$storeid;
                $data = $ProductModel
					->setExtraData($extraData)
					->getListByPage($where, $page, $limit, $append);
            }
            $count = $ProductModel->getCount($where);
            return $this->ajaxTable($data, $count, json_encode($where));
        }
    }
    /*----------------------------===========新版条件设置耗材开始===========--------------------------------*/
    /*
     * 新版条件设置耗材
     * */
    /*public function getProductList()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $user_storeid = $userinfo['storeid'];
        $limit = $post['limit'];
        $page = $post['page'];
        $storeid = $post['storeid'];
        $keyword = $post['keyword'];
        $classKeyword = $post['classKeyword'];
        $labelKeyword = $post['labelKeyword'];
        $statusKeyword = $post['statusKeyword'];
        $ProductModel = new ProductModel();
        $where = array(
            'status' => array('in', '1,2'),
            'merchant_id' => $merchantid
        );
        if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {
            $where['status'] = array('in', '6,7');
            $where['consumable_class'] = array('in', '1,2,3');   // 耗材
            $where['issku'] = 2;                                    // 非规格
            if ($statusKeyword) {
                $statusKeyword += 5;
            }
        }
        if (isset($post['consumableType']) && $post['consumableType'] == 1) {
            $where['consumable_type'] = 1; // 小包装耗材
        }
        if ($classKeyword) {
            $where['product_classid'] = $classKeyword;
        }
        if ($statusKeyword) {
            $where['status'] = $statusKeyword;
        }
        if ($keyword) {
            $where['product_name'] = array('like', '%' . $keyword . '%');
        }
        if ($labelKeyword) {
            $where['product_labelids'] = array(
                array('like', $labelKeyword . ',%'),
                array('like', '%,' . $labelKeyword),
                array('like', '%,' . $labelKeyword . ',%'),
                array('eq', $labelKeyword),
                'OR'
            );
        }
        if ($storeid) {
            // 查看门店数据
            $StoreProductModel = new StoreProductModel(1);
            $newwhere = array();
            if (isset($where['status'])) {
                unset($where['status']);
            }
            if (isset($where['product_name'])) {
                unset($where['product_name']);
            }
            foreach ($where as $k => $v) {
                $newwhere['p.' . $k] = $v;
            }
            $newwhere['s.store_id'] = $storeid;
            $newwhere['s.merchant_id'] = $merchantid;
            if ($statusKeyword == 1) {
                $newwhere['s.status'] = 1;
                $newwhere['p.status'] = 1;
            } else if ($statusKeyword == 2) {
                $newwhere['_sql'] = ' s.status = 2 OR p.status = 2 ';
            } else if ($statusKeyword == 6) {
                $newwhere['s.status'] = 6;
                $newwhere['p.status'] = 6;
            } else if ($statusKeyword == 7) {
                $newwhere['_sql'] = ' s.status = 7 OR p.status = 7 ';
            } else {
                if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {
                    $newwhere['s.status'] = array('in', '6,7');
                    $newwhere['p.status'] = array('in', '6,7');
                } else {
                    $newwhere['s.status'] = array('in', '1,2');
                    $newwhere['p.status'] = array('in', '1,2');
                }
            }
            if ($keyword) {
                if (isset($newwhere['_sql'])) {
                    $newwhere['_sql'] = '(' . $newwhere['_sql'] . ')' . " AND (s.product_name like '%{$keyword}%' OR p.product_name like '%{$keyword}%') ";
                } else {
                    $newwhere['_sql'] = "s.product_name like '%{$keyword}%' OR p.product_name like '%{$keyword}%' ";
                }
            }
            if (isset($post['isSellOnLine']) && $post['isSellOnLine'] == 1) {
                $newwhere['p.sell_online'] = 1;
            }
            $append = array(
                'imgarr',
                's_price',
                's_class',
                's_label',
                'totalnum',
                'totalsell'
            );
            if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                $append[] = 'freightInfo';
            }
            // 库存预警
            if (isset($post['stockWarning']) && $post['stockWarning'] == 1) {
                unset($append['s_class']);
                unset($append['s_label']);
                $append[] = 'stockWarningStatus';
                $append[] = 'stockWarningNum';
                $StoreglobalModel = new StoreglobalModel;
                $storeGlobalSetMap = array(
                    'map' => array(
                        'merchantid' => $merchantid,
                        'storeid' => $post['storeid']
                    )
                );
                $storeGlobalSet = $StoreglobalModel->getFindData($storeGlobalSetMap);
                if (is_object($storeGlobalSet)) {
                    $storeGlobalSet = $storeGlobalSet->toArray();
                }
                if (!$storeGlobalSet || $storeGlobalSet['stock_warning'] == 0) {
                    return $this->ajaxSuccess(array(), '未开启库存预警');
                }
                $extraData = array(
                    'storeGlobalSet' => $storeGlobalSet
                );
                $StoreProductModel = $StoreProductModel->setExtraData($extraData); // 设置额外的数据 使用于 获取器
                // 库存预警类型
                if (isset($post['warningType']) && $post['warningType']) {
                    $warningTypeSql = '';
                    switch ($post['warningType']) {
                        case 1:
                            $warningTypeSql = 's.stock_warning in (2,3)';
                            break;
                        case 2:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum <{$storeGlobalSet['default_warning_min_num']}) OR (s.stock_warning = 1 AND s.totalnum < s.default_warning_min_num)";
                            break;
                        case 3:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum >{$storeGlobalSet['default_warning_max_num']}) OR (s.stock_warning = 1 AND s.totalnum > s.default_warning_max_num)";
                            break;
                        case 4:
                            $warningTypeSql = "(s.stock_warning =0 AND s.totalnum <{$storeGlobalSet['default_warning_max_num']} AND s.totalnum >{$storeGlobalSet['default_warning_min_num']}) OR (s.stock_warning = 1 AND s.totalnum < s.default_warning_max_num AND s.totalnum > s.default_warning_min_num)";
                            break;
                        default:
                            break;
                    }
                    if (isset($newwhere['_sql'])) {
                        $newwhere['_sql'] = '(' . $newwhere['_sql'] . ')' . " AND " . $warningTypeSql;
                    } else {
                        $newwhere['_sql'] = $warningTypeSql;
                    }
                }
            }
            if (!isset($post['skuType']) || (isset($post['skuType']) && $post['skuType'] == 0)) {
                $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append);
                $count = $StoreProductModel->getCount($newwhere);
            } else if (isset($post['skuType']) && $post['skuType'] == 1) {
                $newwhere['p.issku'] = 2; // 非规格产品
                $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append);
                $count = $StoreProductModel->getCount($newwhere);
            } else {
                $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
                // 库存预警
                if (isset($post['stockWarning']) && $post['stockWarning'] == 1) {
                    $StoreProductSkuAttrModel = $StoreProductSkuAttrModel->setExtraData($extraData);
                }
                $data = $StoreProductSkuAttrModel->getStoreProductSkuDataByPage($newwhere, $page, $limit, $append);
                $count = $StoreProductSkuAttrModel->getStoreProductSkuCount($newwhere);
            }
            return $this->ajaxTable($data, $count);
        } else {
            if (isset($post['cardType']) && $post['cardType'] == -100) {
                $user_storeid = -100;
            }
            if ($user_storeid && !$storeid) {
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
                $data = $ProductModel->getListByPage($where, $page, $limit, $append);
                // 总部数据(门店查看)
                $StoreProductModel = isset($StoreProductModel) ? $StoreProductModel : new StoreProductModel;
                // 设置是否加入门店数据
                foreach ($data as $k => &$v) {
                    $find = $StoreProductModel->getFind(array('product_id' => $v['id'], 'store_id' => $user_storeid), 'id');
                    $v['isadd'] = $find ? 1 : 0;
                }
            } else {
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell',
                    'hasAddStoreList'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
                $data = $ProductModel->getListByPage($where, $page, $limit, $append);
            }
            $count = $ProductModel->getCount($where);
            return $this->ajaxTable($data, $count);
        }
    }*/

    public function newConsumable()
    {
        $userinfo = $this->userinfo;
        $where = array(
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '6,7')
        );
        $ProductModel = new ProductModel;
        $where['product_labelids'] = -1;
        $res = $ProductModel->updateProduct($where, array('consumable_class' => 1));
        $where['product_labelids'] = -2;
        $res = $ProductModel->updateProduct($where, array('consumable_class' => 2));
        $where['product_labelids'] = -3;
        $res = $ProductModel->updateProduct($where, array('consumable_class' => 3));
        unset($where['product_labelids']);
        $where['small_unit'] = '';
        $prefix = config('database.prefix');
        $sql = "UPDATE {$prefix}product   SET `small_unit` = `unit` WHERE `merchant_id` = {$userinfo['merchantid']} AND `status` in (6,7) AND `small_unit` = ''";
        $res = $ProductModel->updateField($sql);
        return $this->ajaxSuccess('重置成功');
    }

    // 将耗材移动到产品区（默认状态是下架的）；
    public function moveTosellProduct()
    {
        $userinfo = $this->userinfo;
        $id = input('post.id');
        $where = array(
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '6,7'),
            'id' => $id
        );
        $ProductModel = new ProductModel;
        $res = $ProductModel->updateProduct($where, array('status' => 2));
        if ($res) {
            $StoreProductModel = new StoreProductModel;
            $where2 = array(
                'merchant_id' => $userinfo['merchantid'],
                'status' => array('in', '6,7'),
                'product_id' => $id
            );
            $res2 = $StoreProductModel->updateProduct($where2, array('status' => 2));
            return $this->ajaxSuccess('设置成功');
        } else {
            return $this->ajaxFail('设置失败');
        }
    }

    /* 产品设置耗材类型 */
    public function submitEditToConsumable()
    {
        $userInfo = $this->userinfo;
        $id = input('post.product');
        $type = input('post.type');
        $small_content = input('post.smallContent');
        $small_unit = input('post.smallUnit');
        if (!$id) {
            return $this->ajaxFail('请选择需要设置为耗材的产品');
        }
        $ProductModel = new ProductModel;
        $where = array(
            'merchant_id' => $userInfo['merchantid'],
            'id' => $id
        );
        $updateData = array(
            'consumable_class' => $type,
            'small_content'=>$small_content,
            'small_unit'=>$small_unit,
        );
        $res = $ProductModel->updateProduct($where, $updateData);
        if ($res) {
            return $this->ajaxSuccess('设置成功');
        } else {
            return $this->ajaxFail('设置失败');
        }
    }

    /*----------------------------===========新版条件设置耗材结束===========--------------------------------*/
    public function getProductDetailData()
    {
        $userinfo = $this->userinfo;
        $productid = input('post.productID');
        $where = array(
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '1,2'),
            'id' => $productid
        );
        $ProductModel = new ProductModel;
        $data = $ProductModel->getFind($where, 'detail');

        if ($data) {
            if ($data['detail']) {
                $data['detail'] = htmlspecialchars_decode($data['detail']);
            } else {
                $data['detail'] = '';
            }
            return $this->ajaxSuccess($data, '查询成功');
        } else {
            return $this->ajaxFail('未找到详情数据');
        }
    }

    public function submitProductDetail()
    {
        $detail = input('post.detail');
        $userinfo = $this->userinfo;
        $productid = input('post.productID');
        $where = array(
            'merchant_id' => $userinfo['merchantid'],
            'status' => array('in', '1,2'),
            'id' => $productid
        );
        $ProductModel = new ProductModel;
        $data = $ProductModel->updateProduct($where, array('detail' => $detail));
        if ($data) {
            return $this->ajaxSuccess('提交成功');
        } else {
            return $this->ajaxFail('提交失败');
        }
    }

    /*
     * 获取门店产品数据 按规格铺开 一个规格算一条数据
     * */
    public function getStoreProductList()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $limit = $post['limit'];
        $page = $post['page'];
        $storeid = $post['storeid'];
        $keyword = $post['keyword'];
        $classKeyword = $post['classKeyword'];
        $isSkuProduct = $post['isSkuProduct'];
        $where = array(
            'status' => array('in', '1,2'),
            'merchant_id' => $merchantid
        );
        if ($classKeyword) {
            $where['product_classid'] = $classKeyword;
        }
        if ($keyword) {
            $where['product_name'] = array('like', '%' . $keyword . '%');
        }
//        $labelKeyword = $post['labelKeyword'];
//        if ($labelKeyword) {
//            $where['product_labelids'] = array(
//                array('like', $labelKeyword . ',%'),
//                array('like', '%,' . $labelKeyword),
//                array('like', '%,' . $labelKeyword . ',%'),
//                array('eq', $labelKeyword),
//                'OR'
//            );
//        }

        // 查看门店数据
        $newwhere = array();
        foreach ($where as $k => $v) {
            $newwhere['p.' . $k] = $v;
        }
        $newwhere['s.store_id'] = $storeid;
        $newwhere['s.merchant_id'] = $merchantid;
        $newwhere['s.status'] = array('in', '1,2');
        if ($isSkuProduct == 0) {
            $newwhere['p.issku'] = 2;
            $newwhere['s.issku'] = 2;
            $append = array(
                'imgarr',
                's_class',
                'totalnum',
                'sku_id'
            );
            if (isset($post['needShipment']) && $post['needShipment'] == 1) {
                $append[] = 'shipmentNum';
            }
            $StoreProductModel = new StoreProductModel(1);
            $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductModel->getCount($newwhere);
        } else if ($isSkuProduct == 2) {
            // 耗材
            $newwhere['s.status'] = array('in', '6,7');
            $newwhere['p.status'] = array('in', '6,7');
            $newwhere['p.issku'] = 2;
            $newwhere['s.issku'] = 2;
            $append = array(
                'imgarr',
                's_class',
                'totalnum',
                'sku_id'
            );
            if (isset($post['needShipment']) && $post['needShipment'] == 1) {
                $append[] = 'shipmentNum';
            }
            $StoreProductModel = new StoreProductModel(1);
            $data = $StoreProductModel->getStoreProductDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductModel->getCount($newwhere);
        } else {
            $newwhere['p.issku'] = 1;
//            $newwhere['s.issku'] = 2;
            $append = array(
                'imgarr',
                's_class',
                'totalnum'
            );
            if (isset($post['needShipment']) && $post['needShipment'] == 1) {
                $append[] = 'shipmentNum';
            }
            $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
            $data = $StoreProductSkuAttrModel->getStoreProductSkuDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductSkuAttrModel->getStoreProductSkuCount($newwhere);
        }
        return $this->ajaxTable($data, $count);
    }


    /*
    * 获取目标门店|仓库产品数据 按规格铺开 一个规格算一条数据
    * */
    public function getApplyStoreProductList()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $limit = $post['limit'];
        $page = $post['page'];
        $storeid = $post['storeid'];
        $keyword = $post['keyword'];
        $classKeyword = $post['classKeyword'];
        $isSkuProduct = $post['isSkuProduct'];
        $where = array(
            'status' => array('in', '1,2'),
            'merchant_id' => $merchantid
        );
        if ($classKeyword) {
            $where['product_classid'] = $classKeyword;
        }
        if ($keyword) {
            $where['product_name'] = array('like', '%' . $keyword . '%');
        }
        // 查看门店数据
        $newwhere = array();
        foreach ($where as $k => $v) {
            $newwhere['p.' . $k] = $v;
        }
        $newwhere['s.store_id'] = $storeid;
        $newwhere['s.merchant_id'] = $merchantid;
        $newwhere['s.status'] = array('in', '1,2');
        if ($isSkuProduct == 0) {
            $newwhere['p.issku'] = 2;
            $newwhere['s.issku'] = 2;
            $append = array(
                'imgarr',
                's_class',
                'totalnum',
                'sku_id',
                'warehouseGoodsInfo'
            );
            $StoreProductModel = new StoreProductModel(1);
            $extraData = array(
                'storeid' => -100,
                'merchantid' => $merchantid
            );
            $data = $StoreProductModel->setExtraData($extraData)->getStoreProductDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductModel->getCount($newwhere);
        } elseif ($isSkuProduct == 2) {
            $newwhere['s.status'] = array('in', '6,7');
            $newwhere['p.status'] = array('in', '6,7');
            $newwhere['p.issku'] = 2;
            $newwhere['s.issku'] = 2;
            $append = array(
                'imgarr',
                's_class',
                'totalnum',
                'sku_id',
                'warehouseGoodsInfo'
            );
            $StoreProductModel = new StoreProductModel(1);
            $extraData = array(
                'storeid' => -100,
                'merchantid' => $merchantid
            );
            $data = $StoreProductModel->setExtraData($extraData)->getStoreProductDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductModel->getCount($newwhere);
        } else {
            $newwhere['p.issku'] = 1;
            $append = array(
                'imgarr',
                's_class',
                'totalnum',
                'warehouseGoodsInfo'
            );
            $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
            $extraData = array(
                'storeid' => -100,
                'merchantid' => $merchantid
            );
            $data = $StoreProductSkuAttrModel->setExtraData($extraData)->getStoreProductSkuDataByPage($newwhere, $page, $limit, $append);
            $count = $StoreProductSkuAttrModel->getStoreProductSkuCount($newwhere);
        }
        return $this->ajaxTable($data, $count);
    }

    /**
     * 上传并添加产品
     */
    public function submitAddProduct()
    {
        $postData = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $video_id = 0;
        $ProductModel = new ProductModel;
        if ($postData['product_barcode']) {
            $one = $ProductModel->getBarcodeFind($postData['product_barcode'], $merchantid);
            if (!$one) {
                return $this->ajaxFail('商品条码重复');
            }
        }
        if (isset($postData['radioUrl']) && isset($postData['radioUrl'][0])) {
            $video_id = $postData['radioUrl'][0]['id'];
        }
        $imgids = '';
        if (isset($postData['imgarr']) && count($postData['imgarr']) > 0) {
            foreach ($postData['imgarr'] as $k => $v) {
                $imgids .= $v['id'] . ',';
            }
            $imgids = trim($imgids, ',');
        }
        if (!isset($postData['sell_online'])) {
            $postData['sell_online'] = 1;
        }
        if (!isset($postData['unit'])) {
            $postData['unit'] = '件';
        }
        if (!isset($postData['allproductnum'])) {
            $postData['allproductnum'] = 0;
        }
        if (!isset($postData['sellproductnum'])) {
            $postData['sellproductnum'] = 0;
        }
        if (!isset($postData['detail'])) {
            $postData['detail'] = '';
        }
        if (isset($postData['skuPriceArr']) && count($postData['skuPriceArr']) > 0) {
            $postData['cost_price'] = 0;
            $postData['price'] = 0;
        }
        $nameMap = array(
            'merchant_id' => $merchantid,
            'product_name' => $postData['product_name'],
            'status' => array('in', '1,2')
        );
        $one = $ProductModel->getFind($nameMap, 'id');
        if ($one) {
            return $this->ajaxFail('添加失败，产品名称重复');
        }
        // 组装添加数组
        $addData = array(
            'merchant_id' => $merchantid,
            'status' => 2,                    //默认为下架产品
            'product_name' => $postData['product_name'],
            'product_barcode' => $postData['product_barcode'],
            'product_classid' => $postData['product_class'],
            // 'product_labelids' => implode(',', $postData['product_labelid']),
            'video_id' => $video_id,
            'img_id' => $imgids,
            'unit' => $postData['unit'],
            'allproductnum' => $postData['allproductnum'],
            'sellproductnum' => $postData['sellproductnum'],
            'cost_price' => round($postData['cost_price'] * 100, 0),
            'price' => round($postData['price'] * 100, 0),
            'freight' => round($postData['freight'] * 100, 0),
            'sell_online' => $postData['sell_online'],
            'price_tag' => $postData['price_tag'],
            'detail' => $postData['detail']
        );
        if (isset($postData['adjust_price_id']) && $postData['adjust_price_id']) {
            $addData['adjust_price_id'] = $postData['adjust_price_id'];
        }
        // 组装添加规格数据
        if (isset($postData['skuPriceArr']) && count($postData['skuPriceArr']) > 0) {
            if (isset($postData['skuAddImg']) && $postData['skuAddImg'] == 1 && isset($postData['skuImgArr'])) {
                $skuimgarr = array();
                foreach ($postData['skuImgArr'] as $k => $v) {
                    $skuimgarr[$v['skuid']] = $v['id'];
                }
            }
            $skuAddData = array();
            $ProductSkuModel = new ProductSkuModel;
            $ProductSkuAttrModel = new ProductSkuAttrModel;
            foreach ($postData['skuPriceArr'] as $key => $value) {
                if ($value['product_barcode']) {
                    // 判断规格条码
                    $one = $ProductSkuAttrModel->getBarcodeFind($value['product_barcode'], $merchantid, $value['id']);
                    if (!$one) {
                        return $this->ajaxFail('规格商品条码重复');
                    } else {
                        // 判断条码是否已被使用
                        $one = $ProductModel->getBarcodeFind($value['product_barcode'], $merchantid, 0, 1);
                        if (!$one) {
                            return $this->ajaxFail('规格商品条码重复');
                        }
                    }
                }
                $sku_val_id = implode(',', $value['sku']);
                $sku = $ProductSkuModel->getSkuNamnesByIds($sku_val_id);
                $skuItem = array(
                    'product_id' => 0,
                    'merchant_id' => $merchantid,
                    'sku_val_id' => implode(',', $value['sku']),
                    'sku' => $sku,
                    'price' => round($value['price'] * 100, 0),
                    'cost_price' => round($value['cost_price'] * 100, 0),
                    'barcode' => $value['product_barcode'],
                    'stock_num' => 0,
                );
                if (isset($skuimgarr[$value['sku'][0]]) && $skuimgarr[$value['sku'][0]]) {
                    $skuItem['img'] = $skuimgarr[$value['sku'][0]];
                }
                $skuAddData[] = $skuItem;
            }
            $addData['issku'] = 1;
        } else {
            $skuAddData = array();
            $addData['issku'] = 2;
        }
        if ($userinfo['storeid']!=0) {
            $addData['status'] = 1;
            $addData['storeid'] = $userinfo['storeid'];
         }
        $ret = $ProductModel->add($addData, $skuAddData);
        if ($ret) {
            if ($userinfo['storeid']!=0) {
                $this->auto_addToStore($ret);
             }
            return $this->ajaxSuccess($postData, '添加成功,初始状态为下架状态');
        } else {
            $msg = $ProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /*
     *  商户修改商品数据
     * */
    public function submitEditProduct()
    {
        $postData = input('post.');
//        return $this->ajaxSuccess($postData);
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $ProductModel = new ProductModel;
        if ($postData['product_barcode']) {
            $one = $ProductModel->getBarcodeFind($postData['product_barcode'], $merchantid, $postData['id']);
            if (!$one) {
                return $this->ajaxFail('商品条码重复');
            }
        }
        $video_id = 0;
        if (isset($postData['radioUrl']) && isset($postData['radioUrl'][0])) {
            $video_id = $postData['radioUrl'][0]['id'];
        }
        $imgids = '';
        if (isset($postData['imgarr']) && count($postData['imgarr']) > 0) {
            foreach ($postData['imgarr'] as $k => $v) {
                $imgids .= $v['id'] . ',';
            }
            $imgids = trim($imgids, ',');
        }
        if (!isset($postData['sell_online'])) {
            $postData['sell_online'] = 1;
        }
        if (!isset($postData['unit'])) {
            $postData['unit'] = '件';
        }
        if (!isset($postData['allproductnum'])) {
            $postData['allproductnum'] = 0;
        }
        if (!isset($postData['sellproductnum'])) {
            $postData['sellproductnum'] = 0;
        }
        if (isset($postData['skuPriceArr']) && count($postData['skuPriceArr']) > 0) {
            $postData['cost_price'] = 0;
            $postData['price'] = 0;
        }
        $nameMap = array(
            'merchant_id' => $merchantid,
            'product_name' => $postData['product_name'],
            'status' => array('in', '1,2')
        );
        $one = $ProductModel->getFind($nameMap, 'id');
        if ($one && $one['id'] != $postData['id']) {
            return $this->ajaxFail('编辑失败，产品名称重复');
        }
        // 组装添加数组
        $editData = array(
            'id' => $postData['id'],
            'product_name' => $postData['product_name'],
            'product_barcode' => $postData['product_barcode'],
            'product_classid' => $postData['product_class'],
            'product_labelids' => implode(',', $postData['product_labelid']),
            'video_id' => $video_id,
            'img_id' => $imgids,
            'unit' => $postData['unit'],
            'cost_price' => round($postData['cost_price'] * 100, 0),
            'price' => round($postData['price'] * 100, 0),
            'price_tag' => $postData['price_tag'],
            'freight' => round($postData['freight'] * 100, 0),
            'sell_online' => $postData['sell_online'],
        );
        if (isset($postData['adjust_price_id'])) {
            $editData['adjust_price_id'] = $postData['adjust_price_id'];
        }
        // 组装添加规格数据
        if (isset($postData['skuPriceArr']) && count($postData['skuPriceArr']) > 0) {
            if (isset($postData['skuAddImg']) && $postData['skuAddImg'] == 1 && isset($postData['skuImgArr'])) {
                $skuimgarr = array();
                foreach ($postData['skuImgArr'] as $k => $v) {
                    $skuimgarr[$v['skuid']] = intval($v['id']);
                }
            }
            /*`id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `product_id` int(11) DEFAULT NULL COMMENT '服务id',
              `merchant_id` int(11) DEFAULT NULL COMMENT '商家id',
              `sku_val_id` varchar(255) DEFAULT NULL COMMENT '属性值id(多属性值用英文逗号分隔)',
              `sku` varchar(255) DEFAULT NULL COMMENT 'sku属性值',
              `price` int(11) DEFAULT NULL COMMENT '价格以分为单位',
              `cost_price` int(11) DEFAULT NULL COMMENT '价格以分为单位',
              `barcode` varchar(255) DEFAULT NULL COMMENT '条码',
              `stock_num` int(11) DEFAULT NULL COMMENT '库存',
              `addtime` varchar(20) DEFAULT NULL COMMENT '添加时间',
              `status` varchar(1) DEFAULT '1' COMMENT '状态 1 正常 2删除',*/
            $skueditData = array();
            $ProductSkuAttrModel = new ProductSkuAttrModel;
            foreach ($postData['skuPriceArr'] as $key => $value) {
                if ($value['product_barcode']) {
                    // 判断规格条码
                    $one = $ProductSkuAttrModel->getBarcodeFind($value['product_barcode'], $merchantid, $value['id']);
                    if (!$one) {
                        return $this->ajaxFail('规格商品条码重复');
                    } else {
                        // 判断条码是否已被使用
                        $one = $ProductModel->getBarcodeFind($value['product_barcode'], $merchantid, 0, 1);
                        if (!$one) {
                            return $this->ajaxFail('规格商品条码重复');
                        }
                    }
                }
                $skuItem = array(
                    'id' => $value['id'],
                    'price' => round($value['price'] * 100, 0),
                    'cost_price' => round($value['cost_price'] * 100, 0),
                    'barcode' => $value['product_barcode'],
                );
                if (isset($skuimgarr[$value['skuid']]) && $skuimgarr[$value['skuid']]) {
                    $skuItem['img'] = $skuimgarr[$value['skuid']];
                }
                $skueditData[] = $skuItem;
            }
        } else {
            $skueditData = array();
        }
        $ret = $ProductModel->edit($editData, $skueditData);
        if ($ret) {
//            $postData['$skuimgarr'] = $skuimgarr;
//            $postData['$skueditData'] = $skueditData;
            return $this->ajaxSuccess($postData, '编辑成功');
        } else {
            $msg = $ProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    public function submitEditStoreProduct()
    {
        $postData = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $StoreProductModel = new StoreProductModel;
        $imgids = '';
        if (isset($postData['imgarr']) && count($postData['imgarr']) > 0) {
            foreach ($postData['imgarr'] as $k => $v) {
                $imgids .= $v['id'] . ',';
            }
            $imgids = trim($imgids, ',');
        }
        // 组装添加数组
        if (isset($postData['skuPriceArr']) && (count($postData['skuPriceArr']) > 0)) {
            $editData = array(
                'id' => $postData['store_product_id'],
                'product_name' => $postData['product_name'],
                'material_id' => $imgids,
                'isset' => 1,
                'istotalsell' => $postData['istotalsell'],//是否展示销量
            );
        } else {
            $editData = array(
                'id' => $postData['store_product_id'],
                'product_name' => $postData['product_name'],
                'material_id' => $imgids,
                'cost_price' => round($postData['cost_price'] * 100, 0),
                'price' => round($postData['price'] * 100, 0),
                'member_price' => round($postData['member_price'] * 100, 0),
                'isset' => 1,
                'istotalsell' => $postData['istotalsell'],//是否展示销量
            );
        }
        // 组装添加规格数据
        if (isset($postData['skuPriceArr']) && count($postData['skuPriceArr']) > 0) {
            $skueditData = array();
            foreach ($postData['skuPriceArr'] as $key => $value) {
                $skuItem = array(
                    'id' => $value['id'],
                    'price' => round($value['price'] * 100, 0),
                    'cost_price' => round($value['cost_price'] * 100, 0),
                    'isset' => 1
                );
                $skueditData[] = $skuItem;
            }
        } else {
            $skueditData = array();
        }
//        dump($postData['istotalsell']);
//        dump($skueditData);die;
        $ret = $StoreProductModel->edit($editData, $skueditData);
        if ($ret) {
//            $postData['$editData'] = $editData;
//            $postData['$skueditData'] = $skueditData;
            return $this->ajaxSuccess($postData, '编辑成功');
        } else {
            $msg = $StoreProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /**
     * 上下架和删除操作
     * @return array
     */
    public function putawayAndSoldOut()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $storeid = $userinfo['storeid'];
        $id = input('post.id');
        $type = input('post.type');
        $postStoreId = input('post.storeid');
        $where = array(
            'id' => $id,
            'merchant_id' => $merchantid
        );
        if ($type > 0) {
            $update = array(
                'status' => $type
            );
        } else if ($type == -1) {
            $del = input('post.del');
            if ($del == 1) {
                $update = array(
                    'status' => 3
                );
            } else if ($del == 2) {
                $isConsumable = input('isConsumable');
                if ($isConsumable == 1) {
                    $ServiceConsumablesModel = new ServiceConsumablesModel;
                    $map = array(
                        'consumables_id' => $id,
                        'merchantid' => $merchantid
                    );
                    $one = $ServiceConsumablesModel->getRealFind($map);
                    if ($one && is_object($one)) {
                        $one = $one->toArray();
                    }
                    if ($one && count($one) > 0) {
                        return $this->ajaxFail('有服务项目关联该耗材，不能删除！');
                    }
                }
                $update = array(
                    'status' => 8
                );
            }
        }
        if ($storeid == 0 && !$postStoreId) {
            $ProductModel = new ProductModel;
            $data = $ProductModel->updateProduct($where, $update);
            if ($data) {
                return $this->ajaxSuccess('操作成功');
            } else {
                $msg = $ProductModel->error;
                return $this->ajaxFail($msg);
            }
        } else {
            $editData = $where;
            $editData['status'] = $update['status'];
            if ($postStoreId) {
                $editData['store_id'] = $postStoreId;
            } else {
                $editData['store_id'] = $storeid;
            }
            $StoreProductModel = new StoreProductModel;
            $one = $StoreProductModel->getFind($where);
            if ($one['issku'] == 2) {
                $data = $StoreProductModel->edit($editData);
//                $sql = $StoreProductModel->getLastSql();
            } else {
                $map = $where;
                $map['store_product_id'] = $id;
                $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
                $skueditData = $StoreProductSkuAttrModel->getList($map, 'id,merchant_id,store_id,store_product_id', '1');
                $data = $StoreProductModel->edit($editData, $skueditData);
//                $sql = $StoreProductModel->getLastSql();
            }
            if ($data) {
                return $this->ajaxSuccess('操作成功');
            } else {
                $msg = $StoreProductModel->error;
                return $this->ajaxFail($msg);
            }
        }
    }

    /**
     * 获取全部的标签和分类。
     * @return array
     */
    public function getLabelAndClassData()
    {
        $data = array(
            'labelData' => $this->getLabelClassData(2),
            'classData' => $this->getLabelClassData(1),
        );
        return $this->ajaxSuccess($data);
    }

    public function getLabelClassData($type = 1)
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $where = array(
            'merchantid' => $merchantid
        );
        $ProductLabelClassModel = new ProductLabelClassModel;
        $d = $ProductLabelClassModel->getLabelClassData($where, $type);
        $d = $d ? $d : array();
        return $d;
    }

    /**
     * 添加标签分类
     * @return array
     */
    public function addLabelClass()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $post = input('post.');
        $addData = array(
            'name' => $post['name'],
            'type' => $post['type'],
            'merchantid' => $merchantid
        );
        $ProductLabelClassModel = new ProductLabelClassModel;
        $d = $ProductLabelClassModel->addLabelClass($addData);
        if ($d) {
            return $this->ajaxSuccess();
        } else {
            $msg = $ProductLabelClassModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /**
     * 获取分类标签 分类
     * @return array
     */
    public function getLabelClassDataByPage()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $type = input('post.type');
        $page = input('post.page');
        $limit = input('post.limit');
        $where = array(
            'merchantid' => $merchantid,
            'type' => $type,
            'status' => 1
        );
        $ProductLabelClassModel = new ProductLabelClassModel;
        $d = $ProductLabelClassModel->getLabelClassDataByPage($where, $page, $limit, $type);
        $count = $ProductLabelClassModel->getLabelClassCount($where);
        return $this->ajaxTable($d, $count);
    }

    /**
     * 编辑标签名称
     * @return array
     */
    public function editLabelClass()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $post = input('post.');
        $where = array(
            'id' => $post['id'],
            'merchantid' => $merchantid
        );
        $addData = array(
            'name' => $post['name']
        );
        $ProductLabelClassModel = new ProductLabelClassModel;
        $d = $ProductLabelClassModel->editLabelClass($where, $addData);
        if ($d) {
            return $this->ajaxSuccess();
        } else {
            $msg = $ProductLabelClassModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /**
     * 删除标签分类
     * @return array
     */
    public function delLabelClass()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $post = input('post.');
        $where = array(
            'id' => $post['id'],
            'merchantid' => $merchantid
        );
        $addData = array(
            'status' => 2
        );
        $ProductModel = new ProductModel;
        $map = array(
            'merchant_id' => $merchantid,
            'product_classid|product_labelids' => array(
                array('like', $post['id'] . ',%'),
                array('like', '%,' . $post['id']),
                array('like', '%,' . $post['id'] . ',%'),
                array('eq', $post['id']),
                'OR'
            ),
            'status' => array('in', '1,2,6,7')
        );
        $one = $ProductModel->getFind($map, 'id');
        if ($one) {
            $msg = '还有商品在使用该数据，不能删除';
            return $this->ajaxFail($msg);
        }
        $ProductLabelClassModel = new ProductLabelClassModel;
        $d = $ProductLabelClassModel->editLabelClass($where, $addData);
        if ($d) {
            return $this->ajaxSuccess();
        } else {
            $msg = $ProductLabelClassModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /**
     * 获取商户所有规格。
     */
    public function getSkuItemArr()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $where = array(
            'merchantid' => $merchantid,
            'pid' => 0
        );
        $ProductSkuModel = new ProductSkuModel;
        $data = $ProductSkuModel->getSkuItemArr($where);
        return $this->ajaxSuccess($data, '查询成功');
    }

    public function addProductSkuItem()
    {
        $rule = [
            'name' => 'require|max:10',
            'pid' => 'require'
        ];

        $msg = [
            'name.require' => '名称不能为空',
            'name.max' => '名称最多不能超过10个字符',
            'pid.require' => '上级id必填'
        ];
        $validate = Validate::make($rule, $msg);
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $post = input('post.');
        $data = array(
            'merchantid' => $merchantid,
            'pid' => $post['pid'],
            'name' => $post['name']
        );
        $result = $validate->batch()->check($data);
        if (true !== $result) {
            $error = $validate->getError();
            return $this->ajaxFail($error);
        } else {
            $ProductSkuModel = new ProductSkuModel;
            $res = $ProductSkuModel->add($data);
            if ($res) {
                return $this->ajaxSuccess('添加成功');
            } else {
                $msg = $ProductSkuModel->error;
                return $this->ajaxFail($msg);
            }
        }
    }


    /*耗材*/
    public function consumables()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $storeid = $userinfo['storeid'];
        if ($storeid && $storeid != 0) {
            $this->assign('isstore', 1);
            $this->assign('productlisttab', 'second');
        } else {
            $this->assign('isstore', 0);
            $this->assign('productlisttab', 'first');
        }
        $this->assign('storeid', $storeid);
        $this->outPut();
    }

    /*
     * 添加耗材submitAddConsumables
     * */
    public function addconsumables()
    {
        $newProductUnit = $this->newProductUnit();
        $this->assign('unit', $newProductUnit);
        $this->outPut();
    }

    /**
     * 上传并耗材
     */
    public function submitAddConsumables()
    {
        $postData = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $ProductModel = new ProductModel;
        if ($postData['product_barcode']) {
            $one = $ProductModel->getBarcodeFind($postData['product_barcode'], $merchantid);
            if (!$one) {
                return $this->ajaxFail('商品条码重复');
            }
        }
        $imgids = '';
        if (isset($postData['imgarr']) && count($postData['imgarr']) > 0) {
            foreach ($postData['imgarr'] as $k => $v) {
                $imgids .= $v['id'] . ',';
            }
            $imgids = trim($imgids, ',');
        }
        if (!isset($postData['unit'])) {
            $postData['unit'] = '件';
        }
        if (!isset($postData['allproductnum'])) {
            $postData['allproductnum'] = 0;
        }
        if (!isset($postData['sellproductnum'])) {
            $postData['sellproductnum'] = 0;
        }
        if (!isset($postData['detail'])) {
            $postData['detail'] = '';
        }
        $nameMap = array(
            'merchant_id' => $merchantid,
            'product_name' => $postData['product_name'],
            'status' => array('in', '1,2,6,7')
        );
        $one = $ProductModel->getFind($nameMap, 'id');
        if ($one) {
            return $this->ajaxFail('添加失败，产品名称重复');
        }
        // 组装添加数组
        $addData = array(
            'merchant_id' => $merchantid,
            'status' => 6,                    //默认为上架耗材
            'product_name' => $postData['product_name'],
            'product_barcode' => $postData['product_barcode'],
            'product_classid' => $postData['product_class'],
            'consumable_class' => $postData['consumable_class'],
            'img_id' => $imgids,
            'unit' => $postData['unit'],
            'cost_price' => round($postData['cost_price'] * 100, 0),
            'issku' => 2
        );
        if (isset($postData['consumable_type'])) {
            $addData['consumable_type'] = $postData['consumable_type'];
        }
        if (isset($postData['small_content'])) {
            $addData['small_content'] = $postData['small_content'];
        }
        if (isset($postData['small_unit'])) {
            $addData['small_unit'] = $postData['small_unit'];
        }
        $skuAddData = array();
        $ret = $ProductModel->add($addData, $skuAddData);
        if ($ret) {
            return $this->ajaxSuccess($postData, '添加成功');
        } else {
            $msg = $ProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /**
     * 上传并保存耗材
     */
    public function submitUpdateConsumables()
    {
        $postData = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $ProductModel = new ProductModel;
        $id = $postData['id'];
        if ($postData['product_barcode']) {
            $one = $ProductModel->getBarcodeFind($postData['product_barcode'], $merchantid, $id);
            if (!$one) {
                return $this->ajaxFail('商品条码重复');
            }
        }
        $imgids = '';
        if (isset($postData['imgarr']) && count($postData['imgarr']) > 0) {
            foreach ($postData['imgarr'] as $k => $v) {
                $imgids .= $v['id'] . ',';
            }
            $imgids = trim($imgids, ',');
        }
        if (!isset($postData['unit'])) {
            $postData['unit'] = '件';
        }
        if (!isset($postData['allproductnum'])) {
            $postData['allproductnum'] = 0;
        }
        if (!isset($postData['sellproductnum'])) {
            $postData['sellproductnum'] = 0;
        }
        if (!isset($postData['detail'])) {
            $postData['detail'] = '';
        }
        $nameMap = array(
            'merchant_id' => $merchantid,
            'product_name' => $postData['product_name'],
            'status' => array('in', '1,2,6,7')
        );
        $one = $ProductModel->getFind($nameMap, 'id');
        if ($one && $one['id'] != $id) {
            return $this->ajaxFail('编辑失败，产品名称重复');
        }
        // 组装添加数组
        $addData = array(
            'id' => $id,
            'merchant_id' => $merchantid,
            'product_name' => $postData['product_name'],
            'product_barcode' => $postData['product_barcode'],
            'product_classid' => $postData['product_class'],
            'consumable_class' => $postData['consumable_class'],
            'img_id' => $imgids,
            'unit' => $postData['unit'],
            'cost_price' => round($postData['cost_price'] * 100, 0),
            'issku' => 2
        );
        if (isset($postData['small_content'])) {
            $addData['small_content'] = $postData['small_content'];
        }
        if (isset($postData['small_unit'])) {
            $addData['small_unit'] = $postData['small_unit'];
        }
        $skuAddData = array();
        $ret = $ProductModel->edit($addData, $skuAddData);
        if ($ret) {
            return $this->ajaxSuccess($postData, '保存成功');
        } else {
            $msg = $ProductModel->error;
            return $this->ajaxFail($msg);
        }
    }

    /*
     * 获取数据
     * */
    public function getConsumablesData()
    {
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $storeid = $userinfo['storeid'];
        $id = input('post.id');
        if ($storeid && $storeid != 0) {
            return $this->ajaxFail('数据获取失败');
        } else {
            $ProductModel = new ProductModel;
            $map = array(
                'merchant_id' => $merchantid,
                'status' => array('in', '6,7'),
                'id' => $id
            );
            $data = $ProductModel->getFind($map, '*', array('imgarr2'));
            if ($data) {
                return $this->ajaxSuccess($data);
            } else {
                return $this->ajaxFail('数据获取失败');
            }
        }
    }

    public function saveStockWarning()
    {
        $post = input('post.');
        $id = $post['id'];
        $sku_id = $post['sku_id'];
        $max = $post['maxNum'];
        $min = $post['minNum'];
        $status = $post['status'];
        if ($sku_id == 0) {
            $StoreProductModel = new StoreProductModel;
            $data = array(
                'id' => $id,
                'default_warning_min_num' => $min,
                'default_warning_max_num' => $max,
                'stock_warning' => $status
            );
            if ($status == 2 || $status == 3) {
                unset($data['default_warning_min_num']);
                unset($data['default_warning_max_num']);
            }
            $res = $StoreProductModel->editData($data);
        } else {
            $StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
            $data = array(
                'id' => $sku_id,
                'default_warning_min_num' => $min,
                'default_warning_max_num' => $max,
                'stock_warning' => $status
            );
            if ($status == 2 || $status == 3) {
                unset($data['default_warning_min_num']);
                unset($data['default_warning_max_num']);
            }
            $res = $StoreProductSkuAttrModel->editData($data);
        }
        if ($res !== false) {
            return $this->ajaxSuccess($post);
        } else {
            return $this->ajaxFail('设置失败');
        }
    }


    //生成产品推广二维码
    public function getProductCode()
    {
        $post = input('post.');
        $user = session("userinfo");
        $productid = $post['id'];
        $merchant_id = $user['merchantid'];
        $storeid = $post['storeid'];
        $page = 'pages/shoopProduct/shoopProduct';
        $xcxData["path"] = $page . '?productid=' . $productid . '&merchantid=' . $merchant_id . '&storeid=' . $storeid; //绑定参数，字符串依次：产品id，商户id，门店id
        $xcxData["width"] = 280;//二维码宽度
		Request::instance()->post(array(
			'storeid' => $storeid,
			'merchantid' => $merchant_id,
			'path' => $xcxData["path"],
			'width' => $xcxData["width"],
		));
		$xcx = new Xcx();
		$res = $xcx->setVerify(md5($merchant_id))->getXcxQrCode();
		return $res;
//		$access_token = get_authorizer_access_tokens($merchant_id);
//        $xcxObj = new \xcxClass\xcxClass();
//        $data = $xcxObj->getAppCode($xcxData, $access_token);
//        if ($data) {
//            $img = 'data:image/jpeg' . ';base64,' . $data;//合成图片的base64编码
//            return $this->ajaxSuccess($img, 'ok');
//        } else {
//            return $this->ajaxFail('获取失败');
//        }
    }

    // 获取耗材的拆包设置
    public function getUnwrapSetTableData()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $user_storeid = $userinfo['storeid'];
        $storeid = $post['storeid'];
        $ProductModel = new ProductModel();
        $where = array(
            'status' => array('in', '1,2'),
            'merchant_id' => $merchantid
        );
        if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {
            $where['status'] = array('in', '6,7');
        }
        if (isset($post['cid']) && $post['cid']) {
            $where['cid'] = $post['cid'];
        }
        if ($storeid) {
            // 查看门店数据
            $StoreProductModel = new StoreProductModel(1);
            $newwhere = array();
            if (isset($where['status'])) {
                unset($where['status']);
            }
            if (isset($where['product_name'])) {
                unset($where['product_name']);
            }
            foreach ($where as $k => $v) {
                $newwhere['p.' . $k] = $v;
            }
            $newwhere['s.store_id'] = $storeid;
            $newwhere['s.merchant_id'] = $merchantid;
            if (isset($post['isConsumables']) && $post['isConsumables'] == 1) {
                $newwhere['s.status'] = array('in', '6,7');
                $newwhere['p.status'] = array('in', '6,7');
            } else {
                $newwhere['s.status'] = array('in', '1,2');
                $newwhere['p.status'] = array('in', '1,2');
            }
            $append = array(
                'imgarr',
                's_price',
                's_class',
                's_label',
                'totalnum',
                'totalsell'
            );
            $data = $StoreProductModel->getStoreProductDataAll($newwhere, $append);
            $count = 0;
            return $this->ajaxTable($data, $count);
        } else {
            if (isset($post['cardType']) && $post['cardType'] == -100) {
                $user_storeid = -100;
            }
            if ($user_storeid && !$storeid) {
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
                $data = $ProductModel->getSelect($where, '*', $append);
                // 总部数据(门店查看)
                $StoreProductModel = isset($StoreProductModel) ? $StoreProductModel : new StoreProductModel;
                // 设置是否加入门店数据
                foreach ($data as $k => &$v) {
                    $find = $StoreProductModel->getFind(array('product_id' => $v['id'], 'store_id' => $user_storeid), 'id');
                    $v['isadd'] = $find ? 1 : 0;
                }
            } else {
                $append = array(
                    'imgarr',
                    's_price',
                    's_class',
                    's_label',
                    'totalnum',
                    'totalsell',
                    'hasAddStoreList'
                );
                if (isset($post['freightInfo']) && $post['freightInfo'] == 1) {
                    $append[] = 'freightInfo';
                }
                $data = $ProductModel->getSelect($where, '*', $append);
            }
            $count = 0;
            return $this->ajaxTable($data, $count);
        }
    }

    // 拆包设置
    public function unwrap()
    {
        $storeId = $this->userinfo['storeid'];
        if ($storeId) {
            $this->assign('isStore', 1);
        } else {
            $this->assign('isStore', 0);
        }
        $this->outPut();
    }

    public function getConsumableData()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $ProductModel = new ProductModel();
        $where = array(
            'status' => array('in', '6,7'),
            'merchant_id' => $merchantid
        );
        $where['consumable_type'] = $post['consumableType'];
        $append = array(
            'imgarr'
        );
        $page = $post['page'];
        $limit = $post['limit'];
        if ($post['consumableType'] == 2) {
            // 大包装
            $ProductunwrapModel = new ProductunwrapModel;
            $map = array(
                'merchantid' => $this->userinfo['merchantid'],
            );
            $dataMap = array(
                'map' => $map,
                'field' => array('big_package_id as id')
            );
            $idsArr = $ProductunwrapModel->getAllData($dataMap);
            if (count($idsArr) > 0) {
                $newIdsArr = array_column($idsArr, 'id');
                $where['id'] = array('not in', $newIdsArr);
            }
        }
        //pre($where);
        $data = $ProductModel->getListByPage($where, $page, $limit, $append);
        $count = $ProductModel->getCount($where);
        return $this->ajaxTable($data, $count);
    }

    public function addUnwrap()
    {
        $ProductunwrapModel = new ProductunwrapModel;
        $post = input('post.');
        $post = json_decode(json_encode($post), true);
        $id = $post['id'];
        $big_package_id = $post['bigProduct']['id'];
        $small_package_id = $post['smallProduct']['id'];
        $num = $post['num'];
        $data = array(
            'merchantid' => $this->userinfo['merchantid'],
            'big_package_id' => $big_package_id,
            'small_package_id' => $small_package_id,
            'num' => $num
        );
        if ($id) {
            $data['id'] = $id;
        } else {
            $data['addtime'] = time();
        }
        if (isset($post['sort'])) {
            $data['sort'] = $post['sort'];
        }
        $res = $ProductunwrapModel->editData($data);
        if ($res !== false) {
            return $this->ajaxSuccess('保存成功');
        } else {
            return $this->ajaxFail('保存失败');
        }
    }

    public function delUnwrap()
    {
        $ProductunwrapModel = new ProductunwrapModel;
        $post = input('post.');
        $id = $post['id'];
        $data = array(
            'map' => array(
                'id' => $id,
                'merchantid' => $this->userinfo['merchantid']
            )
        );
        $res = $ProductunwrapModel->delData($data);
        if ($res) {
            return $this->ajaxSuccess('删除成功');
        } else {
            return $this->ajaxFail('删除失败');
        }
    }

    public function getProductUnwrapData()
    {
        $post = input('post.');
        $userinfo = $this->userinfo;
        $merchantid = $userinfo['merchantid'];
        $ProductunwrapModel = new ProductunwrapModel;
        $where = array(
            'merchantid' => $merchantid
        );
        $page = $post['page'];
        $limit = $post['limit'];
        $field = array(
            '*',
            'big_package_id as bigProduct',
            'small_package_id as smallProduct',
        );
        $data = $ProductunwrapModel->getListByPage($where, $page, $limit, $field);
        foreach ($data as $k => &$v) {
            $v['addtime'] = date('Y-m-d H:i:s', $v['addtime']);
        }
        $count = $ProductunwrapModel->getCount($where);
        return $this->ajaxTable($data, $count);
    }

    /*批量操作*/
    public function hQbatchSet()
    {
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $ProductModel = new ProductModel;
        // 上架
        if ($post['type'] == 1) {
            $map = array(
                'merchant_id' => $userInfo['merchantid'],
                'id' => array('in', $ids)
            );
            $res = $ProductModel->updateProduct($map, array('status' => 1));
            if ($res !== false) {
                return $this->ajaxSuccess('上架成功');
            } else {
                return $this->ajaxSuccess('上架失败');
            }
        }
        // 下架
        if ($post['type'] == 2) {
            $map = array(
                'merchant_id' => $userInfo['merchantid'],
                'id' => array('in', $ids)
            );
            $res = $ProductModel->updateProduct($map, array('status' => 2));
            if ($res !== false) {
                return $this->ajaxSuccess('下架成功');
            } else {
                return $this->ajaxFail('下架失败');
            }
        }
        // 删除
        if ($post['type'] == 3) {
            $map = array(
                'merchant_id' => $userInfo['merchantid'],
                'id' => array('in', $ids)
            );
            $res = $ProductModel->updateProduct($map, array('status' => 3));
            if ($res !== false) {
                return $this->ajaxSuccess('删除成功');
            } else {
                return $this->ajaxFail('删除失败');
            }
        }
        return $this->ajaxFail('错误的操作');
    }

    /*批量操作*/
    public function sTbatchSet()
    {
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $StoreProductModel = new StoreProductModel;
        // 上架
        if ($post['type'] == 1) {
            $map = array(
                'merchant_id' => $userInfo['merchantid'],
                'id' => array('in', $ids),
            );
            $res = $StoreProductModel->updateProduct($map, array('status' => 1));
            if ($res !== false) {
                return $this->ajaxSuccess('上架成功');
            } else {
                return $this->ajaxFail('上架失败');
            }
        }
        // 下架
        if ($post['type'] == 2) {
            $map = array(
                'merchant_id' => $userInfo['merchantid'],
                'id' => array('in', $ids)
            );
            $res = $StoreProductModel->updateProduct($map, array('status' => 2));
            if ($res !== false) {
                return $this->ajaxSuccess('下架成功');
            } else {
                return $this->ajaxFail('下架失败');
            }
        }
        return $this->ajaxFail('错误的操作');
    }

    public function batchAdjustPrice()
    {
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $adjust_price_id = $post['val'];
        $ProductModel = new ProductModel;
        $map = array(
            'merchant_id' => $userInfo['merchantid'],
            'id' => array('in', $ids)
        );
        $res = $ProductModel->updateProduct($map, array('adjust_price_id' => $adjust_price_id));
        if ($res !== false) {
            return $this->ajaxSuccess('调价范围设置成功');
        } else {
            return $this->ajaxSuccess('调价范围设置失败');
        }
    }

    public function batchFreight()
    {
        $post = input('post.');
        $ids = $post['data'];
        $userInfo = $this->userinfo;
        $batchFreight = $post['val'];
        $ProductModel = new ProductModel;
        $map = array(
            'merchant_id' => $userInfo['merchantid'],
            'id' => array('in', $ids)
        );
        if ($batchFreight['type'] == 0) {
            $updateData = array(
                'freight' => round($batchFreight['freight'] * 100, 0),
                'freight_id' => 0
            );
        } else {
            $updateData = array(
                'freight_id' => $batchFreight['freightInfo']['id']
            );
        }
        $res = $ProductModel->updateProduct($map, $updateData);
        if ($res !== false) {
            return $this->ajaxSuccess('运费设置成功');
        } else {
            return $this->ajaxSuccess('运费设置失败');
        }
    }

    public function newProductUnit()
    {
        $HotsearchModel = new HotsearchModel;
        $userInfo = $this->userinfo;
        $dataMap = array(
            'map' => array(
                'merchantid' => $userInfo['merchantid'],
                'storeid' => 0
            )
        );
        $data = $HotsearchModel->getFindData($dataMap);
        if ($data && count($data) > 0) {
            $unitArr = array();
            foreach ($data['hot_keywords'] as $k => $v) {
                $unitArr[] = array('name' => $v);
            }
            return json_encode($unitArr);
        } else {
            $data = [
                '个',
                '件',
                '包',
                '只',
                '套',
                '对',
                '张',
                '本',
                '条',
                '根',
                '桶',
                '片',
                '瓶',
                '盒',
                '箱',
                '袋',
                '支',
                '把',
                '罐',
                '卷'
            ];
            $unitArr = array();
            foreach ($data as $k => $v) {
                $unitArr[] = array('name' => $v);
            }
            return json_encode($unitArr);
        }
    }

    public function setSort(){
        $post = input('post.');
        $StoreProductModel = new StoreProductModel;
        $data = array(
            'sort'=>$post['sort'],
        );
        $map = array(
            'merchant_id'=>$this->userinfo['merchantid'],
            'store_id'=>$this->userinfo['storeid'],
            'id'=>$post['id'],
        );
        $res = $StoreProductModel->updateProduct($map,$data);
        if($res){
            return $this->ajaxSuccess('设置成功');
        }
        $error = $StoreProductModel->error;
        return $this->ajaxFail($error?$error:'设置失败');
    }
}