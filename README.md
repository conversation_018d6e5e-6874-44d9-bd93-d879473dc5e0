- sql 5.6
- php 7.1

# Global

- storeid: layui.S_USER_TOKEN.storeid
- merchantid: layui.S_USER_TOKEN.merchantid

# TODO

+- 分类 改成 分类
+- 标签：去掉肩颈
+- 不要关联手艺人
+- 提成设置 ui 调整
+- 订单列表，+按钮改提成，+员工提成
+- 充卡数量需要可输入
+- 后台组出来的销售/理疗师，分割要用全角逗号
+- 打印服务订单要显示到期时间、剩余多少次
+- 收银端客户列表要显示客户备注名
+- 小程序留预约电话
+- 赠送功能：各种组合
+- 送的服务、产品等，小票应该显示 0 元
+- 最高指示不做短信通知
+- 客户评价加上可选填的话术：效果好、手势好等
+- 工作报告，定固定表单，打钩或选择，以便填写
+- 提成设置的“劳动”改成“推针”
+- 有效期到 205555 改成 2025-74-4 到期
+- 小票有效期单独可选是否显示
+- 财务报表，都要导出功能
+- 控制台，预警太久没来的客户列表，近 30 天内要过期的卡列表
+- 病历，定各种可选病，及其相关需要选择的表单。例如：糖尿病，什么时间开始，吃什么药了，各种相关指标多少等等。
+- 收银端，服务、产品默认展开、去掉批量；改价改优惠金额（扣减）
+- 小票显示优惠金额
+- 划卡仍有赠送 bug
+- 客户列表显示总剩余次数
+- 服务开单，不要扣卡优惠、
+- 小票：合计改成应收金额、手动改价叫优惠金额、数量叫消费数量
+- 加 + - 号，不要圆形
+- 划卡马上打印的小票，卡项详情看是否对
+- 小票打印划卡时，具体哪项服务剩余次数
+- 小票打印划卡两项不同充卡服务，收款流程直接打印，没有了卡项详情
+- 备注灰色地，一行过
+- 收银台可改下单时间
+- 小票详情，要扣了后的数量
+- 超赠送额度解决
+- 工作总结再权益记录上新建
+- 改成”设置提成“按钮
+- 病历+时间，病历标题居中
+- 小程序不能看到别的店铺，新客户进入只能看到默认内容
+- 小程序订单如果扣了卡，需要显示剩余多少次、共多少次、期限等
+- 划卡收费单独界面，不要右边数字
+- 搜索进入的新客户，注册归总部
+- 客户列表显示划卡为 0 的服务
+- 拉卡拉列表加上刷新按钮
+- 要不要库存功能，不要
+- 生成小程序码
+- 都分享首页。
+- 存蓄卡功能，收钱可选扣金额
+- 门店可修改卡价格
+- 60 次卡（含 6 项服务）改成：服务卡项（含 6 项服务）
+- 权益记录 -> 扣卡记录
+- 每个产品要有会员价
+- 会员价，改了数量，自动重算优惠价
+- ui 订单左对齐
+- 该客户已在总部建立客户信息，是否继续注册使用已创建信息
+- 品牌文化改品牌动态，首页竖向三条，图文左右排
+- 分享、扫店铺内的小程序码，新注册要归那个店
+- 提示：如果用户以创建，加一句提示，可以直接去收银端开卡
+- 小程序总部、未注册的服务产品都不要显示
+- 扫哪家的码，就要显示哪家门店的信息，也顺便给绑定到这个店。分享也一样。
+- 如果客户同时属于 AB 两个店，AB 店的收银端都要显示这个客户
+- 小程序总部要没有预约电话
+- 重新计算提成功能
+- 做图片过小程序定位权限审核
+- 财务报表 + 门店筛选框，不是算总店的报表
+- 提成方案 叫回 分类
+- 有成为会员，才自动计算会员价
+- 标签不要了
+- 会员详情中会员卡改成卡项
+- 不是会员还有会员价 bug
+- 流水列表 加客户信息的搜索及显示详情，加门店筛选
+- 卡项列表 加客户搜索，要重写次数展现方式
+- 收银端余额支付前面不选，后置
+- 下架产品启用时，提示做弹框明显点
+- 财务报表店铺下拉搜索
+- 充值界面改，直观显示充多少送多少，自定义充值的输入做大。
+- 充值界面带上客户选框
+- 总部客户列表需要搜索，按手机号，客户名、备注名，按卡号
+- 设置提成放上一行
+- 充值页面调整
+- 小程序绑定设备
+- 订单列表需要搜索，按手机号，客户名、备注名，按卡号

- 业绩提成设置下放门店
- 权益改名、平均扣多少钱
- 订单、应收、实收、优惠多少钱、图片不要，行缩小
- 病例加视频
- 提成分：产品、卡、服务等
- 打印小票用余额支付后，会员信息有 bug

- 网页端客户列表，加按时间段筛选在期间有过消费的客户
- 门店端小程序要有直播功能
- 病例用其他公司资质的小程序

- 赠送 +时间选一年

- 客户端要可改客户资料
- 病历单独出菜单

- 客户的累计消费金额次数等要根据门店不同而不同
- 会员信息独立：交易、病例、体验、设备绑定
- 归属门店不要、新客来源不要、新客归属不要
- 收银端不让搜别店或总部的客户
- 小程序权我的权益不要
- 我的卡项，直接进入所有卡项的页面
- 组合卡页面，蓝色卡中些卡的基础信息。页面名字叫服务卡项。会员权益改叫卡项详情，
- 点击卡项中的服务后，跳转到我的权益点击具体服务跳转的页面
- 我的信息页面，字再大一点

- 打印小票可选不显示价钱
- 直播要能连麦
- 写使用手册

- 也要有个总开关控制是否自动用会员价
- 小程序订单些多点中文，清晰说明
- 划卡页面，美观点
- 可配置小程序上仪器不显示价格

- 交易类型，按产品，按服务类型，按划卡
- 会员详情需要显示服务卡项，以及总次数和剩余
- 卡项列表加搜索
- 员工绩效，把服务和产品的数量，单价，类别区分显示出来
- 绩效汇总需要销售总额，服务总次数，服务总额

- 订单列表中，抵扣要写中文写清楚；消费要写消费两个字，会员详情中的交易记录也要改
- 品项改名服务，现在品项包含了产品，产品反而空。需要下单的时候区分订单类型改成 2 代表产品（1 是代表品项） http://192.168.8.188/index.php?s=/store/index/index#/Order/index
- 权益记录是否包含扣余额，要做赛选扣卡还是扣余额，也要搜索客户 http://192.168.8.188/index.php?s=/store/index/index#/Memberequity/index
- 卡项列表 要加导出
- http://192.168.8.188/index.php?s=/store/index/index#/Commission/index 数量、单价要显示出来，要有类型区分（服务、产品、售卡、充值等等）
- http://192.168.8.188/index.php?s=/store/index/index#/Commission/index 汇总页面要分清楚：划卡、扣余额、
- 提成绩效表 + 分类统计；服务和扣卡要分开，产品单独提出来，总额不要；扣卡总额、扣卡总次数；明细里面加数量和单价；单次服务也放到售卡；自助理疗科要单独统计出来
- 产品销售总额、服务销售总额（不算自主理疗卡、单次服务）、自助理疗卡销售、服务提成

- 新建卡的时候，加卡类型：服务卡、自主理疗卡；一开始是空的
- 流水列表，卡项类型下拉，品项要拆分成服务卡、产品，加多个自主理疗分类

- 充值 bug：第一次，选客户，填金额及送金额，选账号，点收款，跳转到了划卡界面。办卡也会触发。未发现如何稳定复现。
- 小票中要显示充值完后里面有多少钱
- 收银页面，上面按钮跟下面有点脱离，修改。支付方式，写这几个字。重做页面。确认收款都在中间。
- 去掉抹零功能，现金收款不要键盘，不要改金额
- 小程序仪器不要显示价格

