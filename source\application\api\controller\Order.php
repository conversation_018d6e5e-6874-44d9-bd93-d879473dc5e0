<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-04-30
 * Time: 9:02
 */

namespace app\api\controller;

use app\agent\model\Merchant as MerchantModel;
use app\api\model\Payorder as PayorderModel;
use app\store\model\Booker as BookerModel;
use app\store\model\Card as CardModel;
use app\pay\model\Payment as PaymentModel;

use app\store\model\CardDetail as CardDetailModel;
use app\store\model\MemberCard as MemberCardModel;
use app\store\model\Member as MemberModel;
use app\store\model\MemberCardDetail as MemberCardDetailModel;
use app\store\model\Bookserver as BookserverModel;
use app\api\model\Staff as StaffModel;
use app\store\model\OperateGuestPlan as GuestPlanModel;
use app\store\model\Stoserviec as StoserviceModel;
use app\store\model\Receipt as ReceiptModel;
use Exception;
/*06-29合伙人*/

use app\store\model\Promoters as PromotersModel;
use app\pagecommon\model\Material as MaterialModel;

use app\store\model\Membercode as MembercodeModel;
use app\android\model\Memberentitycard as MemberentitycardModel;

use app\store\model\Memberpaymentset as MemberpaymentsetModel;
use app\store\model\Storeglobal as StoreglobalModel;

use app\store\model\OperateGuestPlanRecord as OperateGuestPlanRecordModel;
use app\store\model\OperateGuestPlanBind as GuestPlanBindModel;

use app\store\model\BonusBind as BonusBindModel;
// 股东礼券
use app\store\model\ShareholderGiftCouponIssue as ShareholderGiftCouponIssueModel;
use app\store\model\ShareholderGiftCouponRecord as ShareholderGiftCouponRecordModel;

//<!--[meiye_10_13]-->
class Order extends Controller
{
	protected $error;
	protected $merchantid = 0;
	protected $storeid = 0;

	protected $status = 1;  // 全局产品状态               xcx  1;       android  array('in','1,2')

	protected $issellonline = true;          // 全局产品是否必须线上售卖   xcx  true       android  false

	protected $interfaceType = 'xcx';        // 接口类型    xcx  android

	protected $getPostData = 0;

	public function __construct($interface = 'xcx')
	{
		parent::__construct();
		$re = $this->checkPostData();
		if ($re) {
			echo json_encode($re);
			exit;
		}
		$this->interfaceType = $interface;
		if ($interface == 'android') {
			$this->status = array('in', '1,2');
			$this->issellonline = false;
		}
	}

	public function checkPostData()
	{
		$post = input('post.');
		$merchantid = $post['merchantid'];  // 商户id
		$storeid = $post['storeid'];        //  门店id
		if (!$merchantid) {
			return $this->ajaxFail('商户id不能为空');
		}
		if (!$storeid) {
			return $this->ajaxFail('店铺id不能为空');
		}
		$this->merchantid = $merchantid;
		$this->storeid = $storeid;
		if ($this->getPostData) {
			return $this->ajaxSuccess(input('post.'));
		}
	}

	public function yzjson()
	{

		$yzjson = '{"deptId":"********","type":1,"sourceType":3,"buyer":{"yzUid":1358441574},"orderItems":[{"itemType":2,"itemId":*********,"itemName":"1元次卡","originPrice":100,"num":1,"realPay":100,"stage":1,"cardSnapId":492788,"salesmen":[]}],"promotions":[],"orderGiftItems":[]}';
		$yzjson2 = '{"deptId":"********","type":2,"sourceType":3,"buyer":{"yzUid":1358441574},"orderItems":[{"itemType":4,"itemId":"1904300944476087","itemName":"100充值卡","originPrice":10000,"num":1,"realPay":10000,"stage":1,"cardSnapId":492829,"salesmen":[]}],"promotions":[],"orderGiftItems":[{"giftItemUnique":*********,"giftItemName":"美甲","giftType":1,"giftItemValue":1,"giftItemTermTime":"2020-04-30"}],"remark":"100元充值卡"}';
		$yzjson3 = '{"type":0,"sourceType":3,"deptId":"********","orderItems":[{"itemSnapshotId":"65JxdZSeUPMx2bBSLv8rqGLN","itemId":1529054,"itemType":1,"itemName":"美甲","itemAlias":"3ne8eeoxgxj7v","goodsId":*********,"skuId":********,"stockNum":0,"num":1,"originPrice":2000,"realPay":1600,"stage":1,"specifications":[{"name":"颜色","value":"紫色"}],"uuid":10,"promotion":{"id":-1,"rightsId":-1,"cardName":"手动改价","goodsName":"直接修改小计金额","rightsType":-1},"promotionId":-1,"promotionType":0,"promotionValue":0,"promotionName":"手动改价","promotionPrice":0,"promotionStage":1,"manual":1600,"salesmen":[],"technicians":[]},{"itemSnapshotId":"65JxdZSeUPMx2bBSLv8rqGLN","itemId":1529054,"itemType":1,"itemName":"美甲","itemAlias":"3ne8eeoxgxj7v","goodsId":*********,"skuId":********,"stockNum":0,"num":1,"originPrice":2000,"realPay":2000,"stage":1,"specifications":[{"name":"颜色","value":"红色"}],"uuid":9,"promotion":{"id":0,"rightsId":0,"cardName":"不使用权益","goodsName":"","rightsType":0},"promotionId":0,"promotionType":0,"promotionName":null,"promotionPrice":0,"promotionStage":1,"salesmen":[],"technicians":[]},{"itemSnapshotId":"65JxdZSeUPMx2bBSLv8rqGLN","itemId":1529054,"itemType":1,"itemName":"美甲","itemAlias":"3ne8eeoxgxj7v","goodsId":*********,"skuId":********,"stockNum":0,"num":1,"originPrice":2000,"realPay":1600,"stage":1,"specifications":[{"name":"颜色","value":"紫色"}],"uuid":5,"promotion":{"goodsId":*********,"goodsName":"美甲","rightsType":1,"subRightsType":200,"unlimitedTimes":0,"gift":0,"value":80,"remainValue":80,"termBeginAt":0,"termEndAt":0,"rightsId":********,"cardId":*********,"cardNo":"519043009483566762","accountNo":"***************","remainCapitalBalance":10000,"remainPresentBalance":2000,"remainTotalBalance":12000,"cardName":"100充值卡","cardType":3,"personMemberCardVO":{"allDiscount":0,"allDiscountValue":0},"allDiscount":0,"sourceType":1},"promotionId":********,"promotionType":2,"promotionCardNo":"519043009483566762","promotionValue":80,"promotionRemainValue":80,"promotionName":"会员卡8折","promotionPrice":400,"promotionStage":1,"consumeAccountNo":"***************","consumeAccountPrice":1600,"salesmen":[],"technicians":[],"accountInfo":{"accountNo":"***************","accountName":"100充值卡","totalBalance":12000,"capitalBalance":10000,"presentBalance":2000,"isDefaultAccount":0,"accountStatus":1,"openDeptId":********,"onlyConsumable":false,"unavailable":false}},{"itemSnapshotId":"65JxdZSeUPMx2bBSLv8rqGLN","itemId":1529054,"itemType":1,"itemName":"美甲","itemAlias":"3ne8eeoxgxj7v","goodsId":*********,"skuId":********,"stockNum":0,"num":1,"originPrice":2000,"realPay":0,"stage":1,"specifications":[{"name":"颜色","value":"红色"}],"uuid":7,"promotion":{"goodsId":*********,"goodsName":"美甲","rightsType":2,"subRightsType":200,"unlimitedTimes":0,"gift":0,"value":10,"remainValue":10,"termBeginAt":0,"termEndAt":0,"rightsId":********,"cardId":*********,"cardNo":"519043009240488322","cardName":"1元次卡","cardType":1,"personMemberCardVO":{"allDiscount":0,"allDiscountValue":0},"allDiscount":0,"sourceType":1,"promotionValue":1},"promotionId":********,"promotionType":1,"promotionCardNo":"519043009240488322","promotionValue":1,"promotionRemainValue":10,"promotionName":"会员卡抵扣1次","promotionPrice":2000,"promotionStage":1},{"itemSnapshotId":"65JxdZSeUPMx2bBSLv8rqGLN","itemId":1529054,"itemType":1,"itemName":"美甲","itemAlias":"3ne8eeoxgxj7v","goodsId":*********,"skuId":********,"stockNum":0,"num":1,"originPrice":2000,"realPay":0,"stage":1,"specifications":[{"name":"颜色","value":"红色"}],"uuid":6,"promotion":{"goodsId":*********,"goodsName":"美甲","rightsType":2,"subRightsType":200,"unlimitedTimes":0,"gift":0,"value":10,"remainValue":10,"termBeginAt":0,"termEndAt":0,"rightsId":********,"cardId":*********,"cardNo":"519043009240488322","cardName":"1元次卡","cardType":1,"personMemberCardVO":{"allDiscount":0,"allDiscountValue":0},"allDiscount":0,"sourceType":1,"promotionValue":1},"promotionId":********,"promotionType":1,"promotionCardNo":"519043009240488322","promotionValue":1,"promotionRemainValue":10,"promotionName":"会员卡抵扣1次","promotionPrice":2000,"promotionStage":1}],"buyer":{"yzUid":1358441574},"orderGiftItems":[],"promotions":[],"remark":""}';
		return json_decode($yzjson3, true);
	}

	public function postjson()
	{
		$arr = array(
			'storeid' => 37,
			'merchantid' => 17,
			'cashierId' => 1,
			'sourceType' => '',
			'orderType' => 1, // 1：品项（产品服务开单），2：品项（产品服务开单）3：购买卡项，4：充值 ，5：组合卡购买。
			'orderNo' => '',
			'orderItems' => array(
				array(
					'itemId' => 1,                //
					'itemType' => 1,              // 1 服务 2产品 3卡项 4充值
					'itemName' => '美甲AAACCC',
					'itemImgId' => '1',
					'goodsId' => 3,               // 服务 产品 卡项 充值 的id （没有填写0）
					'skuId' => 5,
					'skuName' => '',
					'num' => 1,                   // 数量 除产品 其他均为1
					'originPrice' => 2000,       // 原价( 分 )
					'realPay' => 1600,            //真实支付
					'stage' => 1,                 // 当前阶段
					'equityType' => 4,            // 1 无权益 2折扣 3抵扣 4手动改价
					'cardId' => 5,                //  卡项id
					'cardDetailsId' => 5,         //  卡项详情id
					'discount' => '10.00',
					'salesmen' => array(),
					'technicians' => array(
						array('id' => 1, 'dot' => 1)
					),
					'present_money' => 0,           //  充值金额 (分)    手动充值时必传
					'recharge_money' => 0,          //  赠送金额 （分）  手动充值时必传
				),
				array(
					'itemId' => 2,                //
					'itemType' => 1,              // 1 服务 2产品 3卡项 4充值
					'itemName' => '美甲BBBCCC',
					'itemImgId' => '1',
					'goodsId' => 3,
					'skuId' => 7,
					'skuName' => '精致,鼻子',
					'num' => 1,                   // 数量 除产品 其他均为1
					'originPrice' => 2000,       // 原价( 分 )
					'realPay' => 1600,            //真实支付
					'stage' => 1,                 // 当前阶段
					'equityType' => 4,            // 1 无权益 2折扣 3抵扣 4手动改价
					'cardId' => 5,                //  卡项id
					'cardDetailsId' => 4,         //  卡项详情id
					'discount' => '10.00',
					'salesmen' => array(),
					'technicians' => array(),
				)
			),
			'buyerId' => 1,//会员id
			'orderGiftItems' => array(),
			'promotions' => array(),
			'remark' => '123456',
			'addressInfo' => '',
			'dispatchType' => 1, // 配送类型：0，非配送，1，到店自提，2，配送。
			'dispatchFee' => 0
		);
		$arr['orderItems'] = htmlspecialchars(json_encode($arr['orderItems']));
		return $arr;
	}

	public function testJson()
	{
		$testjson = '{"bookerid":0,"buyerId":468,"cashierId":"25","merchantid":"17","orderGiftItems":[],"shift_no":"W2019062981098","orderItems":"[{\"cardId\":0,\"cardDetailsId\":0,\"cardName\":\"\",\"discount\":\"10.00\",\"equityType\":1,\"goodsId\":2,\"itemName\":\"美甲100\",\"itemType\":\"1\",\"num\":1,\"realPay\":10000,\"originPrice\":10000,\"salesmen\":[],\"skuId\":\"0\",\"skuName\":\"\",\"technicians\":[],\"itemImgId\":32,\"present_money\":\"0\",\"recharge_money\":\"0\",\"stage\":\"1\",\"itemId\":\"3545\"}]","orderNo":"M201907011420584013985","orderType":"1","promotions":[],"remark":"","sourceType":3,"storeid":"37","failReturnDataType":1}';
		return json_decode($testjson, true);
	}

	protected $planRecordData = array(); // 获客信息

	protected $giftCouponData = array();// 礼券信息

	public function orderSave()
	{
		$CardModel = new CardModel;

		$MemberCardModel = new MemberCardModel;
		$post = input('post.');
		if (isset($post['debug']) && $post['debug'] == 1) {
			$data['oldpost'] = $post;
			$post['orderItems'] = htmlspecialchars_decode($post['orderItems']);
			$items = json_decode($post['orderItems'], true);
			$post['orderItems'] = $items;
			$data['post'] = $post;
			return $this->ajaxSuccess($data);
		}
		try {
			/*11-09 新增商家账号有效期判断*/
			//<!--[meiye_yxq]-->
			$MerchantModel = new MerchantModel;
			$mchMap = array(
				'id' => $this->merchantid,
			);
			$one = $MerchantModel->getFind($mchMap);
			$time = time();
			if ($one['begin_time'] - $time > 0) {
				return $this->ajaxFail("您登陆的商家暂未启用，请确认");
			}
			if ($one['end_time'] - $time < 0) {
				return $this->ajaxFail("您登陆的商家已过期，请确认");
			}
			$orderSaveStep = 1;
			if ($this->interfaceType == 'android') {
				if (!isset($post['shift_no']) || (isset($post['shift_no']) && !$post['shift_no'])) {
					return $this->ajaxFail('班次单号必须填写');
				}
			}
			if (isset($post['orderNo']) && $post['orderNo']) {
				$PayorderModel = new PayorderModel;
				$w = array(
					'merchant_id' => $this->merchantid,
					'store_id' => $this->storeid,
					'order_number' => $post['orderNo']
				);
				$append = array(
					'orderDetails'
				);
				$one = $PayorderModel->getFind($w, 'id,state,stage,vip_id as orderEditVipId', $append);
				if (!$one) {
					return $this->ajaxFail('订单不存在，请确认');
				}
				if ($one['state'] != 1) {
					return $this->ajaxFail('不是待付款订单');
				}
				if ($one['stage'] != 1) {
					return $this->ajaxFail('订单正在支付中，请确认');
				}
				$one['post'] = $post;
				return $this->orderedit($post, $one['id'], $one['orderDetails'], $one['orderEditVipId']);
			} else {
				$orderType = $post['orderType'];
				//1：品项（产品服务开单），
				//2：品项（产品服务开单）
				//3：购买卡项，
				//4：充值 ，
				//5：组合卡购买,
				//6 直接收款
				//7 获客策划活动
				if (($orderType == 3 || $orderType == 4 || $orderType == 5 || $orderType == 7 || $orderType == 8) && !$post['buyerId']) {
					return $this->ajaxFail('当前业务，会员信息不能为空');
				}
				$orderSaveStep = 2;
				$addData = array(
					'order_time' => time(),
					'collection_time' => '',
					'vip_id' => $post['buyerId'],
					'merchant_id' => $this->merchantid,
					'store_id' => $this->storeid,
					'cashier_id' => $post['cashierId'],
					'customer_remark' => $post['remark'],
					'state' => 1,
					'stage' => 1,
					'net_receipts' => 0,
					'source_type' => 1,                       // 单据来源类型：1，普通开单，2，预约开单，3，商城单据
					'source_id' => 0                          // 单据来源id  预约单的id(source_type==2)
				);
				$post['orderItems'] = htmlspecialchars_decode($post['orderItems']);
				$items = json_decode($post['orderItems'], true);
				/*2020-10-20 线下推广员获得奖励*/
				$promoterId = $this->getPromoterId($post['buyerId']);
				/*2019-08-29 新增直接商城下单 店内核销*/
				if (isset($post['isVerify']) && $post['isVerify'] == 1) {
					$addData['is_verify'] = 1;
					$addData['shoper'] = removeEmoji($post['shoper']);
					$addData['shoper_phone'] = $post['shoperPhone'];
					if ($this->interfaceType != 'xcx') {
						return $this->ajaxFail('只有商城直接购买服务，等待核销');
					}
					if ($orderType != 1) {
						return $this->ajaxFail('直接购买服务等待核销必须是品项订单');
					}
					if (count($items) > 1) {
						return $this->ajaxFail('直接购买服务等待核销服务只能是一个');
					}
				}
				// 将商城单据来源修改为 3
				if ($this->interfaceType == 'xcx') {
					$addData['source_type'] = 3;
				}
				if (isset($post['shift_no'])) {
					$addData['shift_no'] = $post['shift_no'];
				}
				$orderSaveStep = 3;
				if ($post['sourceType'] == 2 && $post['bookerid']) {
					$BookerModel = new BookerModel;
					$b = $BookerModel->getDis(array('id' => $post['bookerid']), array('paytype', 'remoney', 'status', 'paystate', 'phone', 'shoper'));
					if ($b && $b['status'] == 2) {
						return $this->ajaxFail('该预约单已开单');
					}
					if ($b && $b['status'] == 3) {
						return $this->ajaxFail('该预约单已取消');
					}
					if ($b && ($b['paytype'] == 1 || $b['paytype'] == 3) && $b['paystate'] == 2) {
						$net_receipts = $b['remoney'] ? $b['remoney'] : 0;
					} else {
						$net_receipts = 0;
					}
					if (isset($b['phone']) && $b['phone'] && isset($b['shoper']) && $b['shoper']) {
						$addData['shoper'] = $b['shoper'];
						$addData['shoper_phone'] = $b['phone'];
					}
					$addData['source_type'] = 2;
					$addData['source_id'] = $post['bookerid'];
					$res = $BookerModel->getSave(array('id' => $post['bookerid']), array('status' => 2, 'billtime' => time()));
					if (!$res) {
						return $this->ajaxFail('预约单开单失败，请重试');
					}
				} else {
					$net_receipts = 0;
				}
				$orderSaveStep = 4;
				$addData['net_receipts'] = $net_receipts;
				// 地址信息
				if (isset($post['addressInfo'])) {
					$addData['address_info'] = $post['addressInfo'];
				}
				// 配送类型
				if (isset($post['dispatchType'])) {
					$addData['dispatch_type'] = $post['dispatchType'];
				}
				// 运费
				if (isset($post['dispatchFee'])) {
					$addData['dispatch_fee'] = $post['dispatchFee'];
				} else {
					$addData['dispatch_fee'] = 0;
				}
				$detailsData = array();
				$receivable = 0;
				$type = 2;
				$equityUseArr = array();
				$average =0;
				$deduction = 0;
				$manually = 0;
				$dismoney = 0;
				$service_money = 0;
				$product_money = 0;
				$orderSaveStep = 5;
				/*06-29新增合伙人*/
				$promoterIdArr = array();
				$payCardArr = array();
				// 权益类型 1 无权益 2折扣 3抵扣 4手动改价
				foreach ($items as $k => $v) {
					$present_money = 0;
					$recharge_money = 0;
					if ($v['itemType'] == 3) {
						$type = 3;
					} else if ($v['itemType'] == 4) {
						$type = 4;
						$present_money = $v['present_money'];
						$recharge_money = $v['recharge_money'];
					} else if ($v['itemType'] == 1) {
						$type = 1;
					} else if ($v['itemType'] == 5) {
						$type = 5;
					}
					if (!is_array($v['technicians'])) {
						$v['technicians'] = json_decode($v['technicians'], true);
					}
					$v['technicians'] = ($v['technicians'] ? $v['technicians'] : array());
					$technicians = '';
					foreach ($v['technicians'] as $vk => $vv) {
						$technicians .= $vv['id'] . ',';
					}
					$technicians = trim($technicians, ',');
					if (!is_array($v['salesmen'])) {
						$v['salesmen'] = json_decode($v['salesmen'], true);
					}
					$v['salesmen'] = ($v['salesmen'] ? $v['salesmen'] : array());
					$v['equityType'] = $v['equityType'] ? $v['equityType'] : 1;
					if (!$v['itemName']) {
						return $this->ajaxFail('订单详情名称不能为空');
					}
					if ($v['originPrice'] != intval($v['originPrice'])) {
						return $this->ajaxFail("订单内容{$v['itemName']}单价有误");
					}
					if ($v['realPay'] != intval($v['realPay'])) {
						return $this->ajaxFail("订单{$v['itemName']}小计有误");
					}
					$item = array(
						'type' => $v['itemType'],
						'name' => $v['itemName'],
						'img' => $v['itemImgId'],
						'price' => $v['originPrice'],
						'num' => $v['num'] * 1,
						'reduceprice' => $v['originPrice'] * $v['num'] - $v['realPay'],
						'equity_type' => $v['equityType'],
						'discount' => $v['discount'],
						'card_id' => $v['cardId'],
						'card_details_id' => $v['cardDetailsId'],
						'goods_id' => $v['goodsId'],
						'sku_id' => $v['skuId'],
						'sku_name' => $v['skuName'],
						'store_id' => $this->storeid,
						'merchant_id' => $this->merchantid,
						'staff_id' => $technicians,
						'technicians' => json_encode($v['technicians']),
						'sells_id' => implode(',', $v['salesmen']),
						'present_money' => $present_money,
						'recharge_money' => $recharge_money
					);

					/*07-17新增耗卡*/
					if (isset($v['consumeCard']) && $v['consumeCard'] == 2) {
						if (!isset($v['consumeCardId'])) {
							return $this->ajaxFail("{$v['itemName']}使用耗卡,耗卡卡项必须设置");
						}
						if ($v['equityType'] == 3) {
							return $this->ajaxFail("{$v['itemName']}使用抵扣权益类型，不支持耗卡");
						}
						if ($v['cardId'] && $v['equityType'] == 2 && $v['itemType'] != 3) {
							if ($v['cardId'] != $v['consumeCardId']) {
								return $this->ajaxFail("{$v['itemName']}使用充值卡折扣权益，耗卡卡项必须与充值卡一致");
							}
						}
						$item['consume_card'] = 2;
						$item['consume_card_id'] = $v['consumeCardId'];
					} else {
						$item['consume_card'] = 1;
						$item['consume_card_id'] = 0;
					}
					/*06-29新增合伙人*/
					if (isset($v['promoterId']) && $v['promoterId']) {
						$item['promoter_id'] = $v['promoterId'];
						$promoterIdArr[] = $v['promoterId'];
					} else {
						$item['promoter_id'] = $promoterId;
					}
					// 新增下单限制
					//      itemType    1 服务 2产品 3卡项 4充值  5 直接收款
					//      orderType   1：品项（产品服务开单），2：品项（产品服务开单）3：购买卡项，4：充值 ，5：组合卡购买,6直接收款。
					//      equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
					if (!$post['buyerId']) {
						if ($v['equityType'] == 2 || $v['equityType'] == 3) {
							return $this->ajaxFail("非会员订单，{$v['itemName']}不支持折扣及抵扣权益");
						}
					}
					/*// 未使用权益
					if ($v['equityType'] == 1) {
						if ($item['reduceprice'] != 0) {
							return $this->ajaxFail("订单内容未使用权益，{$v['itemName']}小计有误，请确认");
						}
					} else if ($v['equityType'] == 2) {
						$reduceprice = round($v['originPrice'] * $v['num'] * (10 - $v['discount']) / 10, 0);
						if (abs(($item['reduceprice'] - $reduceprice)) > 1) {
							return $this->ajaxFail("订单内容使用折扣，{$v['itemName']}小计有误，请确认");
						}
					} else if ($v['equityType'] == 3) {
						$reduceprice = $v['originPrice'] * $v['num'];
						if ($item['reduceprice'] != $reduceprice) {
							return $this->ajaxFail("订单内容使用抵扣，{$v['itemName']}小计有误，请确认");
						}
					}
					if ($v['itemType'] == 1) {
						// 服务
						if ($orderType == 5) {
							//  购买组合卡
							if ($v['equityType'] != 1) {
								return $this->ajaxFail('组合卡业务，订单内容不能使用权益，请确认');
							}
						} else {
							if ($orderType != 1 && $orderType != 2 && $orderType != 5) {
								return $this->ajaxFail('订单类型与订单内容不匹配');
							}
						}
					} else if ($v['itemType'] == 2) {
						// 2产品
						if ($orderType == 5) {
							return $this->ajaxFail('组合卡业务，订单内容不能包含产品');
						} else {
							if ($v['equityType'] == 3) {
								return $this->ajaxFail("{$v['itemName']}产品暂不支持抵扣权益");
							}
							if ($v['equityType'] == 2) {
								if ($v['cardId']) {
									return $this->ajaxFail("{$v['itemName']}产品暂不支持卡项折扣权益");
								}
							}
						}
					} else if ($v['itemType'] == 3) {
						// 3卡项
						if ($orderType != 3) {
							return $this->ajaxFail("订单不为购买卡项订单，但内容包含卡项{$v['itemName']}，下单失败，请确认");
						}
						if ($v['equityType'] == 3 || $v['equityType'] == 2) {
							return $this->ajaxFail('办卡不支持折扣及抵扣，请确认');
						}
					} else if ($v['itemType'] == 4) {
						// 4充值
						if ($orderType != 4) {
							return $this->ajaxFail("订单不为充值订单，但内容包含充值{$v['itemName']}，下单失败，请确认");
						}
						if ($v['equityType'] == 3 || $v['equityType'] == 2) {
							return $this->ajaxFail('充值不支持折扣及抵扣，请确认');
						}
					} else if ($v['itemType'] == 5) {
						// 5 直接收款
						if ($orderType != 6) {
							return $this->ajaxFail("订单不为直接收款订单，但内容包含直接收款{$v['itemName']}，下单失败，请确认");
						}
						if ($v['equityType'] != 1) {
							return $this->ajaxFail('直接收款不支持权益使用，请确认');
						}
					}else if($v['itemType'] == 6){
						// 6 获客策划
						if ($orderType != 7) {
							return $this->ajaxFail("订单不为获客策划活动订单，但内容包含获客策划{$v['itemName']}，下单失败，请确认");
						}
						if ($v['equityType'] == 3 || $v['equityType'] == 2) {
							return $this->ajaxFail('获客策划活动不支持折扣及抵扣，请确认');
						}
					}*/
					// 检测权益使用 、订单类型与订单内容类型匹配程度
					$checkEquityType = $this->checkEquityType($v, $item, $orderType, $post['buyerId']);
					if ($checkEquityType['code'] == 0) {
						return $checkEquityType;
					}

					$item['card_name'] = isset($v['cardName']) ? $v['cardName'] : '';

					if ($v['itemType'] == 1) {
						$service_money += $v['realPay'];
					} else if ($v['itemType'] == 2) {
						$product_money += $v['realPay'];
					}
					switch ($v['equityType']) {//累计
						case 2:
							$dismoney += $item['reduceprice'];//折扣权益优惠
							break;
						case 3:
							$deduction += $item['reduceprice'];//抵扣权益优惠。

							$mcwhere['id'] = $item['card_id'];
							// $mcfield = array('average_price');
							// $mcinfo = $MemberCardModel->getFind($mcwhere, $mcfield);
							// $average += intval($mcinfo['average_price']);//均价抵扣权益优惠。
							break;
						case 4:
							$manually += $item['reduceprice'];//手动改价优惠
							break;
						case 1:
						default:
							break;
					}
					$receivable += $v['realPay'];
					$detailsData[] = $item;
					// 兼容
					if (!isset($v['consumeCard'])) {
						if (($v['equityType'] == 2 || $v['equityType'] == 3) && $v['stage'] == 1 && $v['cardId'] != 0) {
							$equityUseArr[] = array(
								'price' => $v['originPrice'],
								'num' => $v['num'],
								'realPay' => $v['realPay'],
								'equityType' => $v['equityType'],
								'cardId' => $v['cardId'],
								'cardDetailsId' => $v['cardDetailsId'],
								'cardName' => $item['card_name']
							);
						}
					} else {
						if ($v['stage'] == 1) {
							if ($item['equity_type'] == 3) {
								// 抵扣权益
								$equityUseArr[] = array(
									'price' => $v['originPrice'],
									'num' => $v['num'],
									'realPay' => $v['realPay'],
									'equityType' => $v['equityType'],
									'cardId' => $v['cardId'],
									'cardDetailsId' => $v['cardDetailsId'],
									'cardName' => $item['card_name']
								);
							} else {
								if ($v['consumeCard'] == 2) {
									// 耗卡
									$equityUseArr[] = array(
										'price' => $v['originPrice'],
										'num' => $v['num'],
										'realPay' => $v['realPay'],
										'equityType' => 5,
										'cardId' => $v['consumeCardId'],
										'cardDetailsId' => $v['cardDetailsId'],
										'cardName' => $item['card_name']
									);
									$payCardArr[] = array(
										'money' => $v['realPay'],
										'cardId' => $v['consumeCardId'],
									);
								}
							}
						}
					}
				}
				
				$orderSaveStep = 7;
				if (isset($post['dispatchType']) && $post['dispatchType'] == 2) {
					$receivable += $addData['dispatch_fee'];
				}
				$addData['average'] = $average;                 // 卡项均价抵扣总数 用于计算人均
				$addData['deduction'] = $deduction;             // 抵扣权益优惠
				$addData['manually'] = $manually;               // 手动改价优惠
				$addData['dismoney'] = $dismoney;               // 折扣优惠权益
				$addData['service_money'] = $service_money;     // 服务金额
				$addData['product_money'] = $product_money;     // 产品金额
				$addData['type'] = $orderType;                   // 订单类型 1服务 2产品（1,2品项） 3售卡 4充值 5组合卡 6 快速收款
				$addData['receivable'] = $receivable;           //  订单小计
				$orderSaveStep = 8;
				// 充卡
				if ($orderType == 5) {
					if (count($equityUseArr) > 0) {
						return $this->ajaxFail('组合卡购买开单不能使用权益，请确认');
					}
					// 组合卡购买
					if (isset($post['extraData'])) {
						if (is_string($post['extraData'])) {
							$post['extraData'] = htmlspecialchars_decode($post['extraData']);
							$post['extraData'] = json_decode($post['extraData'], true);
						}
						//  单据额外信息
						$indate = $post['extraData']['indate'];                      // 永久传0，其他传 2019-08-01
						if ($indate != '0') {
							$indate = strtotime($indate . ' 23:59:59');
						}
						$comboCardMoney = $post['extraData']['comboCardMoney'];     // 组合卡最终金额。。。
						$extra = array(
							'indate' => $indate,
							'comboCardMoney' => $comboCardMoney
						);
						$addData['extra'] = json_encode($post['extraData']);
						// 重置金额数据
						$addData['service_money'] = 0;                              // 充卡 出售
						$addData['product_money'] = 0;                              // 充卡 出售
						$addData['receivable'] = $comboCardMoney;
						$addData['manually'] = $receivable - $comboCardMoney;       // 总计算价减最终价
					} else {
						return $this->ajaxFail('组合卡购买开单时，有效期及最终金额不能为空');
					}
				} else if ($orderType == 7) {
					// 策划活动订单附加信息
					// 1 分享来源用户类型
					// 2 分享来源pid
					// 3 分享来源用户id
					if (isset($post['extraData'])) {
						if (is_string($post['extraData'])) {
							$post['extraData'] = htmlspecialchars_decode($post['extraData']);
							$post['extraData'] = json_decode($post['extraData'], true);
						}
						$this->planRecordData['from_user'] = $post['extraData']['from_user'];
						$this->planRecordData['from_user_type'] = $post['extraData']['from_user_type'];
						$this->planRecordData['pid'] = $post['extraData']['pid'];
						$this->planRecordData['from_code'] = $post['extraData']['from_code'];
						if (!$this->planRecordData['from_code']) {
							return $this->ajaxFail('参数from_code不能为空');
						}
						$guestPlanData = $this->planRecordData['guestPlanData'];
						$planStaffId = 0;
						if ($this->planRecordData['from_user_type'] == 2) {
							// 来源于客户分享
							if ($guestPlanData['new_vip'] == 1) {
								// 新用户
								$MemberModel = new MemberModel;
								$memberInfo = $MemberModel->getFinds(array('id' => $post['buyerId']), 'count');
								if (!$memberInfo) {
									return $this->ajaxFail('用户信息有误');
								}
								if ($memberInfo['count'] > 0) {
									return $this->ajaxFail('抱歉，当前活动仅限新用户参与');
								}
							}
							$pMap = array(
								'plan_id' => $guestPlanData['id'],
								'merchantid' => $this->merchantid,
								'id' => $this->planRecordData['pid']
							);
							$pPlanRecordData = (new OperateGuestPlanRecordModel)->getFind($pMap);
							if (!empty($pPlanRecordData) && $pPlanRecordData['original_user_type'] == 1) {
								// 原始来源是员工
								$planStaffId = $pPlanRecordData['original_user'];
							}
						} else if ($this->planRecordData['from_user_type'] == 1) {
							// 员工分享
							$planStaffId = $this->planRecordData['from_user'];
						}
						if ($planStaffId) {
							$hasMap = array(
								'plan_id' => $guestPlanData['id'],
								'original_user_type' => 1,
								'original_user' => $planStaffId,
								'receive' => 1, // 已领取
							);
							$hasNum = (new OperateGuestPlanRecordModel)->where($hasMap)->count('id');
							$hasNum = $hasNum ? $hasNum : 0;
							$bindMap = array(
								'status' => 1,
								'from_user_type' => 1,
								'from_user' => $planStaffId,
								'plan_id' => $guestPlanData['id'],
								'merchantid' => $this->merchantid,
							);
							$maxNum = (new GuestPlanBindModel)->where($bindMap)->value('limit');
							$maxNum = $maxNum ? $maxNum : 0;
							if ($hasNum >= $maxNum) {
								return $this->ajaxFail('抱歉，本活动领取次数已用完');
							}
						}
					}
				} else if ($orderType == 8) {
					// 礼券活动附加信息
					// 1 pid
					// 2 分享会员id
					if (isset($post['extraData'])) {
						if (is_string($post['extraData'])) {
							$post['extraData'] = htmlspecialchars_decode($post['extraData']);
							$post['extraData'] = json_decode($post['extraData'], true);
						}
						$this->giftCouponData['pid'] = $post['extraData']['pid'];
						$this->giftCouponData['share_member_id'] = $post['extraData']['share_member_id'];

						$MemberModel = new MemberModel;
						$memberInfo = $MemberModel->getFinds(array('id' => $post['buyerId']), 'count');
						if (!$memberInfo) {
							return $this->ajaxFail('用户信息有误');
						}
						/*if ($memberInfo['count'] > 0) {
							return $this->ajaxFail('抱歉，当前礼券仅限新用户参与');
						}*/
						$map = array(
							'pid' => $this->giftCouponData['pid'],
							'share_member_id' => $this->giftCouponData['share_member_id'],
							'gift_coupon_issue_id' => $this->giftCouponData['gift_coupon_issue_id'],
							'merchantid' => $this->merchantid,
							'status' => 1
						);
						$giftCount = (new ShareholderGiftCouponRecordModel)->where($map)->count();
						$holderGiftCoupon = $this->giftCouponData['holderGiftCoupon'];
						if ($this->giftCouponData['pid']) {
							if (!$this->giftCouponData['share_member_id']) {
								return $this->ajaxFail('附加参数share_member_id不能为0');
							}
							// 客源分享
							if ($giftCount >= $holderGiftCoupon['share_num']) {
								return $this->ajaxFail('抱歉，礼券可参与次数已达上限');
							}
						} else {
							if ($this->giftCouponData['share_member_id']) {
								return $this->ajaxFail('附加参数share_member_id只能为0');
							}
							// 股东分享
							if ($holderGiftCoupon['num'] <= 0) {
								return $this->ajaxFail('抱歉，礼券可参与次数已达上限');
							}
						}
					}
				}
				$orderSaveStep = 9;
				/*优惠券现金券*/
				$PayorderModel = new PayorderModel;
				if (isset($post['couponData']) && $post['couponData']) {
					if (is_string($post['couponData'])) {
						$post['couponData'] = htmlspecialchars_decode($post['couponData']);
						$post['couponData'] = json_decode($post['couponData'], true);
					}
					if ($post['couponData']['id']) {
						$addData['member_coupon'] = $post['couponData']['id'];                      //优惠券id
						$addData['member_counpon_money'] = $post['couponData']['counpon_money'];  //优惠券减少金额
						$addData['receivable'] = $addData['receivable'] - $post['couponData']['counpon_money'];
						if ($addData['receivable'] < 0) {
							return $this->ajaxFail('订单优惠券金额错误');
						}
						/**
						 * @param $common = array('merchant_id'=>1,'store_id'=>1,'member_coupon'=>1,'member_counpon_money'=>1,'vip_id'=>1)
						 * @param $orderDetail = array(array('goods_id'=>1,'equity_type','card_id','consume_card','price','num','reduceprice','type'))
						 */
						$common = array(
							'merchant_id' => $this->merchantid,
							'store_id' => $this->storeid,
							'member_coupon' => $addData['member_coupon'],
							'member_counpon_money' => $addData['member_counpon_money'],
							'vip_id' => $post['buyerId']
						);
						$res = $PayorderModel->checkCoupon($common, $detailsData);
						if (!$res) {
							$error = $PayorderModel->error;
							$error = $error ? $error : '优惠券使用错误';
							return $this->ajaxFail($error);
						}
					}
				}
				$PresentData = array();
				// 下单赠送（服务|产品）。。。
				if (isset($post['presentData'])) {
					$post['presentData'] = htmlspecialchars_decode($post['presentData']);
					$presentData = json_decode($post['presentData'], true);
					$createPresentData = $this->createPresentData($presentData, $post['buyerId'], $post['cashierId']);
					if ($createPresentData['code'] == 0) {
						return $this->ajaxFail($createPresentData['msg']);
					}
					$PresentData = $createPresentData['data'];
//                    $PayorderModel->setPresentData($createPresentData['data']);
					$addData['present_value'] = $createPresentData['allMoney'];
					//关闭赠送额度
					// if ($addData['present_value'] > 0) {
					// 	$presentSet = $this->presentSet(0);
					// 	if ($presentSet['code'] == 0 && $addData['present_value'] > 0) {
					// 		return $this->ajaxFail('店铺未开启赠送');
					// 	} else {
					// 		if ($presentSet['data']['surplus'] - $addData['present_value'] < 0) {
					// 			return $this->ajaxFail('店铺赠送额度不足，请确认');
					// 		}
					// 	}
					// }
				}
				if (count($equityUseArr) > 0) {
					$buyerId = $post['buyerId'];
					if (!$post['buyerId']) {
						return $this->ajaxFail('抵扣、折扣权益使用时，会员信息不能为空');
					}
					$equityUsePass = $this->equityUsePass($equityUseArr, $buyerId, $addData['receivable']);
					if (!$equityUsePass) {
						$msg = $this->error;
						$msg = $msg ? $msg : '您的权益使用不正确，请重新选择';
						return $this->ajaxFail($msg);
					}
				}
				//  共享员工 分红
				if (isset($post['extraData'])) {
					if (is_string($post['extraData'])) {
						$post['extraData'] = htmlspecialchars_decode($post['extraData']);
						$post['extraData'] = json_decode($post['extraData'], true);
					}
					// 协助接待员工
					if (isset($post['extraData']['help_staff']) && $post['extraData']['help_staff']) {
						$addData['help_staff_ids'] = implode(',', $post['extraData']['help_staff']);
					}
					// 会员订单
					if ($post['buyerId']) {
						// 会员绑定员工id
						$bind_staff_id = $this->getMemberBindId($post['buyerId'], 1);
						if ($bind_staff_id !== false) {
							$addData['bind_staff_id'] = $bind_staff_id;
						} else {
							return $this->ajaxFail($this->error);
						}
						// 会员绑定股东id
						$bind_stock_holder_id = $this->getMemberBindId($post['buyerId'], 2);
						if ($bind_staff_id !== false) {
							$addData['bind_stock_holder_id'] = $bind_stock_holder_id;
						} else {
							return $this->ajaxFail($this->error);
						}
					}
				}
				if (count($detailsData) > 0) {
					if ($addData['type'] == 7) {
						// 获客活动时使用
						$readd = $PayorderModel
							->setPresentData($PresentData)
							->setPlanRecordData($this->planRecordData)
							->createOrder($addData, $detailsData);
					} else if ($addData['type'] == 8) {
						// 礼券时使用
						$readd = $PayorderModel
							->setPresentData($PresentData)
							->setGiftCouponRecordData($this->giftCouponData)
							->createOrder($addData, $detailsData);
					} else {
						$readd = $PayorderModel
							->setPresentData($PresentData)
							->createOrder($addData, $detailsData);
					}
					if (!$readd) {
						$msg = $PayorderModel->error;
						return $this->ajaxFail($msg);
					}
					$readd['equityUseArr'] = $equityUseArr;
					$readd['payCardArr'] = $payCardArr;
					if ($addData['type'] == 7) {
						$guestPlanData = $this->planRecordData['guestPlanData'];
						// pay_time 支付时间（1，领取时支付，2，使用前支付）
						if ($guestPlanData['pay_time'] == 2 || $addData['receivable'] == 0) {
							$readd['receivePlan'] = 1; // 直接领取
						} else {
							$readd['receivePlan'] = 0; // 支付
						}
						$readd['plan_record_id'] = $PayorderModel->getPlanRecordId();
					}
					if ($addData['type'] == 8) {
						$readd['gift_coupon_record_id'] = $PayorderModel->getGiftCouponRecordId();
						if($addData['receivable']==0){
							$readd['receiveGiftCoupon'] = 1; // 直接领取
						}else{
							$readd['receiveGiftCoupon'] = 0; // 直接领取
						}
						if($this->giftCouponData['pid']){
							$readd['can_share'] = false;
							$readd['has_share_num'] = 0;
						}else{
							$readd['can_share'] = true;
							$readd['has_share_num'] = 0;
						}
					}
					return $this->ajaxSuccess($readd);
				} else {
					return $this->ajaxFail('订单内容不能为空');
				}
			}
		} catch (\Exception $e) {
			$msg = $e->getMessage() . $e->getLine();
			$msg = $msg ? $msg . ' errorCode:000' . $orderSaveStep : '参数错误';
			return $this->ajaxFail($msg);
		}
	}

	private function orderedit($post, $id, $orderDetails, $orderEditVipId)
	{
		$MemberCardModel = new MemberCardModel;
		$orderType = $post['orderType'];
		//1：品项（产品服务开单），
		//2：品项（产品服务开单）
		//3：购买卡项，
		//4：充值 ，
		//5：组合卡购买,
		//6 直接收款。
		//7 获客策划。
		if (($orderType == 3 || $orderType == 4 || $orderType == 5) && !$post['buyerId']) {
			return $this->ajaxFail('当前业务，会员信息不能为空');
		}
		$updateData = array(
			'collection_time' => '',
			'cashier_id' => $post['cashierId'],
			'customer_remark' => $post['remark'],
		);
		$post['orderItems'] = htmlspecialchars_decode($post['orderItems']);
		$items = json_decode($post['orderItems'], true);
		/*2019-08-29 新增直接商城下单 店内核销*/
		if (isset($post['isVerify']) && $post['isVerify'] == 1) {
			$updateData['is_verify'] = 1;
			$updateData['shoper'] = $post['shoper'];
			$updateData['shoper_phone'] = $post['shoperPhone'];
			if ($this->interfaceType != 'xcx') {
				return $this->ajaxFail('只有商城直接购买服务等待核销');
			}
			if ($orderType != 1) {
				return $this->ajaxFail('直接购买服务等待核销，必须是品项订单');
			}
			if (count($items) > 1) {
				return $this->ajaxFail('直接购买服务等待核销服务只能是一个');
			}
		}
		// 将商城单据来源修改为 3
		if ($this->interfaceType == 'xcx') {
			$addData['source_type'] = 3;
		}
		// 地址信息
		if (isset($post['addressInfo'])) {
			$updateData['address_info'] = $post['addressInfo'];
		}
		// 配送类型
		if (isset($post['dispatchType'])) {
			$updateData['dispatch_type'] = $post['dispatchType'];
		}
		// 运费
		if (isset($post['dispatchFee'])) {
			$updateData['dispatch_fee'] = $post['dispatchFee'];
		} else {
			$updateData['dispatch_fee'] = 0;
		}
		// 用户信息
		if (isset($post['buyerId'])) {
			$updateData['vip_id'] = $post['buyerId'];
		} else {
			$updateData['vip_id'] = $orderEditVipId;
		}
		if (isset($post['shift_no'])) {
			$updateData['shift_no'] = $post['shift_no'];
		}
		/*2020-10-20 线下推广员获得奖励*/
		$promoterId = $this->getPromoterId($post['buyerId']);
		$detailsData = array();
		$receivable = 0;
		$where = array(
			'id' => $id
		);
		$average =0;
		$deduction = 0;
		$manually = 0;
		$dismoney = 0;
		$service_money = 0;
		$product_money = 0;
		$equityUseArr = array();
		/*06-29合伙人*/
		$promoterIdArr = array();
		/**/
		$itemIds = array();
		foreach ($orderDetails as $key => $val) {
			$itemIds[] = $val['itemId'];
		}
		$saveDetailsData = array();
		$payCardArr = array();
		// 权益类型 1 无权益 2折扣 3抵扣 4手动改价
		foreach ($items as $k => $v) {
			$present_money = 0;
			$recharge_money = 0;
			if ($v['itemType'] == 3) {
				$type = 3;
			} else if ($v['itemType'] == 4) {
				$type = 4;
				$present_money = $v['present_money'];
				$recharge_money = $v['recharge_money'];
			} else if ($v['itemType'] == 1) {
				$type = 1;
			} else if ($v['itemType'] == 5) {
				$type = 5;
			}
			if (!is_array($v['technicians'])) {
				$v['technicians'] = json_decode($v['technicians'], true);
			}
			$v['technicians'] = ($v['technicians'] ? $v['technicians'] : array());
			$technicians = '';
			foreach ($v['technicians'] as $vk => $vv) {
				$technicians .= $vv['id'] . ',';
			}
			$technicians = trim($technicians, ',');
			if (!is_array($v['salesmen'])) {
				$v['salesmen'] = json_decode($v['salesmen'], true);
			}
			$v['salesmen'] = ($v['salesmen'] ? $v['salesmen'] : array());
			$v['equityType'] = $v['equityType'] ? $v['equityType'] : 1;
			if (!$v['itemName']) {
				return $this->ajaxFail('订单详情名称不能为空');
			}
			if ($v['originPrice'] != intval($v['originPrice'])) {
				return $this->ajaxFail("订单内容{$v['itemName']}单价有误");
			}
			if ($v['realPay'] != intval($v['realPay'])) {
				return $this->ajaxFail("订单{$v['itemName']}小计有误");
			}
			$item = array(
				'type' => $v['itemType'],
				'name' => $v['itemName'],
				'img' => $v['itemImgId'],
				'price' => $v['originPrice'],
				'num' => $v['num'] * 1,
				'reduceprice' => $v['originPrice'] * $v['num'] - $v['realPay'],
				'equity_type' => $v['equityType'],
				'discount' => $v['discount'],
				'card_id' => $v['cardId'],
				'card_details_id' => $v['cardDetailsId'],
				'goods_id' => $v['goodsId'],
				'sku_id' => $v['skuId'],
				'sku_name' => $v['skuName'],
				'store_id' => $this->storeid,
				'merchant_id' => $this->merchantid,
				'staff_id' => $technicians,
				'technicians' => json_encode($v['technicians']),
				'sells_id' => implode(',', $v['salesmen']),
				'present_money' => $present_money,
				'recharge_money' => $recharge_money,
				'order_id' => $id
			);
			//*07-17新增耗卡*/
			if (isset($v['consumeCard']) && $v['consumeCard'] == 2) {
				if (!isset($v['consumeCardId'])) {
					return $this->ajaxFail("{$v['itemName']}使用耗卡,耗卡卡项必须设置");
				}
				if ($v['equityType'] == 3) {
					return $this->ajaxFail("{$v['itemName']}使用抵扣权益类型，不支持耗卡");
				}
				if ($v['cardId'] && $v['equityType'] == 2) {
					if ($v['cardId'] != $v['consumeCardId']) {
						return $this->ajaxFail("{$v['itemName']}使用充值卡折扣权益，耗卡卡项必须与充值卡一致");
					}
				}
				$item['consume_card'] = 2;
				$item['consume_card_id'] = $v['consumeCardId'];
			} else {
				$item['consume_card'] = 1;
				$item['consume_card_id'] = 0;
			}
			/*06-29新增合伙人*/
			if (isset($v['promoterId']) && $v['promoterId']) {
				$item['promoter_id'] = $v['promoterId'];
				$promoterIdArr[] = $v['promoterId'];
			} else {
				$item['promoter_id'] = $promoterId;
			}
			// 新增下单限制
			// itemType    1 服务 2产品 3卡项 4充值  5 直接收款
			// orderType   1：品项2：品项3：购买卡项，4：充值 ，5：组合卡,6直接收款 7,获客策划活动
			// equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
			if (!$post['buyerId']) {
				if ($v['equityType'] == 2 || $v['equityType'] == 3) {
					return $this->ajaxFail("非会员订单，{$v['itemName']}不支持折扣及抵扣权益");
				}
			}
			/*// 未使用权益
			if ($v['equityType'] == 1) {
				if ($item['reduceprice'] != 0) {
					return $this->ajaxFail("订单内容未使用权益，{$v['itemName']}小计有误，请确认");
				}
			} else if ($v['equityType'] == 2) {
				$reduceprice = round($v['originPrice'] * $v['num'] * (10 - $v['discount']) / 10, 0);
				if (abs(($item['reduceprice'] - $reduceprice)) > 1) {
					return $this->ajaxFail("订单内容使用折扣，{$v['itemName']}小计有误，请确认");
				}
			} else if ($v['equityType'] == 3) {
				$reduceprice = $v['originPrice'] * $v['num'];
				if ($item['reduceprice'] != $reduceprice) {
					return $this->ajaxFail("订单内容使用抵扣，{$v['itemName']}小计有误，请确认");
				}
			}
			if ($v['itemType'] == 1) {
				// 服务
				if ($orderType == 5) {
					//  购买组合卡
					if ($v['equityType'] != 1) {
						return $this->ajaxFail('组合卡业务，订单内容不能使用权益，请确认');
					}
				} else {
					if ($orderType != 1 && $orderType != 2 && $orderType != 5) {
						return $this->ajaxFail('订单类型与订单内容不匹配');
					}
				}
			} else if ($v['itemType'] == 2) {
				// 2产品
				if ($orderType == 5) {
					return $this->ajaxFail('组合卡业务，订单内容不能包含产品');
				} else {
					if ($v['equityType'] == 3) {
						return $this->ajaxFail("{$v['itemName']}产品暂不支持抵扣权益");
					}
					if ($v['equityType'] == 2) {
						if ($v['cardId']) {
							return $this->ajaxFail("{$v['itemName']}产品暂不支持卡项折扣权益");
						}
					}
				}
			} else if ($v['itemType'] == 3) {
				// 3卡项
				if ($orderType != 3) {
					return $this->ajaxFail("订单不为购买卡项订单，但内容包含卡项{$v['itemName']}，下单失败，请确认");
				}
				if ($v['equityType'] == 3 || $v['equityType'] == 2) {
					return $this->ajaxFail('办卡不支持折扣及抵扣，请确认');
				}
			} else if ($v['itemType'] == 4) {
				// 4充值
				if ($orderType != 4) {
					return $this->ajaxFail("订单不为充值订单，但内容包含充值{$v['itemName']}，下单失败，请确认");
				}
				if ($v['equityType'] == 3 || $v['equityType'] == 2) {
					return $this->ajaxFail('充值不支持折扣及抵扣，请确认');
				}
			} else if ($v['itemType'] == 5) {
				// 5 直接收款
				if ($orderType != 6) {
					return $this->ajaxFail("订单不为直接收款订单，但内容包含直接收款{$v['itemName']}，下单失败，请确认");
				}
				if ($v['equityType'] != 1) {
					return $this->ajaxFail('直接收款不支持权益使用，请确认');
				}
			}else if($v['itemType'] == 6){
				// 6 获客策划
				if ($orderType != 7) {
					return $this->ajaxFail("订单不为获客策划活动订单，但内容包含获客策划{$v['itemName']}，下单失败，请确认");
				}
				if ($v['equityType'] !=1) {
					return $this->ajaxFail('获客策划活动不支持权益使用，请确认');
				}
			}*/
			// 检测权益使用 、订单类型与订单内容类型匹配程度
			$checkEquityType = $this->checkEquityType($v, $item, $orderType, $post['buyerId']);
			if ($checkEquityType['code'] == 0) {
				return $checkEquityType;
			}
			$item['card_name'] = isset($v['cardName']) ? $v['cardName'] : "";
			if ($v['itemType'] == 1) {
				$service_money += $v['realPay'];
			} else if ($v['itemType'] == 2) {
				$product_money += $v['realPay'];
			}
			// switch ($v['equityType']) {
			// 	case 2:
			// 		$dismoney += $item['reduceprice'];
			// 		break;
			// 	case 3:
			// 		$deduction += $item['reduceprice'];
			// 		break;
			// 	case 4:
			// 		$manually += $item['reduceprice'];
			// 		break;
			// 	case 1:
			// 	default:
			// 		break;
			// }
			switch ($v['equityType']) {//累计
				case 2:
					$dismoney += $item['reduceprice'];//折扣权益优惠
					break;
				case 3:
					$deduction += $item['reduceprice'];//抵扣权益优惠。

					$mcwhere['id'] = $item['card_id'];
					// $mcfield = array('average_price');
					// var_dump($mcwhere);
					// exit();
					// $mcinfo = $MemberCardModel->getFind($mcwhere, $mcfield);
					// $average += intval($mcinfo['average_price']);//均价抵扣权益优惠。
					break;
				case 4:
					$manually += $item['reduceprice'];//手动改价优惠
					break;
				case 1:
				default:
					break;
			}
			$receivable += $v['realPay'];
			if (isset($v['itemId']) && $v['itemId']) {
				if (!in_array($v['itemId'], $itemIds)) {
//                    $msg = "单据内容{$v['itemName']}数据有误，请确认";
//                    return $this->ajaxFail($msg);
					$item['id'] = $v['itemId'];
					$itemIds = array_merge(array_diff($itemIds, array($v['itemId'])));
					$saveDetailsData[] = $item;
				} else {
					$item['id'] = $v['itemId'];
					$itemIds = array_merge(array_diff($itemIds, array($v['itemId'])));
					$saveDetailsData[] = $item;
				}
			} else {
				$detailsData[] = $item;
			}
			// 兼容
			if (!isset($v['consumeCard'])) {
				if (($v['equityType'] == 2 || $v['equityType'] == 3) && $v['stage'] == 1 && $v['cardId'] != 0) {
					$equityUseArr[] = array(
						'price' => $v['originPrice'],
						'num' => $v['num'],
						'realPay' => $v['realPay'],
						'equityType' => $v['equityType'],
						'cardId' => $v['cardId'],
						'cardDetailsId' => $v['cardDetailsId'],
						'cardName' => $item['card_name']
					);
				}
			} else {
				if ($v['stage'] == 1) {
					if ($item['equity_type'] == 3) {
						// 抵扣权益
						$equityUseArr[] = array(
							'price' => $v['originPrice'],
							'num' => $v['num'],
							'realPay' => $v['realPay'],
							'equityType' => $v['equityType'],
							'cardId' => $v['cardId'],
							'cardDetailsId' => $v['cardDetailsId'],
							'cardName' => $item['card_name']
						);
					} else {
						if ($v['consumeCard'] == 2) {
							// 耗卡
							$equityUseArr[] = array(
								'price' => $v['originPrice'],
								'num' => $v['num'],
								'realPay' => $v['realPay'],
								'equityType' => 5,
								'cardId' => $v['consumeCardId'],
								'cardDetailsId' => $v['cardDetailsId'],
								'cardName' => $item['card_name']
							);
							$payCardArr[] = array(
								'money' => $v['realPay'],
								'cardId' => $v['consumeCardId'],
							);
						}
					}
				}
			}
		}
		if (count($promoterIdArr) > 0) {
			// 有合伙人
			$newPromoterIdArr = array_unique($promoterIdArr);
			$newPromoterIdArr = array_merge($newPromoterIdArr);
			$PromotersModel = new PromotersModel;
			$promotersMap = array(
				'a.merchantid' => $this->merchantid,
				'a.id' => array('in', $newPromoterIdArr)
			);
			$promotersCount = $PromotersModel->getCount($promotersMap);
			if ($promotersCount != count($newPromoterIdArr)) {
				return $this->ajaxFail('合伙人信息有误');
			}
		}
		if (isset($post['dispatchType']) && $post['dispatchType'] == 2) {
			$receivable += $updateData['dispatch_fee'];
		}
		$updateData['average'] = $average;                 // 卡项均价抵扣总数 用于计算人均
		$updateData['receivable'] = $receivable;
		$updateData['deduction'] = $deduction;
		$updateData['manually'] = $manually;
		$updateData['dismoney'] = $dismoney;
		$updateData['service_money'] = $service_money;
		$updateData['product_money'] = $product_money;
		if ($orderType == 5) {
			if (count($equityUseArr) > 0) {
				return $this->ajaxFail('组合卡购买开单不能使用权益，请确认');
			}
			// 组合卡购买
			if (isset($post['extraData'])) {
				if (is_string($post['extraData'])) {
					$post['extraData'] = htmlspecialchars_decode($post['extraData']);
					$post['extraData'] = json_decode($post['extraData'], true);
				}
				//  单据额外信息
				$indate = $post['extraData']['indate'];                      // 永久传0，其他传 2019-08-01
				if ($indate != '0') {
					$indate = strtotime($indate . ' 23:59:59');
				}
				$comboCardMoney = $post['extraData']['comboCardMoney'];     // 组合卡最终金额。。。
				$extra = array(
					'indate' => $indate,
					'comboCardMoney' => $comboCardMoney
				);
				$updateData['extra'] = json_encode($extra);
				// 重置金额数据
				$updateData['receivable'] = $comboCardMoney;
				$updateData['manually'] = $receivable - $comboCardMoney;       // 总计算价减最终价
			} else {
				return $this->ajaxFail('组合卡购买开单时，有效期及最终金额不能为空');
			}
		} else if ($orderType == 7) {
			return $this->ajaxFail('活动单据不支持单据修改');
		} else if ($orderType == 8) {
			return $this->ajaxFail('礼券单据不支持单据修改');
		}
		$PayorderModel = new PayorderModel;
		if (isset($post['couponData']) && $post['couponData']) {
			if (is_string($post['couponData'])) {
				$post['couponData'] = htmlspecialchars_decode($post['couponData']);
				$post['couponData'] = json_decode($post['couponData'], true);
			}
			if ($post['couponData']['id']) {
				$updateData['member_coupon'] = $post['couponData']['id'];                      //优惠券id
				$updateData['member_counpon_money'] = $post['couponData']['counpon_money'];  //优惠券减少金额
				$updateData['receivable'] = $updateData['receivable'] - $post['couponData']['counpon_money'];
				if ($updateData['receivable'] < 0) {
					return $this->ajaxFail('订单优惠券金额错误');
				}
				/**
				 * @param $common = array('merchant_id'=>1,'store_id'=>1,'member_coupon'=>1,'member_counpon_money'=>1,'vip_id'=>1)
				 * @param $orderDetail = array(array('goods_id'=>1,'equity_type','card_id','consume_card','price','num','reduceprice','type'))
				 */
				$common = array(
					'merchant_id' => $this->merchantid,
					'store_id' => $this->storeid,
					'member_coupon' => $updateData['member_coupon'],
					'member_counpon_money' => $updateData['member_counpon_money'],
					'vip_id' => $updateData['vip_id']
				);
				$checkCouponDetailsData = $saveDetailsData;
				foreach ($detailsData as $k => $v) {
					$checkCouponDetailsData[] = $v;
				}
				$res = $PayorderModel->checkCoupon($common, $checkCouponDetailsData);
				if (!$res) {
					$error = $this->error;
					$error = $error ? $error : '优惠券使用错误';
					return $this->ajaxFail($error);
				}
			} else {
				$updateData['member_coupon'] = 0;         //优惠券id
				$updateData['member_counpon_money'] = 0;  //优惠券减少金额
			}
		} else {
			$updateData['member_coupon'] = 0;          //优惠券id
			$updateData['member_counpon_money'] = 0;  //优惠券减少金额
		}
		$delwhere = array(
			'order_id' => $id,
			'id' => array('in', $itemIds)
		);
		$PresentData = array();
		// 下单赠送（服务|产品）。。。
		if (isset($post['presentData'])) {
			$post['presentData'] = htmlspecialchars_decode($post['presentData']);
			$presentData = json_decode($post['presentData'], true);
			$createPresentData = $this->createPresentData($presentData, $post['buyerId'], $post['cashierId']);
			if ($createPresentData['code'] == 0) {
				return $this->ajaxFail($createPresentData['msg']);
			}
			$PresentData = $createPresentData['data'];
			$updateData['present_value'] = $createPresentData['allMoney'];
			//关闭赠送额度
			// if ($updateData['present_value'] > 0) {
			// 	$presentSet = $this->presentSet($id);
			// 	if ($presentSet['code'] == 0 && $updateData['present_value'] > 0) {
			// 		return $this->ajaxFail('店铺未开启赠送');
			// 	} else {
			// 		if ($presentSet['data']['surplus'] - $updateData['present_value'] < 0) {
			// 			return $this->ajaxFail('店铺赠送额度不足，请确认');
			// 		}
			// 	}
			// }
		}
		if (count($equityUseArr) > 0) {
			$buyerId = $post['buyerId'];
			$equityUsePass = $this->equityUsePass($equityUseArr, $buyerId, $updateData['receivable']);
			if (!$equityUsePass) {
				$msg = $this->error;
				$msg = $msg ? $msg : '您的权益使用不正确，请重新选择';
				return $this->ajaxFail($msg);
			}
		}
		//  共享员工 分红
		if (isset($post['extraData'])) {
			if (is_string($post['extraData'])) {
				$post['extraData'] = htmlspecialchars_decode($post['extraData']);
				$post['extraData'] = json_decode($post['extraData'], true);
			}
			// 协助接待员工
			if (isset($post['extraData']['help_staff']) && $post['extraData']['help_staff']) {
				$updateData['help_staff_ids'] = implode(',', $post['extraData']['help_staff']);
			}
			// 会员订单
			if ($post['buyerId']) {
				// 会员绑定员工id
				$bind_staff_id = $this->getMemberBindId($post['buyerId'], 1);
				if ($bind_staff_id !== false) {
					$updateData['bind_staff_id'] = $bind_staff_id;
				} else {
					return $this->ajaxFail($this->error);
				}
				// 会员绑定股东id
				$bind_stock_holder_id = $this->getMemberBindId($post['buyerId'], 2);
				if ($bind_staff_id !== false) {
					$updateData['bind_stock_holder_id'] = $bind_stock_holder_id;
				} else {
					return $this->ajaxFail($this->error);
				}
			}
		}
		if (count($detailsData) > 0 || count($saveDetailsData) > 0) {
			$readd = $PayorderModel
				->setPresentData($PresentData)
				->editOrder($where, $updateData, $delwhere, $detailsData, $saveDetailsData);
			if (!$readd) {
				$msg = $PayorderModel->error;
				return $this->ajaxFail($msg);
			}
			$d = array('id' => $id, 'orderNo' => $post['orderNo']);
			$d['payCardArr'] = $payCardArr;
			return $this->ajaxSuccess($d);
		} else {
			return $this->ajaxFail('订单内容不能为空');
		}
	}

	/**
	 * 检测权益使用、 订单类型与订单内容匹配  处理活动相关限制
	 * @param $v
	 * @param $item
	 * @param $orderType
	 * @return array
	 */
	private function checkEquityType($v, $item, $orderType, $memberId)
	{
		// 未使用权益
		if ($v['equityType'] == 1) {
			if ($item['reduceprice'] != 0) {
				return $this->ajaxFail("订单内容未使用权益，{$v['itemName']}小计有误，请确认");
			}
		} else if ($v['equityType'] == 2) {
			$reduceprice = round($v['originPrice'] * $v['num'] * (10 - $v['discount']) / 10, 0);
			if (abs(($item['reduceprice'] - $reduceprice)) > 1) {
				return $this->ajaxFail("订单内容使用折扣，{$v['itemName']}小计有误，请确认");
			}
		} else if ($v['equityType'] == 3) {
			$reduceprice = $v['originPrice'] * $v['num'];
			if ($item['reduceprice'] != $reduceprice) {
				return $this->ajaxFail("订单内容使用抵扣，{$v['itemName']}小计有误，请确认");
			}
		}
		if ($v['itemType'] == 1) {
			// 服务
			if ($orderType == 5) {
				//  购买组合卡
				if ($v['equityType'] != 1) {
					return $this->ajaxFail('组合卡业务，订单内容不能使用权益，请确认');
				}
			} else {
				if ($orderType != 1 && $orderType != 2 && $orderType != 5) {
					return $this->ajaxFail('订单类型与订单内容不匹配');
				}
			}
		} else if ($v['itemType'] == 2) {
			// 2产品
			if ($orderType == 5) {
				return $this->ajaxFail('组合卡业务，订单内容不能包含产品');
			} else {
//				if ($v['equityType'] == 3) {
//					return $this->ajaxFail("{$v['itemName']}产品暂不支持抵扣权益");
//				}
				if ($v['equityType'] == 2) {
					if ($v['cardId']) {
						return $this->ajaxFail("{$v['itemName']}产品暂不支持卡项折扣权益");
					}
				}
			}
		} else if ($v['itemType'] == 3) {
			// 3卡项
			if ($orderType != 3) {
				return $this->ajaxFail("订单不为购买卡项订单，但内容包含卡项{$v['itemName']}，下单失败，请确认");
			}
			if ($v['equityType'] == 3 || $v['equityType'] == 2) {
				return $this->ajaxFail('办卡不支持折扣及抵扣，请确认');
			}
			// 检测卡项是否是最新的
			$CardModel = new CardModel;
			$cardid = $item['goods_id'];
			$map = array('id' => $cardid, 'merchantid' => $this->merchantid, 'isdel' => 1, 'isnew' => 1);
			$CardData = $CardModel->getCardData($map, array());
			if (!$CardData) {
				return $this->ajaxFail('错误的卡项，请刷新卡项，重新选择');
			}
			$cardinfo = $CardData['cardinfo'];
			$cardDetails = $CardData['cardDetails'];
			if (!$cardinfo || !$cardDetails) {
				return $this->ajaxFail('当前卡项内容有误');
			}
			if ($cardinfo['status'] != 1) {
				return $this->ajaxFail('当前卡项已下架');
			}
		} else if ($v['itemType'] == 4) {
			// 4充值
			if ($orderType != 4) {
				return $this->ajaxFail("订单不为充值订单，但内容包含充值{$v['itemName']}，下单失败，请确认");
			}
			if ($v['equityType'] == 3 || $v['equityType'] == 2) {
				return $this->ajaxFail('充值不支持折扣及抵扣，请确认');
			}
		} else if ($v['itemType'] == 5) {
			// 5 直接收款
			if ($orderType != 6) {
				return $this->ajaxFail("订单不为直接收款订单，但内容包含直接收款{$v['itemName']}，下单失败，请确认");
			}
			if ($v['equityType'] != 1) {
				return $this->ajaxFail('直接收款不支持权益使用，请确认');
			}
		} else if ($v['itemType'] == 6) {
			// 6 获客策划
			if ($orderType != 7) {
				return $this->ajaxFail("订单不为活动订单，但内容包含{$v['itemName']}，下单失败，请确认");
			}
			if ($v['equityType'] != 1) {
				return $this->ajaxFail('活动不支持权益使用，请确认');
			}
			if (!$memberId) {
				return $this->ajaxFail('必须是会员才能参与活动，请确认');
			}
			$dataMap = array(
				'map' => array(
					'id' => $item['goods_id'],
					'merchantid' => $this->merchantid
				),
				'field' => array(
					'*'
				)
			);
			$guestPlanData = (new GuestPlanModel)->getFindData($dataMap);
			// 检测活动是否可用
			if ($guestPlanData && isset($guestPlanData['id'])) {
				$endTime = strtotime($guestPlanData['end_time'] . ':59');
				$time = time();
				if ($endTime < $time) {
					return $this->ajaxFail('活动已过期');
				}
				if ($guestPlanData['effect_time'] == 1) {
					// 次日生效
					$startTime = strtotime(date('Y-m-d') . ' :00:00:00') + 86400;
					if ($endTime < $startTime) {
						return $this->ajaxFail('活动参与时间已结束');
					}
				}
				// 判断领取限制
				$map = array(
					'merchantid' => $this->merchantid,
					'plan_id' => $guestPlanData['id'],
					'receive' => 1,
					'memberid' => $memberId,
				);
				$num = (new OperateGuestPlanRecordModel)->getCount($map);
				if ($num >= $guestPlanData['limit']) {
					return $this->ajaxFail("抱歉，您的参与次数已达上限");
				}
				$this->planRecordData['plan_id'] = $guestPlanData['id'];
				$this->planRecordData['merchantid'] = $this->merchantid;
				$this->planRecordData['storeid'] = $this->storeid;
				$this->planRecordData['addtime'] = time();
				// 分享后可领取次数（最大奖励次数）
				$this->planRecordData['max_reward_num'] = $guestPlanData['share_num'];
				$this->planRecordData['reward_num'] = 0;
				$this->planRecordData['pay'] = 0;
				$this->planRecordData['memberid'] = $memberId;
				$this->planRecordData['guestPlanData'] = $guestPlanData;
			} else {
				return $this->ajaxFail('活动未找到，请确认');
			}
		} else if ($v['itemType'] == 7) {
			// 6 获客策划
			if ($orderType != 8) {
				return $this->ajaxFail("订单不为礼券订单，但内容包含{$v['itemName']}，请确认");
			}
			if ($v['equityType'] != 1) {
				return $this->ajaxFail('礼券不支持权益使用，请确认');
			}
			if (!$memberId) {
				return $this->ajaxFail('必须是会员才能领取礼券，请确认');
			}
			$where = array(
				'merchantid' => $this->merchantid,
				'status' => 1,
				'id' => $item['goods_id'],
			);
			$page['withKey'] = array(
				'giftCouponData' => function ($query) {
					$fields = array(
						'id',
						'status',
						'id as cardList',
						'id as guestPlanList',
					);
					$query->field($fields);
				},
			);
			$fields = array(
				'id',
				'max_num',
				'num',
				'share_num',
				'storeid',
				'status',
				'gift_coupon_id',
				'shareholder_id'
			);
			$holderGiftCouponData = (new ShareholderGiftCouponIssueModel)->getFind($where, $page, $fields);
			// 检测礼券是否可用
			if ($holderGiftCouponData && isset($holderGiftCouponData['id'])) {
				// 检测礼券状态
				if (!$holderGiftCouponData['gift_coupon_data']) {
					return $this->ajaxFail('当前礼券内容为空，请确认');
				}
				$giftCouponData = $holderGiftCouponData['gift_coupon_data'];
				if ($giftCouponData['status'] != 1) {
					return $this->ajaxFail('当前礼券已失效');
				}
				// 检测  卡项状态
				if (isset($giftCouponData['cardList']) && count($giftCouponData['cardList']) > 0) {
					$CardModel = new CardModel;
					foreach ($giftCouponData['cardList'] as $k => $v) {
						$cardId = $v['goods_id'];
						// 判断总部和店铺是否存在该卡项（未删除的）
						// 检测卡项是否是最新的
						$map = array('id' => $cardId, 'merchantid' => $this->merchantid, 'isdel' => 1, 'isnew' => 1);
						$CardData = $CardModel->where($map)->field('isdel')->find();
						if (!$CardData) {
							return $this->ajaxFail("卡项【{$v['goodsName']}】未找到");
						}
						if ($CardData['isdel'] != 1) {
							return $this->ajaxFail('当前卡项已被删除');
						}
					}
				}
				if (isset($giftCouponData['guestPlanList']) && count($giftCouponData['guestPlanList']) > 0) {
					$GuestPlanModel = new GuestPlanModel;
					foreach ($giftCouponData['guestPlanList'] as $k => $v) {
						$dataMap = array(
							'map' => array(
								'id' => $v['goods_id'],
								'merchantid' => $this->merchantid
							),
							'field' => array(
								'*'
							)
						);
						$guestPlanData = $GuestPlanModel->getFindData($dataMap);
						// 检测活动是否可用
						if ($guestPlanData && isset($guestPlanData['id'])) {
							$endTime = strtotime($guestPlanData['end_time'] . ':59');
							$time = time();
							if ($endTime < $time) {
								return $this->ajaxFail("【{$v}】活动已过期");
							}
							if ($guestPlanData['effect_time'] == 1) {
								// 次日生效
								$startTime = strtotime(date('Y-m-d') . ' :00:00:00') + 86400;
								if ($endTime < $startTime) {
									return $this->ajaxFail("【{$v}】活动参与时间已结束");
								}
							}
						} else {
							return $this->ajaxFail('活动未找到，请确认');
						}
					}
				}
				// 检测  获客活动状态

				$this->giftCouponData['gift_coupon_issue_id'] = $item['goods_id'];
				$this->giftCouponData['merchantid'] = $this->merchantid;
				$this->giftCouponData['storeid'] = $this->storeid;
				$this->giftCouponData['addtime'] = time();
				$this->giftCouponData['status'] = 0;

				$this->giftCouponData['memebr_id'] = $memberId;
				$this->giftCouponData['shareholder_id'] = $holderGiftCouponData['shareholder_id'];
				$this->giftCouponData['holderGiftCoupon'] = $holderGiftCouponData;
			} else {
				return $this->ajaxFail('礼券未找到，请确认');
			}
		}
		return $this->ajaxSuccess('订单类型及内容类型匹配&权益使用正确');
	}


	/*获取会员绑定的推广员id*/
	private function getPromoterId($vid)
	{
		try {
			$MemberModel = new MemberModel;
			$map = array(
				'id' => $vid,
				'merchantid' => $this->merchantid
			);
			$member = $MemberModel->getFinds($map, 'id,pid');
			return $member ? $member['pid'] : 0;
		} catch (Exception $e) {
			return 0;
		}
	}

	private function createPresentData($presentData, $uid, $adminId)
	{
		try {
			$data = array();
			$allPresentMoney = 0;
			foreach ($presentData as $k => $v) {
				$total = $v['originPrice'] * $v['num'];
				if ($v['itemType'] == 1) {
					if ($uid == 0 || $uid == '') {
						continue;
					}
					$indate = strtotime($v['indate'] . ' 23:59:59');
				} else {
					$indate = 0;
				}
				$v['skuId'] = isset($v['skuId']) ? (is_numeric($v['skuId']) ? $v['skuId'] : 0) : 0;
				$v['skuName'] = isset($v['skuName']) ? $v['skuName'] : "";
				$item = array(
					'goods_type' => $v['itemType'],
					'merchantid' => $this->merchantid,
					'storeid' => $this->storeid,
					'goods_id' => $v['goodsId'],
					'name' => $v['itemName'],
					'img' => $v['itemImgId'],
					'price' => $v['originPrice'],
					'num' => $v['num'] * 1,
					'sku_id' => $v['skuId'],
					'sku_name' => $v['skuName'],
					'total' => $total,
					'indate' => $indate,
					'uid' => $uid,
					'admin_id' => $adminId,
					'status' => 0,                    //状态：0，仅添加，1，已赠送，2，赠送并退回（用于退款）
				);
				$allPresentMoney += $total;
				$data[] = $item;
			}
			return array(
				'code' => 1,
				'allMoney' => $allPresentMoney,
				'data' => $data
			);
		} catch (Exception $e) {
			return array(
				'code' => 0,
				'msg' => $e->getMessage(),
			);
		}
	}

	private function presentSet($orderId = 0)
	{
		$PayorderModel = new PayorderModel;
		$merchantid = $this->merchantid;
		$storeid = $this->storeid;
		$set = $PayorderModel->getStorePresentAmount($merchantid, $storeid);
		if ($set === false) {
			return array('code' => 0, 'msg' => '店铺未开启赠送');
		} else {
			if ($set['status'] == 2) {
				return array('code' => 0, 'msg' => '店铺未开启赠送');
			}
			$occupy = $PayorderModel->getOccupyPresentAmount($merchantid, $storeid, $orderId);
			$money = $set['amount'] - $set['use'] - $occupy;
			$data = array('surplus' => $money, 'all' => $set['amount'], 'use' => $set['use'], 'occupy' => $occupy);
			return array('code' => 1, 'data' => $data);
		}
	}

	public function getPresentSet()
	{
		$orderId = input('post.orderId');
		$orderId = $orderId ? $orderId : 0;
		$data = $this->presentSet($orderId);
		if ($data['code'] == 1) {
			return $this->ajaxSuccess($data['data']);
		} else {
			$data = array('surplus' => 0, 'all' => 0, 'use' => 0, 'occupy' => 0);
			return array('code' => 0, 'msg' => '店铺未开启赠送', 'data' => $data);
		}
	}

	/*
	 * 取单列表接口
	 * */
	public function fetchOrder()
	{
		$post = input('post.');

		$shift_no = $post['shift_no'];
		$type = $post['type'];
		$where = array(
			'store_id' => $this->storeid,
			'merchant_id' => $this->merchantid,
			'state' => 1,
			'page' => $post['page'],
			'limit' => $post['limit']
		);
		if ($type == 1) {
			$where['type'] = array('in', '1,2'); // 品项取单
			if ($shift_no) {
				$where['shift_no'] = $shift_no; // 班次单号
			}
		} else {
			return $this->ajaxFail('参数错误，或者其他类型未开放');
		}
		if (isset($post['keyword']) && $post['keyword']) {
			$post['keyword'] = trim($post['keyword'], ' ');
			if ($post['keyword']) {
				$where['_string'] = "order_number = '{$post['keyword']}'";
				$memberWhere = array(
					'phone|member_number|member_name|remarks_name' => array('LIKE', '%' . $post['keyword'] . '%'),
					'merchantid' => $this->merchantid
				);
				if (strlen($post['keyword']) == 14) {
					// 会员码
					$MembercodeModel = new MembercodeModel;
					$time = time() - 60;
					$map = array(
						'merchantid' => $this->merchantid,
						'code' => $post['keyword'],
						'addtime' => array('gt', $time)
					);
					$one = $MembercodeModel->getFind($map);
					if ($one && isset($one['uid'])) {
						$post['uid'] = array($one['uid']);
					}
				}
				if (!isset($post['uid'])) {
					// 实体卡码
					$MemberentitycardModel = new MemberentitycardModel;
					$map = array(
						'card_voucher' => $post['keyword'],
						'merchantid' => $this->merchantid,
						'status' => 1
					);
					$one = $MemberentitycardModel->getFind($map);
					if ($one && isset($one['uid'])) {
						$post['uid'] = array($one['uid']);
					}
				}
				if (!isset($post['uid'])) {
					$MemberModel = new MemberModel;
					$field = array(
						'id'
					);
					$data = $MemberModel->getMemberSearch($memberWhere, $field, array());
					if (is_object($data)) {
						$data = $data->toArray();
					}
					if (count($data) > 0) {
						$data = array_column($data, 'id');
						$post['uid'] = $data;
					}
				}
				if (isset($post['uid']) && $post['uid']) {
					$vipIds = implode($post['uid'], ',');
					$where['_string'] = "((order_number = '{$post['keyword']}') OR ( vip_id in ({$vipIds})))";
				}
			}
		}
		$startTime = 0;
		$endTime = 0;
		if (isset($post['startTime']) && $post['startTime']) {
			$startTime = $post['startTime'];
		}
		if (isset($post['endTime']) && $post['endTime']) {
			$endTime = $post['endTime'];
		}
		if ($startTime > 0 && $endTime > 0) {
			$where['order_time'] = array('between', array($startTime, $endTime));
		}
		$PayorderModel = new PayorderModel;
		$field = array(
			'order_number',
			'order_time',
			'vip_id',
			'net_receipts',
			'receivable',
			'state',
			'id'
		);
		$append = array(
			'orderItems', 'buyer'
		);
		$data = $PayorderModel->getOrderListByPage($where, $field, $append);
		if (!$data) {
			$msg = $PayorderModel->error;
			return $this->ajaxFail($msg);
		}
		return $this->ajaxTable($data);
	}

	private function equityUsePass($equityUseArr, $buyerId, $receivable)
	{
		try {
			// 检测权益的使用
			// 1.处理权益使用。
			$newEquityArr = array();
			foreach ($equityUseArr as $key => $value) {
				if ($value['equityType'] == 3) {
					$str = $value['equityType'] . '&' . $value['cardId'] . '&' . $value['cardDetailsId'];
					if (!isset($newEquityArr[$str])) {
						$newEquityArr[$str] = $value;
					} else {
						$newEquityArr[$str]['num'] += $value['num'];
						$newEquityArr[$str]['realPay'] += $value['realPay'];
					}
				} else {
					$str = $value['equityType'] . '&' . $value['cardId'] . '&' . 0;
					if (!isset($newEquityArr[$str])) {
						$newEquityArr[$str] = $value;
					} else {
						$newEquityArr[$str]['num'] += $value['num'];
						$newEquityArr[$str]['realPay'] += $value['realPay'];
						$newEquityArr[$str]['cardDetailsId'] = $newEquityArr[$str]['cardDetailsId'] . ',' . $value['cardDetailsId'];
					}
				}
			}
			// 查询数据库，并检测 次卡数量，充值卡余额 有效期 使用门店。
			$equityUsePass = true;
			$MemberCardModel = new MemberCardModel;
			$CardDetailModel = '';
			$storeId = $this->storeid;
			$time = time();
			$baseWhere = array(
				'merchantid' => $this->merchantid,
				'memberid' => $buyerId,
				'storeid'=>$storeId,
				// 'canusestore' => array(
				// 	array('eq', -1),
				// 	array('eq', $storeId),
				// 	array('like', $storeId . ',%'),
				// 	array('like', '%,' . $storeId . ',%'),
				// 	array('like', '%,' . $storeId),
				// 	'OR'
				// ),
				// 'isactivate' => 1,
				'status' => 1,
				'indate' => array(
					array('eq', 0),
					array('gt', $time),
					'OR'
				)
			);
			$field = array('*');
			$merchantId = $this->merchantid;
			$CardDetailModel = $CardDetailModel ? $CardDetailModel : (new MemberCardDetailModel);
			$MemberModel = '';
			foreach ($newEquityArr as $key => $value) {
				if ($value['equityType'] == 3 && $value['cardId'] == 0) {
					$this->error = '会员默认账户，不支持抵扣权益，请检查';
					$equityUsePass = false;
					break;
				} else if ($value['equityType'] != 3 && $value['cardId'] == 0) {
					// 会员默认账户耗卡使用默认余额
					$MemberModel = $MemberModel ? $MemberModel : (new MemberModel);
					$where = array(
						'merchantid' => $this->merchantid,
						'id' => $buyerId
					);
					$fields = array(
						'id',
						'balance as canUseBalance'
					);
					$one = $MemberModel->getFinds($where, $fields);
					if (!$one) {
						$this->error = '会员信息未找到，请确认';
						$equityUsePass = false;
						break;
					} else if ($one['canUseBalance'] - $value['realPay'] < 0) {
						$this->error = '会员默认账户余额不足';
						$equityUsePass = false;
						break;
					}
					continue;
				}else if($value['cardId'] == -10){
					// 股本消费
					if(class_exists('\app\api\model\Shareholder')){
						$map = array(
							'merchant_id'=>$this->merchantid,
							'member_id'=>$buyerId,
							'status'=>1,
						);
						$shareholder = (new \app\api\model\Shareholder())->getCapitalMoneyData($map,2);
						if($shareholder){
							if($shareholder['residuebalance'] - $value['realPay'] < 0){
								$this->error = '股本账户余额不足';
								$equityUsePass = false;
							}
						}else{
							$this->error = '股本账户未找到，请确认';
							$equityUsePass = false;
						}
					}else{
						$this->error = '股本消费未开发完成，请确认';
						$equityUsePass = false;
					}
					continue;
				}
				$where = $baseWhere;
				$where['id'] = $value['cardId'];
				$one = $MemberCardModel->getFind($where, $field);
				if (!$one) {
					$this->error = '卡项不存在，或已失效';
					$equityUsePass = false;
					break;
				} else {
					if (isset($value['cardName']) && $value['cardName']) {
						$cardName = $value['cardName'];
					} else {
						$cardName = $one['card_name'];
					}
					// 检测活动权益使用门槛
					if (isset($one['plan_type']) && $one['plan_type'] == 1) {
						// 获客活动产生的权益
						$planMap = array(
							'id' => $one['plan_id'],
							'merchantid' => $this->merchantid
						);
						$min_total = (new GuestPlanModel)->where($planMap)->value('min_total');
						$min_total = $min_total ? $min_total : 0;
						if ($min_total > $receivable) {
							$min_total = bcdiv($min_total, 100, 2);
							$this->error = '订单金额不满足【' . $cardName . "】的使用门槛{$min_total}元";
							$equityUsePass = false;
							break;
						}
					}
					$cardName = $cardName ? ($cardName . '的权益使用有误，错误:') : '';
					if ($value['equityType'] == 3) {
						// 查询权益详情
						$itemWhere = array(
							'merchantid' => $merchantId,
							'memberid' => $buyerId,
							'membercard_id' => $value['cardId'],
							'id' => $value['cardDetailsId']
						);
						$cardDetailsItem = $CardDetailModel->getFind($itemWhere);
						if (!$cardDetailsItem) {
							$this->error = $cardName . '卡项详情不存在';
							$equityUsePass = false;
							break;
						}
						//抵扣权益
						if ($cardDetailsItem['isgive'] == 2 && $cardDetailsItem['card_type'] == 2) {
							// 非赠送 非次卡
							$this->error = $cardName . '抵扣权益不能使用充值卡的折扣权益';
							$equityUsePass = false;
							break;
						} else {
							// 判断次数是否足够
							if ($cardDetailsItem['isgive'] == 2 && $one['once_cardtype'] == 2) {
								// 非赠送无限次卡（无需判断）
							} else {
								if (($cardDetailsItem['num'] - $cardDetailsItem['usenum'] - $value['num']) < 0) {
									$this->error = $cardName . '抵扣次数不足';
									$equityUsePass = false;
									break;
								}
							}
						}
					} else {
						$cardDetailsIds = $value['cardDetailsId'];
						$cardDetailsIdArr = explode(',', $cardDetailsIds);
						foreach ($cardDetailsIdArr as $k => $v) {
							if ($v == 0) {
								unset($cardDetailsIdArr[$k]);
							}
						}
						$cardDetailsIdArr = array_values($cardDetailsIdArr);
						$cardDetailsIdArr = array_unique($cardDetailsIdArr);
						// 查询权益详情
						$itemWhere = array(
							'merchantid' => $merchantId,
							'memberid' => $buyerId,
							'membercard_id' => $value['cardId'],
							'id' => array('in', $cardDetailsIdArr),
							'isgive' => 2,                            // 必须是非赠送
							'card_type' => 2                          // 必须是充值卡
						);
						$cardDetailsItem = $CardDetailModel->getSelect($itemWhere, 'id');
						if (!$cardDetailsItem) {
							$this->error = $cardName . '卡项详情不存在';
							$equityUsePass = false;
							break;
						} else if (count($cardDetailsItem) != count($cardDetailsIdArr)) {
							$this->error = $cardName . '卡项详情有误';
							$equityUsePass = false;
							break;
						} else {
							// 折扣权益  充值卡 判断余额是否充足
							if ($one['totalbalance'] - $one['usebalance'] - $value['realPay'] < 0) {
								$this->error = $cardName . '充值卡余额不足';
								$equityUsePass = false;
								break;
							}
						}
					}
				}
			}
			return $equityUsePass;
		} catch (\Exception $e) {
			$this->error = $e->getMessage();
		}
		return false;
	}

	/*
	 * 获取订单内容（打印页）
	 * */
	public function getOrderDetail()
	{
		try {
			$post = input('post.');
			$orderNo = $post['orderNo'];
			if ((isset($post['bath']) && $post['bath'] == 1) || strpos($orderNo, ',') !== false) {
				return $this->bathGetOrderDetail();
			}
			if (strpos($orderNo, '_print') !== false) {
				$post['is_print'] = 1;
				$orderNo = str_replace('_print', '', $orderNo);
			} else {
				$post['is_print'] = 0;
			}
			$where = array(
				'store_id' => $this->storeid,
				'merchant_id' => $this->merchantid,
				'order_number' => $orderNo
			);
			$PayorderModel = new PayorderModel;
			$field = array(
				'order_number',
				'order_time',
				'vip_id',
				'net_receipts',
				'receivable',
				'state',
				'id',
				'deduction',
				'manually',
				'dismoney',
				'remarks',
				'cashier_id',
				'member_coupon',
				'member_counpon_money',
				'collection_time as payTime',
				'id as presentData',
				'type',
				'is_refund',
				'is_debt'
			);
			$append = array(
				'orderDetails', 'buyer', 'toBePay', 'cashierInfo'
			);
			$data = $PayorderModel->getOrderData($where, $field, $append);
			$repayment = isset($post['repayment']) ? $post['repayment'] : 0;
			if (!$data) {
				$msg = $PayorderModel->error;
				$msg = $msg ? $msg : '订单不存在';
				return $this->ajaxFail($msg);
			} else {
				if (isset($post['is_print']) && $post['is_print'] == 0) {
					if ($data['is_refund'] == 1) {
						return $this->ajaxFail('订单已退款');
					}
					if ($data['state'] != 1 && $data['state'] != 5) {
						if ($repayment == 1 && $data['state'] == 4) {

						} else {
							return $this->ajaxFail('订单已支付');
						}
					} else if ($data['state'] == 5) {
						return $this->ajaxFail('订单已取消');
					}
				}
			}
			$orderDetails = $data['orderDetails'];
			// 为了充值卡支付 循环订单内容，将充值卡支付卡项及金额提出来
			$equityUseArr = array();
			$sellCardType = 0;
			$vipWorth = $data['deduction'];
			$residueMoney = $data['toBePay'];
			foreach ($orderDetails as $k => $v) {
				// 折扣权益，且使用的是卡
				// if (isset($v['consumeCard']) && $v['consumeCard'] == 2) {
					// 耗卡
					if($data['type']<3){
					$equityUseArr[] = array(
						'realPay' => $v['price'] * $v['num'] - $v['reduceprice'],
						'equityType' => 2,
						'cardId' => $v['consumeCardId'],
						'cardDetailsId' => $v['card_details_id']
					);
					$vipWorth += ($v['price'] * $v['num'] - $v['reduceprice']);
					$residueMoney -= ($v['price'] * $v['num'] - $v['reduceprice']);
				}
				// } else {
					if ($v['equity_type'] == 2 && $v['card_id'] > 0) {
						$equityUseArr[] = array(
							'realPay' => $v['price'] * $v['num'] - $v['reduceprice'],
							'equityType' => $v['equity_type'],
							'cardId' => $v['card_id'],
							'cardDetailsId' => $v['card_details_id']
						);
						$vipWorth += ($v['price'] * $v['num'] - $v['reduceprice']);
					}
				// }
				if ($data['type'] == 3) {
					// 售卡
					$CardModel = new CardModel;
					$sellCardMap = array(
						'id' => $v['card_id'],
						'merchantid' => $this->merchantid
					);
					$field = array(
						'id', 'card_type'
					);
					$one = $CardModel->getFind($sellCardMap, $field);
					if ($one) {
						if (isset($one['card_type'])) {
							$sellCardType = $one['card_type'];
						}
					} else {
						$sellCardType = 0;
					}
				}
			}
			$data['residueMoney'] = $residueMoney;  // 剩余金额
			$data['sellCardType'] = $sellCardType;
			$data['vipWorth'] = $vipWorth;
			if ($vipWorth > 0 || $sellCardType == 1) {
				// 消耗价值大于0
				$needVipPassData = $this->needVipPass($vipWorth, $data['vip_id']);
				if ($needVipPassData['needVipPass'] == 1) {
					$data['needVipPass'] = 1;
				} else {
					$data['needVipPass'] = 0;
				}
				$data['smallMoney'] = $needVipPassData['minMoney'];
			} else {
				$data['needVipPass'] = 0;
				$data['smallMoney'] = 0;
			}
			$newEquityArr = array();
			$cost = $data['deduction'] + $data['dismoney'];
			if ($data['vip_id']) {
				$data['payCardInfo'] = $this->getPayCardInfo($data['vip_id'], $data['buyer'], $cost);
			} else {
				$data['payCardInfo'] = $this->getPayCardInfo($data['vip_id'], $data['buyer'], $cost);
			}
			$payCardInfo = array();
			if (count($equityUseArr) > 0) {
				// 处理payCardInfo数组
				foreach ($data['payCardInfo'] as $ks => $vs) {
					$payCardInfo[$vs['id']] = $vs;
				}
//                pre($payCardInfo,false);
				foreach ($equityUseArr as $key => $value) {
					$str = $value['cardId'];
					if (!isset($newEquityArr[$str])) {
						if (isset($payCardInfo[$value['cardId']])) {
							foreach ($payCardInfo[$value['cardId']] as $k => $v) {
								$value[$k] = $v;
							}
							if (isset($payCardInfo[$value['cardId']]['residuebalance'])) {
								$value['residuebalance'] = $payCardInfo[$value['cardId']]['residuebalance'];
							} else {
								$value['residuebalance'] = $payCardInfo[$value['cardId']]['totalbalance'] - $payCardInfo[$value['cardId']]['usebalance'];
							}
						}
						$newEquityArr[$str] = $value;
					} else {
						$newEquityArr[$str]['realPay'] += $value['realPay'];
					}
				}
				$newEquityArr = array_values($newEquityArr);
			}
			$data['requisiteCard'] = count($newEquityArr) > 0 ? $newEquityArr : array();
			if ($data['type'] == 5) {
				$data['sellCardType'] = 1; // 购买组合卡使用会员支付
			}
			return $this->ajaxSuccess($data, 'success' . json_encode($post));
		} catch (\Exception $e) {
			$this->error = $e->getMessage();
			return $this->ajaxFail($this->error);
		}
	}

	/**
	 * 还款批量获取订单内容
	 * @return array
	 */
	public function bathGetOrderDetail()
	{
		try {
			$post = input('post.');
			$orderNoArr = explode(',', $post['orderNo']);
			$PayorderModel = new PayorderModel;
			$bathData = array();
			$orderDataList = array();
			$vipId = 0;
			$buyer = array('id' => 0);
			$payMoney = 0;
			$bathSellCardType = 1;
			foreach ($orderNoArr as $key => $orderNo) {
				$where = array(
					'store_id' => $this->storeid,
					'merchant_id' => $this->merchantid,
					'order_number' => $orderNo,
					'is_debt' => 1,
				);
				$field = array(
					'order_number',
					'order_time',
					'vip_id',
					'net_receipts',
					'receivable',
					'state',
					'id',
					'deduction',
					'manually',
					'dismoney',
					'remarks',
					'cashier_id',
					'member_coupon',
					'member_counpon_money',
					'collection_time as payTime',
					'id as presentData',
					'type',
					'is_refund',
					'is_debt',
					'debt_value as debtMoney'
				);
				$append = array(
					'orderDetails', 'buyer', 'toBePay', 'cashierInfo'
				);
				$data = $PayorderModel->getOrderData($where, $field, $append);
				$repayment = isset($post['repayment']) ? $post['repayment'] : 0;
				if (!$data) {
					$msg = $PayorderModel->error;
					$msg = $msg ? $msg : '订单不存在';
					return $this->ajaxFail($msg);
				} else {
					if (isset($post['is_print']) && $post['is_print'] == 1) {
						if ($data['is_refund'] == 1) {
							return $this->ajaxFail('订单已退款');
						}
						if ($data['state'] != 1 && $data['state'] != 5) {
							if ($repayment == 1 && $data['state'] == 4) {

							} else {
								return $this->ajaxFail('订单已支付');
							}
						} else if ($data['state'] == 5) {
							return $this->ajaxFail('订单已取消');
						}
					}
				}
				$orderDetails = $data['orderDetails'];
				// 为了充值卡支付 循环订单内容，将充值卡支付卡项及金额提出来
				$equityUseArr = array();
				$sellCardType = 0;
				$vipWorth = $data['deduction'];
				$residueMoney = $data['toBePay'];
				foreach ($orderDetails as $k => $v) {
					// 折扣权益，且使用的是卡
					if ($data['type'] == 3) {
						// 售卡
						$CardModel = new CardModel;
						$sellCardMap = array(
							'id' => $v['card_id'],
							'merchantid' => $this->merchantid
						);
						$field = array(
							'id', 'card_type'
						);
						$one = $CardModel->getFind($sellCardMap, $field);
						if ($one) {
							if (isset($one['card_type'])) {
								$sellCardType = $one['card_type'];
							}
						} else {
							$sellCardType = 0;
						}
					}
				}
				$data['residueMoney'] = $residueMoney;  // 剩余金额
				$data['sellCardType'] = $sellCardType;
				$data['vipWorth'] = $vipWorth;
				$newEquityArr = array();
				$cost = $data['deduction'] + $data['dismoney'];
				if ($data['vip_id']) {
					$vipId = $data['vip_id'];
				}
				if ($data['buyer']) {
					$buyer = $data['buyer'];
				}
				$data['requisiteCard'] = count($newEquityArr) > 0 ? $newEquityArr : array();
				if ($data['type'] == 5) {
					$data['sellCardType'] = 1; // 购买组合卡使用会员支付
				}
				if ($data['sellCardType'] != 1) {
					$bathSellCardType = 0;
				}
				$orderDataList[] = $data;
				$payMoney += $data['debtMoney'];
			}
			$bathData['payCardInfo'] = $this->getPayCardInfo($vipId, $buyer, $cost);
			$bathData['buyer'] = $buyer;
			$bathData['orderList'] = $orderDataList;
			$bathData['payMoney'] = $payMoney;
			$bathData['order_number'] = $post['orderNo'];
			$bathData['sellCardType'] = $bathSellCardType; // 能否使用余额支付
			$vipWorth = $payMoney;
			$allbalance = 0;
			foreach ($bathData['payCardInfo'] as $k => $v) {
				$allbalance += $v['residuebalance'];
			}
			// 会员余额 不足以支付欠款 （余额禁选）；
			if ($allbalance < $vipWorth) {
				$bathData['sellCardType'] = 0;
				$bathSellCardType = 0;
			}
			if ($vipWorth > 0 || $bathSellCardType == 1) {
				// 消耗价值大于0
				$needVipPassData = $this->needVipPass($vipWorth, $data['vip_id']);
				if ($needVipPassData['needVipPass'] == 1) {
					$bathData['needVipPass'] = 1;
				} else {
					$bathData['needVipPass'] = 0;
				}
				$bathData['smallMoney'] = $needVipPassData['minMoney'];
			} else {
				$bathData['needVipPass'] = 0;
				$bathData['smallMoney'] = 0;
			}
			return $this->ajaxSuccess($bathData, 'success');
		} catch (\Exception $e) {
			$this->error = $e->getMessage();
			return $this->ajaxFail($this->error);
		}
	}

	/**
	 * 检测是否需要会员密码
	 * @param $money
	 * @param $vipId
	 */
	private function needVipPass($money, $vipId)
	{
		// 此处判断会员是否需要输入密码；
		$MemberpaymentsetModel = new MemberpaymentsetModel;
		$map = array(
			'uid' => $vipId
		);
		$set = $MemberpaymentsetModel->getFind($map);
		if (!$set) {
			// 全部密码支付  获取店铺设置
			$StoreglobalModel = new StoreglobalModel;
			$map = array(
				'map' => array(
					'merchantid' => $this->merchantid,
					'storeid' => 0
				)
			);
			$data = $StoreglobalModel->getFindData($map);
			if (!$data || count($data) == 0) {
				return array('needVipPass' => true, 'minMoney' => 0);
			} else {
				if ($data['open_pay'] == 2) {
					// 全部免密支付
					return array('needVipPass' => false, 'minMoney' => -1);
				} else {
					// 开启密码支付
					if ($data['open_small_pay'] == 2) {
						// 关闭小额免密支付
						return array('needVipPass' => true, 'minMoney' => -1);
					} else {
						//开启小额免密支付
						if ($data['price'] - $money < 0) {
							return array('needVipPass' => true, 'minMoney' => $set['price']);
						} else {
							return array('needVipPass' => false, 'minMoney' => $set['price']);
						}
					}
				}
			}
		} else if ($set['open_pay'] == 2) {
			// 全部免密支付
			return array('needVipPass' => false, 'minMoney' => -1);
		} else {
			// 开启密码支付
			if ($set['open_small_pay'] == 2) {
				// 关闭小额免密支付
				return array('needVipPass' => true, 'minMoney' => -1);
			} else {
				//开启小额免密支付
				if ($set['price'] - $money < 0) {
					return array('needVipPass' => true, 'minMoney' => $set['price']);
				} else {
					return array('needVipPass' => false, 'minMoney' => $set['price']);
				}
			}
		}
		return array('needVipPass' => true, 'minMoney' => 0);
	}

	private function getPayCardInfo($buyerId, $data, $cost)
	{
		if (!$buyerId || $data['id'] == 0) {
			return array();
		}
		$memberid = $buyerId;
		$MemberCardModel = new MemberCardModel;
		$storeid = $this->storeid;
		$time = time();
		$where = array(
			'memberid' => $memberid,
			'merchantid' => $this->merchantid,
			// 状态
			'status' => 1,
			// 激活
			// 'isactivate' => 1,
			// 有效期
			'indate' => array(
				array('eq', 0),
				array('gt', $time),
				'OR'
			),
			// 可用门店
			'canusestore' => array(
				array('eq', -1),
				array('eq', $storeid),
				array('LIKE', $storeid . ',%'),
				array('LIKE', '%,' . $storeid . ',%'),
				array('LIKE', '%,' . $storeid),
				'OR'
			),
			'cardtype' => 2
		);
		$field = array(
			'id',
			'cardid',
			'cardtype',
			'indate',
			'totalbalance',
			'usebalance',
			'capitalbalance',
			'usecapitalbalance',
			'presentbalance',
			'usepresentbalance',
			'card_name as memberCardName'
		);
		$append = array(
			'card_info'
		);
		$cardInfo = $MemberCardModel->getSearch($where, $field, $append);
		if ($cardInfo === false) {
			$error = $MemberCardModel->error;
			return $this->ajaxFail($error);
		}
		foreach ($cardInfo as $k => &$v) {
			if ($v['indate'] == '0' || $v['indate'] == '') {
				$v['indate'] = '永久有效';
			} else {
				$v['indate'] = date('Y-m-d', $v['indate']) . '前有效';
			}
			$v['residuebalance'] = $v['totalbalance'] - $v['usebalance'];
			$v['capitalbalance'] -= $v['usecapitalbalance'];
			$v['presentbalance'] -= $v['usepresentbalance'];
			unset($v['usecapitalbalance']);
			unset($v['usepresentbalance']);
		}
		$cardInfo[] = array(
			"id" => 0,
			"cardid" => 0,
			"cardtype" => 0,
			"indate" => "永久有效",
			"totalbalance" => $data['balance'],
			"usebalance" => 0,
			"capitalbalance" => $data['balance'],
			"presentbalance" => 0,
			"residuebalance" => $data['balance'],
			"card_info" => "默认账户"
		);
		if(class_exists('\app\api\model\Shareholder')){
			$map = array(
				'merchant_id'=>$this->merchantid,
				'member_id'=>$memberid,
				'status'=>1,
			);
			$shareholder = (new \app\api\model\Shareholder())->getCapitalMoneyData($map,2);
			if($shareholder){
				$cardInfo[] = $shareholder;
			}
		}
		return $cardInfo;
	}

	/*
	 * 取单获取订单详情
	 * */
	public function fetchOrderDetail()
	{
		$post = input('post.');
		$orderNo = $post['orderNo'];
		$where = array(
			'store_id' => $this->storeid,
			'merchant_id' => $this->merchantid,
			'order_number' => $orderNo
		);
		$PayorderModel = new PayorderModel;
		$field = array(
			'order_number',
			'order_time',
			'vip_id',
			'net_receipts',
			'receivable',
			'state',
			'id',
			'deduction',
			'manually',
			'dismoney',
			'remarks as remark',
			'customer_remark',
			'cashier_id',
			'member_coupon',
			'member_coupon',
			'member_counpon_money',
			'id as presentData',
			'help_staff_ids'
		);
		$append = array(
			'orderDetailsData', 'buyer', 'toBePay', 'cashierInfo'
		);
		$data = $PayorderModel->getOrderData($where, $field, $append);
		if (!$data) {
			$msg = $PayorderModel->error;
			$msg = $msg ? $msg : '订单不存在';
			return $this->ajaxFail($msg);
		} else if ($data['state'] != 1 && $data['state'] != 5) {
			return $this->ajaxFail('订单已支付');
		} else if ($data['state'] == 5) {
			return $this->ajaxFail('订单已取消');
		}
		$data['help_staff_ids'] = $data['help_staff_ids'] ? explode(',', $data['help_staff_ids']) : [];
		$orderDetails = $data['orderDetailsData'];
		// 为了充值卡支付 循环订单内容，将充值卡支付卡项及金额提出来
		$equityUseArr = array();
		foreach ($orderDetails as $k => $v) {
			// 折扣权益，且使用的是卡
			if (isset($v['consumeCard']) && $v['consumeCard'] == 2) {
				// 耗卡
				$equityUseArr[] = array(
					'realPay' => $v['price'] * $v['num'] - $v['reduceprice'],
					'equityType' => 2,
					'cardId' => $v['consumeCardId'],
					'cardDetailsId' => $v['card_details_id']
				);
			} else {
				if ($v['equity_type'] == 2 && $v['card_id'] > 0) {
					$equityUseArr[] = array(
						'realPay' => $v['price'] * $v['num'] - $v['reduceprice'],
						'equityType' => $v['equity_type'],
						'cardId' => $v['card_id'],
						'cardDetailsId' => $v['card_details_id']
					);
				}
			}
		}
		$newEquityArr = array();
		$cost = $data['deduction'] + $data['dismoney'];
		if ($data['vip_id']) {
			$data['payCardInfo'] = $this->getPayCardInfo($data['vip_id'], $data['buyer'], $cost);
		} else {
			$data['payCardInfo'] = $this->getPayCardInfo($data['vip_id'], $data['buyer'], $cost);
		}
		$payCardInfo = array();
		if (count($equityUseArr) > 0) {
			// 处理payCardInfo数组
			foreach ($data['payCardInfo'] as $k => $v) {
				$payCardInfo[$v['id'] . '_1'] = $v;
			}
			foreach ($equityUseArr as $key => $value) {
				$str = $value['cardId'];
				if (!isset($newEquityArr[$str])) {
					if (isset($payCardInfo[$value['cardId'] . '_1'])) {
						$value = array_merge($value, $payCardInfo[$value['cardId'] . '_1']);
					}
					$newEquityArr[$str] = $value;
				} else {
					$newEquityArr[$str]['realPay'] += $value['realPay'];
				}
			}
			$newEquityArr = array_values($newEquityArr);
		}
		$data['requisiteCard'] = count($newEquityArr) > 0 ? $newEquityArr : array();
		foreach ($data['orderDetailsData'] as $k => &$v) {
			$v['consumeCardName'] = '';
			if (isset($v['consumeCard']) && $v['consumeCard'] == 2) {
				foreach ($newEquityArr as $k2 => $v2) {
					if ($v2['id'] == $v['consumeCardId']) {
						$v['consumeCardName'] = $v2['card_info'];
						break;
					}
				}
			}
		}
		return $this->ajaxSuccess($data);
	}

	/*预约单开单*/
	public function bookingOrderDetail()
	{
		$post = input('post.');
		$orderNo = $post['orderNo'];
		$where = array(
			'storeid' => $this->storeid,
			'merchantid' => $this->merchantid,
			'pre_order' => $orderNo
		);
		$BookerModel = new BookerModel;
		$one = $BookerModel->getFind($where);
		if (!$one) {
			$msg = $BookerModel->error;
			$msg = $msg ? $msg : '预约单不存在';
			return $this->ajaxFail($msg);
		} else if ($one['paystate'] != 2 && $one['paytype'] != 2) {
			return $this->ajaxFail('预约单未支付');
		} else if ($one['status'] == 2) {
			return $this->ajaxFail('预约单已开单');
		} else if ($one['status'] == 3) {
			return $this->ajaxFail('预约单已取消');
		} else if ($one['status'] == 4) {
			return $this->ajaxFail('预约单已评价');
		} else if ($one['status'] != 1) {
			return $this->ajaxFail('预约单不正常');
		}
		$BookserverModel = new BookserverModel;
		$map = array(
			'bid' => $one['id']
		);
		$details = $BookserverModel->getAll($map);
		if (!$details) {
			return $this->ajaxFail('预约单内容为空，请确认');
		}
		$MemberModel = new MemberModel;
		$field = array(
			'id',
			'phone',
			'member_number',
			'member_name',
			'remarks_name',
			'count',
			'total',
			'last_time',
			'balance',
			'pic'
		);
		$where = array('id' => $one['uid']);
		$mdata = $MemberModel->getMemberFind($where, $field);
		if ($mdata && isset($mdata['last_time']) && $mdata['last_time']) {
			$mdata['lastTimes'] = date('Y-m-d H:i:s', $mdata['last_time']);
		} else if ($mdata) {
			$mdata['lastTimes'] = date('Y-m-d H:i:s');
		}
		// 预约会员信息
		$buyer = $mdata ? $mdata : array('id' => 0);
		// 定金
		$net_receipts = $one['remoney'];
		$toBePay = 0;
		$StaffModel = new StaffModel;
		$orderDetailsData = array();
		$StoserviceModel = new StoserviceModel;
		foreach ($details as $k => $v) {
			$v['price'] = $v['price'] ? $v['price'] : 0;
			$toBePay += $v['price'];
			$v['staffid'] = $v['staffid'] ? strval($v['staffid']) : '';
			if ($v['staffid']) {
				$staff_idArray = array($v['staffid']);
				$staff = $StaffModel->getFind(array('id' => intval($v['staffid'])), 'nickname');
				$nickname = $staff ? $staff['nickname'] : '';
				$technicians = array(
					array(
						'dot' => 1,
						'id' => $v['staffid'],
						'nickname' => $nickname
					)
				);
			} else {
				$staff_idArray = array();
				$technicians = array();
			}
			$service = $StoserviceModel->getFind(array('store_id' => $this->storeid, 'service_id' => $v['serverid']), 'material_id');
			if (!$service) {
				$imgid = 0;
			} else {
				$imgid = $service['material_id'];
				if ($imgid) {
					if (strpos($imgid, ',') !== false) {
						$imgarr = explode(',', $imgid);
						$imgid = $imgarr[0];
					}
				} else {
					$imgid = 0;
				}
			}
			$item = array(
				'cardName' => '',
				'card_details_id' => 0,
				'card_id' => 0,
				'discount' => '10.00',
				'equity_type' => 1,
				'name' => $v['sername'] ? $v['sername'] : '[unknow]',
				'num' => 1,
				'price' => $v['price'],
				'reduceprice' => 0,
				'sells_id' => [],
				'sellsname' => [],
				'sku_name' => $v['skuname'] ? $v['skuname'] : ($v['skuid'] ? '[unknow]' : ''),
				'smallTotal' => $v['price'],
				'staff_id' => $staff_idArray,
				'technicians' => $technicians,
				'type' => 1,
				'goodsId' => $v['serverid'],
				'skuId' => $v['skuid'],
				'itemImgId' => intval($imgid),
				'consumeCard' => 1,
				'consumeCardId' => 0
			);
			if (isset($v['promoter_id']) && $v['promoter_id']) {
				$item['promoterId'] = $v['promoter_id'];
			} else {
				$item['promoterId'] = 0;
			}
			$orderDetailsData[] = $item;
		}
		$data = array(
			'vip_id' => $one['uid'],
			'toBePay' => $toBePay,
			'orderDetailsData' => $orderDetailsData,
			'buyer' => $buyer,
			'net_receipts' => $net_receipts,
			'bookerid' => $one['id']
		);
		return $this->ajaxSuccess($data);
	}

	/*获取小票设置*/
	public function getReceiptSet()
	{
		$ReceiptModel = new ReceiptModel;
		$where = array(
			'merchantid' => $this->merchantid,
			'storeid' => $this->storeid,
			'status' => 1
		);
		$field = array(
			'*',
			'`set` as `setInfo`'
		);
		$set = $ReceiptModel->getFind($where, $field);
		if ($set) {
			return $this->ajaxSuccess($set);
		} else {
			$sql = $ReceiptModel->getLastSql();
			$msg = $ReceiptModel->error . '  ' . $sql;
			$msg = $msg ? $msg : '未找到小票设置';
			return $this->ajaxFail($msg);
		}
	}

	/*
	 * 获取商城待核销服务接口
	 * */
	public function getVerifyOrder()
	{
		$post = input('post.');
		$where = array(
			'store_id' => $this->storeid,
			'merchant_id' => $this->merchantid,
			'state' => 4,
			'type' => 1,
			'is_verify' => 1
		);
		if ($this->interfaceType == 'xcx') {
			$orderNo = $post['keyword'];
			$where['order_number'] = $orderNo;
		} else {
			$verifyTicket = $post['keyword'];
			$where['verify_ticket'] = $verifyTicket;
		}
		$PayorderModel = new PayorderModel;
		$field = array(
			'order_number',
			'order_time',
			'vip_id',
			'id',
			'shoper',
			'shoper_phone',
			'verify_ticket'
		);
		$append = array(
			'orderDetailsData', 'buyer'
		);
		$data = $PayorderModel->getOrderData($where, $field, $append);
		if (is_object($data)) {
			$data = $data->toArray();
		}
		if (!$data) {
			$msg = $PayorderModel->error;
			$msg = $msg ? $msg : '未找到核销单';
			return $this->ajaxFail($msg);
		}
		$MaterialModel = new MaterialModel;
		foreach ($data['orderDetailsData'] as $k => &$v) {
			$v['imgUrl'] = $MaterialModel->getFilePathByID($v['itemImgId']);
		}
		return $this->ajaxSuccess($data);
	}

	/*
	 * 确认核销
	 * */
	public function confirmVerify()
	{
		$post = input('post.');
		$PayorderModel = new PayorderModel;
		$orderNo = $post['orderNo'];
		$cashierId = $post['cashierId'];
		$w = array(
			'merchant_id' => $this->merchantid,
			'store_id' => $this->storeid,
			'order_number' => $orderNo
		);
		$append = array(
			'orderDetails'
		);
		$field = array(
			'id',
			'state',
			'stage',
			'is_refund as orderVerifyRefund',
			'is_verify',
			'vip_id'
		);
		$one = $PayorderModel->getFind($w, $field, $append);
		// return $this->ajaxSuccess($one);
		if (!$one) {
			return $this->ajaxFail('单据不存在，请确认');
		} else if ($one['state'] != 4) {
			return $this->ajaxFail('单据未付款，无法核销');
		} else if ($one['is_verify'] == 0) {
			return $this->ajaxFail('不是能核销的单据，请确认');
		} else if ($one['is_verify'] == 2) {
			return $this->ajaxFail('单据已核销，请确认');
		} else if ($one['orderVerifyRefund'] == 1) {
			return $this->ajaxFail('单据已退款，请确认');
		}
		$updateData = array(
			'is_verify' => 2,
			'time' => time(),             // 核销时间
			'cashier_id' => $cashierId
		);
		if (!is_array($post['technicians'])) {
			$post['technicians'] = json_decode(htmlspecialchars_decode($post['technicians']), true);
		}
		$post['technicians'] = ($post['technicians'] ? $post['technicians'] : array());
		$techArr = [];
		$techDetailArr = [];
		$helpStaffArr = [];
		foreach ($post['technicians'] as $vk => $vv) {
			if (isset($vv['help']) && $vv['help'] == 1) {
				$helpStaffArr[] = $vv['id'];
			} else {
				$techArr[] = $vv['id'];
				$techDetailArr[] = $vv;
			}
		}
		//  共享员工 分红
		// 会员订单
		if ($one['vip_id']) {
			// 会员绑定员工id
			$bind_staff_id = $this->getMemberBindId($one['vip_id'], 1);
			if ($bind_staff_id !== false) {
				$updateData['bind_staff_id'] = $bind_staff_id;
			} else {
				return $this->ajaxFail($this->error);
			}
			// 会员绑定股东id
			$bind_stock_holder_id = $this->getMemberBindId($one['vip_id'], 2);
			if ($bind_staff_id !== false) {
				$updateData['bind_stock_holder_id'] = $bind_stock_holder_id;
			} else {
				return $this->ajaxFail($this->error);
			}
		}
		if (count($helpStaffArr) > 0) {
			$updateData['help_staff_ids'] = implode(',', $helpStaffArr);
		}
		$saveDatas = array();
		foreach ($one['orderDetails'] as $k => $v) {
			$item = array(
				'id' => $v['itemId'],
				'staff_id' => implode(',', $techArr),
				'technicians' => json_encode($techDetailArr),
				'name' => $v['name'],
				'sku_name' => $v['sku_name']
			);
			$saveDatas[] = $item;
		}
		$where = array(
			'merchant_id' => $this->merchantid,
			'store_id' => $this->storeid,
			'order_number' => $orderNo,
			'id' => $one['id']
		);
		if (count($saveDatas) > 0) {
			$readd = $PayorderModel->verifyOrder($where, $updateData, $saveDatas);
			if (!$readd) {
				$msg = $PayorderModel->error;
				return $this->ajaxFail($msg);
			}
			$d = array('id' => $one['id'], 'orderNo' => $post['orderNo']);
			return $this->ajaxSuccess($d, '核销成功');
		} else {
			return $this->ajaxFail('单据错误');
		}
	}

	public function getOrderNum()
	{
		$uid = input('post.uid');
		$PayorderModel = new PayorderModel;
		$numArray = array(
			'waitPay' => 0,
			'waitTakeGoods' => 0,
			'waitVerify' => 0
		);
		if ($uid) {
			$waitPayMap = array(
				'merchant_id' => $this->merchantid,
				'store_id' => $this->storeid,
				'state' => 1,
				'is_refund' => 2,
				'vip_id' => $uid
			);
			$numArray['waitPay'] = $PayorderModel->getCount($waitPayMap);

			$waitTakeGoodsMap = array(
				'merchant_id' => $this->merchantid,
				'store_id' => $this->storeid,
				'state' => array('in', '2,3'),
				'is_refund' => 2,
				'vip_id' => $uid
			);
			$numArray['waitTakeGoods'] = $PayorderModel->getCount($waitTakeGoodsMap);

			$waitVerifyMap = array(
				'merchant_id' => $this->merchantid,
				'store_id' => $this->storeid,
				'state' => 4,
				'is_verify' => 1,
				'is_refund' => 2,
				'vip_id' => $uid
			);
			$numArray['waitVerify'] = $PayorderModel->getCount($waitVerifyMap);
		}
		return $this->ajaxSuccess($numArray);
	}

	/**
	 * 查询会员绑定的 员工id（$user_type==1）或者 股东id（$user_type==2）
	 * @param $memberId
	 * @param int $user_type
	 * @return bool|int|mixed
	 */
	public function getMemberBindId($memberId, $user_type = 1)
	{
		try {
			if (class_exists('\app\store\model\BonusBind')) {
				$BonusBindModel = new BonusBindModel;
				$map = array(
					'merchantid' => $this->merchantid,
					'memberid' => $memberId,
					'user_type' => $user_type,
					'type' => 1,// 类型 1：绑定 2解绑
				);
				$from_user = $BonusBindModel->where($map)->value('from_user');
				return $from_user ? $from_user : 0;
			}
			return 0;
		} catch (Exception $e) {
			$this->error = $e->getMessage();
			return false;
		}
	}
	    //修改订单时间
		public function EditOrdertime()
		{
		
			try {
				$post = input("post.");
				$Model = new payOrderModel;
				$nt=strtotime($post['cdate']);
			
				if (!isset($post['cdate'])) {
					return $this->ajaxFail('时间不能为空');
				}
				if (!isset($post['id'])) {
					return $this->ajaxFail('订单id不能为空');
				}
				$where['id'] = $post['id'];
				$Edit['order_time'] = $nt;
				$Edit['collection_time'] = $nt;
				$Edit['time'] = $nt;
				
				$Ed = $Model->updates($where, $Edit);

				if ($Ed) {
					return $this->ajaxSuccess("修改成功");
				} else {
					return $this->ajaxFail("失败");
				}
			} catch (Exception $e) {
				$this->error = $e->getMessage();
				return $this->ajaxFail("失败".$this->error);
			}
		}
}