<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-09-09
 * Time: 15:52
 */

namespace app\store\controller;

use PHPExcel_IOFactory;
use think\Db;
use think\Request;
use app\store\model\Goodsimport as GoodsimportModel;
use app\store\model\Classification as ServiceClassModel;
use app\store\model\Label as ServiceLabelModel;
use app\store\controller\Randombarcode as RandombarcodeCtrl;
use app\store\model\Service as ServiceModel;
use app\store\model\Sku as SkuModel;
use app\store\model\Skuattr as SkuAttrModel;
//use app\store\model\Skuattr;
use app\store\model\Skuval as SkuValModel;
use app\store\model\ProductLabelClass as ProductLabelClassModel;

use app\store\model\ProductSku as ProductSkuModel;
use app\store\model\Product as ProductModel;
use app\store\model\ProductSkuAttr as ProductSkuAttrModel;
use app\store\model\Member as MemberModel;
use app\store\model\Createstore as StoreModel;
use app\store\model\MemberCard as MemberCardModel;
use app\store\model\MemberCardDetail as MemberCardDetailModel;

//<!--[meiye_08_20]-->
class Goodsimport extends Controller
{

    public function service()
    {
        $this->outPut();
    }

    public function product()
    {

        $this->outPut();
    }

    // 获取导入列表
    public function getGoodsImportList()
    {
        $post = input('post.');
        $type = $post['type'];
        $page = $post['page'];
        $limit = $post['limit'];
        $userInfo = $this->userinfo;
        $where = array(
            'merchantid' => $userInfo['merchantid'],
            'type' => $type
        );
        $goodsImport = new GoodsimportModel;
        $filed = '*';
        $pageData = array(
            'page' => $page,
            'limit' => $limit
        );
        $data = $goodsImport->getList($where, $pageData, $filed);
        $count = $goodsImport->getCount($where);
        return $this->ajaxTable($data, $count);
    }

    // 删除导入数据
    public function delGoodsImport()
    {
        $post = input('post.');
        $type = $post['type'];
        $userInfo = $this->userinfo;
        $where = array(
            'id' => $post['id'],
            'merchantid' => $userInfo['merchantid'],
            'type' => $type
        );
        $goodsImport = new GoodsimportModel;
        $delMap = array(
            'map' => $where
        );
        $res = $goodsImport->delData($delMap);
        if ($res) {
            return $this->ajaxSuccess('删除成功');
        } else {
            return $this->ajaxFail('删除失败');
        }
    }

    // 保存添加数据
    public function saveGoodsImport()
    {
        try {
            $post = input('post.');
            $goodsImport = new GoodsimportModel;
            $post['data'] = json_decode(htmlspecialchars_decode($post['data']), true);
            $info = json_encode($post['data']);
            if (isset($post['length'])) {
                $length = count($post['data']);
                $length2 = $post['length'];
                if ($length != $length2) {
                    return array('code' => 3, 'msg' => '超出服务器最大上传数据限制。');
                }
            }
            $editData = array(
                'id' => $post['id'],
                'info' => $info
            );
            $res = $goodsImport->editData($editData);
            if ($res!==false) {
                return $this->ajaxSuccess('保存成功');
            } else {
                return $this->ajaxFail('保存失败');
            }
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $msg = $msg?$msg:'保存失败';
            return $this->ajaxFail($msg);
        }
    }

    protected $serviceClassArr = array();
    protected $serviceLabelArr = array();

    public function getServiceClass($merchantid)
    {
        $ServiceClassModel = new ServiceClassModel;
        $where = array(
            'merchant_id' => $merchantid,
            'status' => 1
        );
        $field = array(
            'id',
            'classification_name as name'
        );
        $data = $ServiceClassModel->getList($where, $field, array(), true);
        if ($data) {
            $this->serviceClassArr = $data;
        }
    }

    public function getServiceLabel($merchantid)
    {
        $ServiceLabelModel = new ServiceLabelModel;
        $where = array(
            'merchant_id' => $merchantid,
            'status' => 1
        );
        $field = array(
            'id',
            'label_name as name'
        );
        $data = $ServiceLabelModel->getList($where, $field);
        if ($data) {
            $this->serviceLabelArr = $data;
        }
    }

    // 非规格服务导入
    public function noSkuServiceImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 1
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $addCommon = array(
            'merchant_id' => $userInfo['merchantid'],
            'is_sku' => 2,
            'content' => '',
            'adjust_price_id' => 0,
            'is_sku_img' => 2,
            'status' => 1,
            'addtime' => time(),
            'price_tag' => ''
        );
        $this->getServiceClass($merchantid);
        $this->getServiceLabel($merchantid);
        $ServiceModel = new ServiceModel;
        $successNum = 0;
        $errorNum = 0;
        $RandombarcodeCtrl = new RandombarcodeCtrl;
        // 有条码的先添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if (!$v['bar_code']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            $item['service_name'] = $v['service_name'];
            if(!$v['service_name']){
                $error[] = '名称不能为空';
            }
//            // 服务条码
            if ($v['bar_code']) {
                $item['bar_code'] = $v['bar_code'];
            } else {
                $item['bar_code'] = $this->barCode(1);
            }
            if (!$item['bar_code']) {
                $error[] = '条码有误';
            }
            // 分类
            $item['classification_id'] = $this->classNameToClassId($v['classification_id'], $merchantid);
            if(!$v['classification_id']){
                $error[] = '分类不能为空';
            }
            // 标签
            // $item['label_id'] = $this->labelNameToClassId($v['label_id'], $merchantid);
            // if(!$v['label_id']){
            //     $error[] = '标签不能为空';
            // }
            // 售价
            if (is_numeric($v['price']) && $v['price'] > 0) {
                $item['price'] = intval($v['price'] * 100);
            } else {
                $item['price'] = 0;
                $error[] = '售价有误';
            }
            if (is_numeric($v['duration']) && $v['duration'] > 0) {
                $item['duration'] = 15 * (ceil($v['duration'] / 15));
            } else {
                $error[] = '服务时长有误';
            }
            //网店展示
            if ($v['shop_display'] == '是') {
                $item['shop_display'] = 1; // 网店展示 1展示 2不展示
            } else {
                $item['shop_display'] = 2; // 网店展示 1展示 2不展示
            }
            //上门服务
            if ($v['door_service'] == '是') {
                $item['door_service'] = 2; // 上门服务 2开启 1关闭
            } else {
                $item['door_service'] = 1; // 上门服务 2开启 1关闭
            }
            // 上门服务付定金
            $item['pre_door'] = 2;

            //上门支付 1百分比支付 2 固定金额支付
            if ($v['pre_door_mode'] == '百分比') {
                $item['pre_door_mode'] = 1; // 1百分比支付 2 固定金额支付
            } else {
                $item['pre_door_mode'] = 2; // 1百分比支付 2 固定金额支付
            }

            if (is_numeric($v['door_percentage']) && $v['door_percentage'] > 0 && $v['door_percentage'] <= 100) {
                $item['door_percentage'] = $v['door_percentage'] / 100;
            } else {
                $item['door_percentage'] = 0.2;
            }

            if (is_numeric($v['door_fixed_amount']) && $v['door_fixed_amount'] > 0 && $v['door_fixed_amount'] <= $item['price'] / 100) {
                $item['door_fixed_amount'] = intval($v['door_fixed_amount'] * 100);
            } else {
                $item['door_fixed_amount'] = $item['price'];
            }
            //预约到店
            if ($v['pro_shop'] == '到店付') {
                $item['pro_shop'] = 2; // 预约到店 1付全款 2到店付 3付定金
            } else {
                $item['pro_shop'] = 3; // 预约到店 1付全款 2到店付 3付定金
            }

            if ($v['pro_shop_mode'] == '固定金额') {
                $item['pro_shop_mode'] = 2; // 到店支付 1百分比支付  2固定金额支付
            } else {
                $item['pro_shop_mode'] = 1; // 到店支付 1百分比支付  2固定金额支付
            }
            // 到店百分比支付
            if (is_numeric($v['shop_percentage']) && $v['shop_percentage'] > 0 && $v['shop_percentage'] <= 100) {
                $item['shop_percentage'] = $v['shop_percentage'] / 100;
            } else {
                $item['shop_percentage'] = 0.2;
            }
            // 到店固定金额
            if (is_numeric($v['shop_fixed_amount']) && $v['shop_fixed_amount'] > 0 && $v['shop_fixed_amount'] <= $item['price'] / 100) {
                $item['shop_fixed_amount'] = intval($v['shop_fixed_amount'] * 100);
            } else {
                $item['shop_fixed_amount'] = $item['price'];
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['material_id'] = trim($material_id, ',');
            } else {
                $item['material_id'] = 0;
            }

            // 图片
            if (isset($v['videoArr']) && count($v['videoArr']) > 0) {
                $material_id = '';
                foreach ($v['videoArr'] as $k1 => $v1) {
                    $material_id = $v1['id'];
                }
                $item['video_id'] = $material_id;
            } else {
                $item['video_id'] = 0;
            }
            if (count($error) == 0) {
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'service_name' => $item['service_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ServiceModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '服务名称重复';
                }
                $one = $RandombarcodeCtrl->getServiceBarcode($item['bar_code'], $merchantid);
                if (!$one) {
                    $error[] = '服务条码重复';
                }
                if (count($error) == 0) {
                    $serviceAdd = $ServiceModel->add($item);
                    if ($serviceAdd) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ServiceModel->error ? $ServiceModel->error : '服务添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }
        // 无条码的后添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if ($v['bar_code']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            $item['service_name'] = $v['service_name'];
            if(!$v['service_name']){
                $error[] = '名称不能为空';
            }
//            // 服务条码
            if ($v['bar_code']) {
                $item['bar_code'] = $v['bar_code'];
            } else {
                $item['bar_code'] = $this->barCode(1);
            }
            if (!$item['bar_code']) {
                $error[] = '条码有误';
            }
            // 分类
            $item['classification_id'] = $this->classNameToClassId($v['classification_id'], $merchantid);
            if(!$v['classification_id']){
                $error[] = '分类不能为空';
            }
            // 标签
            // $item['label_id'] = $this->labelNameToClassId($v['label_id'], $merchantid);
            // if(!$v['label_id']){
            //     $error[] = '标签不能为空';
            // }
            // 售价
            if (is_numeric($v['price']) && $v['price'] > 0) {
                $item['price'] = intval($v['price'] * 100);
            } else {
                $item['price'] = 0;
                $error[] = '售价有误';
            }
            if (is_numeric($v['duration']) && $v['duration'] > 0) {
                $item['duration'] = 15 * (ceil($v['duration'] / 15));
            } else {
                $error[] = '服务时长有误';
            }
            //网店展示
            if ($v['shop_display'] == '是') {
                $item['shop_display'] = 1; // 网店展示 1展示 2不展示
            } else {
                $item['shop_display'] = 2; // 网店展示 1展示 2不展示
            }
            //上门服务
            if ($v['door_service'] == '是') {
                $item['door_service'] = 2; // 上门服务 2开启 1关闭
            } else {
                $item['door_service'] = 1; // 上门服务 2开启 1关闭
            }
            // 上门服务付定金
            $item['pre_door'] = 2;

            //上门支付 1百分比支付 2 固定金额支付
            if ($v['pre_door_mode'] == '百分比') {
                $item['pre_door_mode'] = 1; // 1百分比支付 2 固定金额支付
            } else {
                $item['pre_door_mode'] = 2; // 1百分比支付 2 固定金额支付
            }

            if (is_numeric($v['door_percentage']) && $v['door_percentage'] > 0 && $v['door_percentage'] <= 100) {
                $item['door_percentage'] = $v['door_percentage'] / 100;
            } else {
                $item['door_percentage'] = 0.2;
            }

            if (is_numeric($v['door_fixed_amount']) && $v['door_fixed_amount'] > 0 && $v['door_fixed_amount'] <= $item['price'] / 100) {
                $item['door_fixed_amount'] = intval($v['door_fixed_amount'] * 100);
            } else {
                $item['door_fixed_amount'] = $item['price'];
            }
            //预约到店
            if ($v['pro_shop'] == '到店付') {
                $item['pro_shop'] = 2; // 预约到店 1付全款 2到店付 3付定金
            } else {
                $item['pro_shop'] = 3; // 预约到店 1付全款 2到店付 3付定金
            }

            if ($v['pro_shop_mode'] == '固定金额') {
                $item['pro_shop_mode'] = 2; // 到店支付 1百分比支付  2固定金额支付
            } else {
                $item['pro_shop_mode'] = 1; // 到店支付 1百分比支付  2固定金额支付
            }
            // 到店百分比支付
            if (is_numeric($v['shop_percentage']) && $v['shop_percentage'] > 0 && $v['shop_percentage'] <= 100) {
                $item['shop_percentage'] = $v['shop_percentage'] / 100;
            } else {
                $item['shop_percentage'] = 0.2;
            }
            // 到店固定金额
            if (is_numeric($v['shop_fixed_amount']) && $v['shop_fixed_amount'] > 0 && $v['shop_fixed_amount'] <= $item['price'] / 100) {
                $item['shop_fixed_amount'] = intval($v['shop_fixed_amount'] * 100);
            } else {
                $item['shop_fixed_amount'] = $item['price'];
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['material_id'] = trim($material_id, ',');
            } else {
                $item['material_id'] = 0;
            }

            // 图片
            if (isset($v['videoArr']) && count($v['videoArr']) > 0) {
                $material_id = '';
                foreach ($v['videoArr'] as $k1 => $v1) {
                    $material_id = $v1['id'];
                }
                $item['video_id'] = $material_id;
            } else {
                $item['video_id'] = 0;
            }
            if (count($error) == 0) {
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'service_name' => $item['service_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ServiceModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '服务名称重复';
                }
                $one = $RandombarcodeCtrl->getServiceBarcode($item['bar_code'], $merchantid);
                if (!$one) {
                    $error[] = '服务条码重复';
                }
                if (count($error) == 0) {
                    $serviceAdd = $ServiceModel->add($item);
                    if ($serviceAdd) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ServiceModel->error ? $ServiceModel->error : '服务添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    // 规格服务导入
    public function skuServiceImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 2
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $addCommon = array(
            'merchant_id' => $userInfo['merchantid'],
            'is_sku' => 1,                              // 1有规格 2无规格
            'content' => '',
            'adjust_price_id' => 0,
            // 'is_sku_img' => 2,                          // 1有规格图片 2无
            'status' => 1,
            'addtime' => time(),
            'price_tag' => ''
        );
        $this->getServiceClass($merchantid);
        $this->getServiceLabel($merchantid);
        $ServiceModel = new ServiceModel;
        $successNum = 0;
        $errorNum = 0;
        // 规格添加
        $newData = array();
        $error = array();
        $type = 0;
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            // 是服务
            if ($v['serviceType'] == '服务') {
                if($type!=0){
                    if(isset($info[$type-1])){
                        if(isset($info[$type-1]['error'])){
                            $prevService = $info[$type-1]['error'];
                            if($prevService!==false){
                                $prevService[] = '规格服务不能没有规格数据';
                            }else{
                                $prevService = array();
                                $prevService[] = '规格服务不能没有规格数据';
                            }
                        }else{
                            $prevService = array();
                            $prevService[] = '规格服务不能没有规格数据';
                        }
                        $info[$type-1]['error'] = $prevService;
                    }
                }
                $type = $k;
                $item = $addCommon;
                $error = array();
                // 服务名称
                $item['service_name'] = $v['service_name'];
                // 处理数据时先不自动生成条码
                $item['bar_code'] = $v['bar_code'];
                $item['classification_id'] = $v['classification_id'];
                // $item['label_id'] = $v['label_id'];
                if(!$v['service_name']){
                    $error[] = "名称不能为空";
                }
                if(!$v['classification_id']){
                    $error[] = "分类不能为空";
                }
                // if(!$v['label_id']){
                //     $error[] = "标签不能为空";
                // }
                // 服务条码
//                if ($v['bar_code']) {
//                    $item['bar_code'] = $v['bar_code'];
//                } else {
//                    $item['bar_code'] = $this->barCode(1);
//                }
//                if (!$item['bar_code']) {
//                    $error[] = '条码有误';
//                }
                // 分类
                // $item['classification_id'] = $this->classNameToClassId($v['classification_id'], $merchantid);
                // 标签
                // $item['label_id'] = $this->labelNameToClassId($v['label_id'], $merchantid);
                // 售价
                if (is_numeric($v['price']) && $v['price'] > 0) {
                    $item['price'] = intval($v['price'] * 100);
                } else {
                    $item['price'] = 0;
                    $error[] = '售价有误';
                }
                if (is_numeric($v['duration']) && $v['duration'] > 0) {
                    $item['duration'] = 15 * (ceil($v['duration'] / 15));
                } else {
                    $error[] = '服务时长有误';
                }
                //网店展示
                if ($v['shop_display'] == '是') {
                    $item['shop_display'] = 1; // 网店展示 1展示 2不展示
                } else {
                    $item['shop_display'] = 2; // 网店展示 1展示 2不展示
                }
                //上门服务
                if ($v['door_service'] == '是') {
                    $item['door_service'] = 2; // 上门服务 2开启 1关闭
                } else {
                    $item['door_service'] = 1; // 上门服务 2开启 1关闭
                }
                // 上门服务付定金
                $item['pre_door'] = 2;

                //上门支付 1百分比支付 2 固定金额支付
                if ($v['pre_door_mode'] == '百分比') {
                    $item['pre_door_mode'] = 1; // 1百分比支付 2 固定金额支付
                } else {
                    $item['pre_door_mode'] = 2; // 1百分比支付 2 固定金额支付
                }

                if (is_numeric($v['door_percentage']) && $v['door_percentage'] > 0 && $v['door_percentage'] <= 100) {
                    $item['door_percentage'] = $v['door_percentage'] / 100;
                } else {
                    $item['door_percentage'] = 0.2;
                }
                if (is_numeric($v['door_fixed_amount']) && $v['door_fixed_amount'] > 0 && $v['door_fixed_amount'] <= $item['price'] / 100) {
                    $item['door_fixed_amount'] = intval($v['door_fixed_amount'] * 100);
                } else {
                    $item['door_fixed_amount'] = $item['price'];
                }
                //预约到店
                if ($v['pro_shop'] == '到店付') {
                    $item['pro_shop'] = 2; // 预约到店 1付全款 2到店付 3付定金
                } else {
                    $item['pro_shop'] = 3; // 预约到店 1付全款 2到店付 3付定金
                }

                if ($v['pro_shop_mode'] == '固定金额') {
                    $item['pro_shop_mode'] = 2; // 到店支付 1百分比支付  2固定金额支付
                } else {
                    $item['pro_shop_mode'] = 1; // 到店支付 1百分比支付  2固定金额支付
                }
                // 到店百分比支付
                if (is_numeric($v['shop_percentage']) && $v['shop_percentage'] > 0 && $v['shop_percentage'] <= 100) {
                    $item['shop_percentage'] = $v['shop_percentage'] / 100;
                } else {
                    $item['shop_percentage'] = 0.2;
                }
                // 到店固定金额
                if (is_numeric($v['shop_fixed_amount']) && $v['shop_fixed_amount'] > 0 && $v['shop_fixed_amount'] <= $item['price'] / 100) {
                    $item['shop_fixed_amount'] = intval($v['shop_fixed_amount'] * 100);
                } else {
                    $item['shop_fixed_amount'] = $item['price'];
                }
                // 图片
                if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                    $material_id = '';
                    foreach ($v['imgArr'] as $k1 => $v1) {
                        $material_id = $material_id . ',' . $v1['id'];
                    }
                    $item['material_id'] = trim($material_id, ',');
                } else {
                    $item['material_id'] = 0;
                }
                // 图片
                if (isset($v['videoArr']) && count($v['videoArr']) > 0) {
                    $material_id = '';
                    foreach ($v['videoArr'] as $k1 => $v1) {
                        $material_id = $v1['id'];
                    }
                    $item['video_id'] = $material_id;
                } else {
                    $item['video_id'] = 0;
                }
                // 规格头部
                $skuArr = array();
                if ($v['sku1']) {
                    $skuArr[0] = $v['sku1'];
                }
                if ($v['sku2']) {
                    $skuArr[1] = $v['sku2'];
                }
                if ($v['sku3']) {
                    $skuArr[2] = $v['sku3'];
                }
                if (count($skuArr) == 0) {
                    $error[] = '规格分类必须存在';
                }
                $item['sku'] = $skuArr;
                if (count($error) == 0) {
                    $nameMap = array(
                        'merchant_id' => $merchantid,
                        'service_name' => $item['service_name'],
                        'status' => array('in', '1,2')
                    );
                    $one = $ServiceModel->getFind($nameMap, 'id');
                    if ($one && $one['id']) {
                        $error[] = '服务名称重复';
                    }
                    if (count($error) == 0) {
                        $v['error'] = false;
                        $newData[] = array(
                            'service' => $item,
                            'sku' => array()
                        );
                    } else {
                        $errorNum++;
                        $v['error'] = $error;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else if ($v['serviceType'] == '规格') {
                $type = 0;
                $error2 = array();
                if (count($error) > 0) {
                    $error2[] = '服务有误';
                    $v['error'] = $error2;
                    continue;
                }
                $item = array();
                $item['bar_code'] = $v['bar_code'];
                if (is_numeric($v['price']) && $v['price'] > 0) {
                    $item['price'] = intval($v['price'] * 100);
                } else {
                    $item['price'] = 0;
                    $error2[] = '售价有误';
                }
                // 图片
                if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                    $material_id = '';
                    foreach ($v['imgArr'] as $k1 => $v1) {
                        $material_id = $material_id . ',' . $v1['id'];
                    }
                    $item['material_id'] = trim($material_id, ',');
                } else {
                    $item['material_id'] = 0;
                }
                $service = $newData[count($newData) - 1];
                $skuTitle = $service['service']['sku'];
                // 规格头部
                $skuArr = array();
                if ($v['sku1'] && (isset($skuTitle[0]) && $skuTitle[0])) {
                    $skuArr[0] = $v['sku1'];
                }
                if ($v['sku2'] && (isset($skuTitle[1]) && $skuTitle[1])) {
                    $skuArr[1] = $v['sku2'];
                }
                if ($v['sku3'] && (isset($skuTitle[2]) && $skuTitle[2])) {
                    $skuArr[2] = $v['sku3'];
                }
                if (count($skuArr) != count($skuTitle)) {
                    $error2[] = '规格与服务规格数量不对应';
                }
                $item['sku'] = $skuArr;
                if (count($error2) == 0) {
                    $v['error'] = false;
                    $item['error'] = false;
                    $newData[count($newData) - 1]['sku'][] = $item;
                } else {
                    $v['error'] = $error2;
                    $item['error'] = $error2;
                    $newData[count($newData) - 1]['sku'][] = $item;
                }
            }
        }
        //
        $Skumodel = new SkuModel;
        $skuValmodel = new SkuValModel;
        $SkuAttr = new SkuAttrModel;
        $RandombarcodeCtrl = new RandombarcodeCtrl;
        foreach ($newData as $key => $val) {
            $sku = $val['sku'];
            $flag = true;
            foreach ($sku as $k => $v) {
                if ($v['error']) {
                    $flag = false;
                    break;
                }
            }
            if (!$flag) {
                $errorNum++;
                continue;
            }
            // 添加服务，生成SKU
            $addData = $val['service'];
            unset($addData['error']);
            // 将数据添加入服务表
            // 条码
            if (!$addData['bar_code']) {
                $addData['bar_code'] = $this->barCode(1);
            }
            // 分类
            $addData['classification_id'] = $this->classNameToClassId($addData['classification_id'], $merchantid);
            // 标签
            // $addData['label_id'] = $this->labelNameToClassId($addData['label_id'], $merchantid);
            $skuTitle = $addData['sku'];
            unset($addData['sku']);
            $one = $RandombarcodeCtrl->getServiceBarcode($addData['bar_code'], $merchantid);
            if (!$one) {
                $errorNum++;
                continue;
            }
            Db::startTrans();
            $serviceId = $ServiceModel->add($addData);
            $sku = $val['sku'];
            $hasError = false;
            if ($serviceId) {
                foreach ($skuTitle as $k => $v) {
                    if ($hasError) {
                        break;
                    }
                    $Sku = array();
                    $Sku["merchant_id"] = $merchantid;
                    $Sku["ser_good_id"] = $serviceId;
                    $Sku["name"] = $v;
                    $Sku["addtime"] = time();
                    // 添加规格分类
                    $Skuid = $Skumodel->add($Sku);
                    if ($Skuid) {
                        $hasAddSkuName = array();
                        foreach ($sku as $k2 => $v2) {
                            if ($hasError) {
                                break;
                            }
                            $skuName = $v2['sku'][$k];
                            if (in_array($skuName, $hasAddSkuName)) {
                                continue;
                            }
                            $hasAddSkuName[] = $skuName;
                            $SkuVal = array();
                            $SkuVal['sku_id'] = $Skuid;
                            $SkuVal['merchant_id'] = $merchantid;
                            $SkuVal['ser_good_id'] = $serviceId;
                            $SkuVal['name'] = $skuName;
                            $SkuVal['addtime'] = time();
                            if (isset($v2['material_id']) && $v2['material_id']) {
                                $SkuVal['material_id'] = $v2['material_id'];
                            }
                            $SkuValid = $skuValmodel->add($SkuVal);
                            if (!$SkuValid) {
                                $hasError = true;
                            }
                        }
                    } else {
                        $hasError = true;
                    }
                }
                foreach ($sku as $k => $v) {
                    if ($hasError) {
                        break;
                    }
                    $wess['name'] = array("in", $v['sku']);
                    $wess['merchant_id'] = $merchantid;
                    $wess['ser_good_id'] = $serviceId; // 服务标识
                    $SkuValList = $skuValmodel->getList($wess);
                    $sku_val_id = '';
                    foreach ($SkuValList as $kay => $val) {
                        $sku_val_id .= $val['id'] . ",";
                    }
                    $attr['sku_val_id'] = substr($sku_val_id, 0, -1);
                    $attr['ser_good_id'] = $serviceId;
                    $attr['merchant_id'] = $merchantid;
                    $attr['sku'] = implode(',', $v['sku']);
                    $attr['price'] = $v['price'];
                    if ($v['bar_code']) {
                        $attr['barcode'] = $v['bar_code'];
                    } else {
                        $attr['barcode'] = $this->barCode(1);
                    }
                    $one = $RandombarcodeCtrl->getServiceBarcode($attr['barcode'], $merchantid);
                    if (!$one) {
                        $hasError = true;
                        break;
                    }
                    $attr['addtime'] = time();
                    $Attrs = $SkuAttr->add($attr);
                    if (!$Attrs) {
                        $hasError = true;
                    }
                }
            } else {
                $hasError = true;
            }
            if ($hasError) {
                $errorNum++;
                Db::rollback();
            } else {
                $successNum++;
                Db::commit();
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    public function barCode($type)
    {
        $RandombarcodeCtrl = new RandombarcodeCtrl;
        return $RandombarcodeCtrl->getBarcode($type);
    }

    // 服务分类名称转分类id
    public function classNameToClassId($name, $merchantid)
    {
        if (!$name) {
            return 0;
        }
        $classArr = $this->serviceClassArr;
        foreach ($classArr as $k => $v) {
            if ($name == $v['name']) {
                return $v['id'];
            }
        }
        $ServiceClassModel = new ServiceClassModel;
        $addData = array(
            'merchant_id' => $merchantid,
            'classification_name' => $name,
            'addtime' => time()
        );
        $res = $ServiceClassModel->add($addData);
        if ($res) {
            $id = $ServiceClassModel->getLastInsID();
            $this->serviceClassArr[] = array('id' => $id, 'name' => $name);
            return $id;
        }
        return 0;
    }

    public function labelNameToClassId($name, $merchantid)
    {
        if (!$name) {
            return 0;
        }
        $nameArr = explode('/', $name); // 将标签以/分割开
        $classArr = $this->serviceLabelArr;
        $ServiceLabelModel = new ServiceLabelModel;
        $labelIds = '';
        foreach ($nameArr as $key => $val) {
            $flag = false;
            foreach ($classArr as $k => $v) {
                if ($val == $v['name']) {
                    $labelIds = $labelIds . ',' . $v['id'];
                    $flag = true;
                    break;
                }
            }
            if (!$flag) {
                $addData = array(
                    'merchant_id' => $merchantid,
                    'label_name' => $val,
                    'addtime' => time()
                );
                $res = $ServiceLabelModel->add($addData);
                if ($res) {
                    $id = $ServiceLabelModel->getLastInsID();
                    $this->serviceLabelArr[] = array('id' => $id, 'name' => $val);
                    $labelIds = $labelIds . ',' . $id;
                    $classArr = $this->serviceLabelArr;
                }
            }
        }
        return $labelIds ? trim($labelIds, ',') : 0;
    }

    // 非规格产品导入

    public function noSkuProductImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 3
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $this->getProductClass($merchantid);
        $this->getProductLabel($merchantid);
        $addCommon = array(
            'merchant_id' => $userInfo['merchantid'],
            'issku' => 2,
            'detail' => '',
            'adjust_price_id' => 0,
            'status' => 1,
            'addtime' => time(),
            'price_tag' => '',
            'freight_id' => 0
        );
        $ProductModel = new ProductModel;
        $successNum = 0;
        $errorNum = 0;
        // 有条码的先添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if (!$v['product_barcode']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            if(!$v['product_name']){
                $error[] = '名称不能为空';
            }
            $item['product_name'] = $v['product_name'];
            // 服务条码
            if ($v['product_barcode']) {
                $item['product_barcode'] = $v['product_barcode'];
            } else {
                $item['product_barcode'] = $this->barCode(2);
            }
            if (!$item['product_barcode']) {
                $error[] = '条码有误';
            }
            if ($v['unit']) {
                $item['unit'] = $v['unit'];
            } else {
                $item['unit'] = '件';
            }
            $item['small_unit'] = $item['unit'];
            if(!$v['product_classid']){
                $error[] = '分类不能为空';
            }
            // 分类
            $item['product_classid'] = $this->productClassNameToClassId($v['product_classid'], $merchantid);
            // 标签
            // if(!$v['product_labelids']){
            //     $error[] = '标签不能为空';
            // }
            // $item['product_labelids'] = $this->productLabelNameToClassId($v['product_labelids'], $merchantid);
            // 售价
            if (is_numeric($v['price']) && $v['price'] > 0) {
                $item['price'] = intval($v['price'] * 100);
            } else {
                $item['price'] = 0;
                $error[] = '售价有误';
            }
            if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                $item['cost_price'] = intval($v['cost_price'] * 100);
            } else {
                $item['cost_price'] = 0;
                $error[] = '成本价有误';
            }
            if (is_numeric($v['freight']) && $v['freight'] >= 0) {
                $item['freight'] = intval($v['freight'] * 100);
            } else {
                $item['freight'] = 0;
//                $error[] = '运费有误';
            }
            if ($v['sell_online'] == '否') {
                $item['sell_online'] = 2;
            } else {
                $item['sell_online'] = 1;
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['img_id'] = trim($material_id, ',');
            } else {
                $item['img_id'] = 0;
            }
            // 图片
            if (isset($v['videoArr']) && count($v['videoArr']) > 0) {
                $material_id = '';
                foreach ($v['videoArr'] as $k1 => $v1) {
                    $material_id = $v1['id'];
                }
                $item['video_id'] = $material_id;
            } else {
                $item['video_id'] = 0;
            }
            if (count($error) == 0) {
                $one = $ProductModel->getBarcodeFind($item['product_barcode'], $merchantid);
                if (!$one) {
                    $error[] = '商品条码重复';
                }
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'product_name' => $item['product_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ProductModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '商品名称重复';
                }
                if (count($error) == 0) {
                    $res = $ProductModel->add($item);
                    if ($res) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ProductModel->error ? $ProductModel->error : '产品添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }
        // 有条码的先添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if ($v['product_barcode']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            if(!$v['product_name']){
                $error[] = '名称不能为空';
            }
            $item['product_name'] = $v['product_name'];
            // 服务条码
            if ($v['product_barcode']) {
                $item['product_barcode'] = $v['product_barcode'];
            } else {
                $item['product_barcode'] = $this->barCode(2);
            }
            if (!$item['product_barcode']) {
                $error[] = '条码有误';
            }
            if ($v['unit']) {
                $item['unit'] = $v['unit'];
            } else {
                $item['unit'] = '件';
            }
            $item['small_unit'] = $item['unit'];// 耗用单位
            if(!$v['product_classid']){
                $error[] = '分类不能为空';
            }
            // 分类
            $item['product_classid'] = $this->productClassNameToClassId($v['product_classid'], $merchantid);
            // 标签
            // if(!$v['product_labelids']){
            //     $error[] = '标签不能为空';
            // }
            // $item['product_labelids'] = $this->productLabelNameToClassId($v['product_labelids'], $merchantid);
            // 售价
            if (is_numeric($v['price']) && $v['price'] > 0) {
                $item['price'] = intval($v['price'] * 100);
            } else {
                $item['price'] = 0;
                $error[] = '售价有误';
            }
            if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                $item['cost_price'] = intval($v['cost_price'] * 100);
            } else {
                $item['cost_price'] = 0;
                $error[] = '成本价有误';
            }
            if (is_numeric($v['freight']) && $v['freight'] > 0) {
                $item['freight'] = intval($v['freight'] * 100);
            } else {
                $item['freight'] = 0;
//                $error[] = '运费有误';
            }
            if ($v['sell_online'] == '否') {
                $item['sell_online'] = 2;
            } else {
                $item['sell_online'] = 1;
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['img_id'] = trim($material_id, ',');
            } else {
                $item['img_id'] = 0;
            }
            // 图片
            if (isset($v['videoArr']) && count($v['videoArr']) >= 0) {
                $material_id = '';
                foreach ($v['videoArr'] as $k1 => $v1) {
                    $material_id = $v1['id'];
                }
                $item['video_id'] = $material_id;
            } else {
                $item['video_id'] = 0;
            }
            if (count($error) == 0) {
                $one = $ProductModel->getBarcodeFind($item['product_barcode'], $merchantid);
                if (!$one) {
                    $error[] = '商品条码重复';
                }
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'product_name' => $item['product_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ProductModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '商品名称重复';
                }
                if (count($error) == 0) {
                    $res = $ProductModel->add($item);
                    if ($res) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ProductModel->error ? $ProductModel->error : '产品添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }

        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    // 规格产品导入

    public function skuProductImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 4
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $this->getProductClass($merchantid);
        $this->getProductLabel($merchantid);
        $addCommon = array(
            'merchant_id' => $userInfo['merchantid'],
            'issku' => 1,
            'detail' => '',
            'adjust_price_id' => 0,
            'status' => 1,
            'addtime' => time(),
            'price_tag' => '',
            'freight_id' => 0
        );
        $ProductModel = new ProductModel;
        $successNum = 0;
        $errorNum = 0;
        $newData = array();
        $error = array();
        $index = 0;
        $type = 0;
        //pre($info);
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if($v['productType'] != '产品' && $v['productType'] != '规格'){
                $v['error'] = array('类型错误');
            }
            if ($v['productType'] == '产品') {
                if($type!=0){
                    if(isset($info[$type-1])){
                        if(isset($info[$type-1]['error'])){
                            $prevService = $info[$type-1]['error'];
                            if($prevService!==false){
                                $prevService[] = '规格产品不能没有规格数据';
                            }else{
                                $prevService = array();
                                $prevService[] = '规格产品不能没有规格数据';
                            }
                        }else{
                            $prevService = array();
                            $prevService[] = '规格产品不能没有规格数据';
                        }
                        $info[$type-1]['error'] = $prevService;
                    }
                }
                $type = $k;
                $index = $k;
                $item = $addCommon;
                $error = array();
                // 名称
                $item['product_name'] = $v['product_name'];
                if(!$v['product_name']){
                    $error[] = "名称不能为空";
                }
                $item['product_classid'] = $v['product_classid'];
                if(!$v['product_classid']){
                    $error[] = "分类不能为空";
                }
                $item['product_labelids'] = $v['product_labelids'];
                // if(!$v['product_classid']){
                //     $error[] = "标签不能为空";
                // }
                // 条码
                $item['product_barcode'] = $v['product_barcode'];
                if ($v['unit']) {
                    $item['unit'] = $v['unit'];
                } else {
                    $item['unit'] = '件';
                }
                $item['small_unit'] = $item['unit'];// 耗用单位
                // 售价
                if (is_numeric($v['price']) && $v['price'] > 0) {
                    $item['price'] = intval($v['price'] * 100);
                } else {
                    $item['price'] = 0;
                    $error[] = '售价有误';
                }
                if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                    $item['cost_price'] = intval($v['cost_price'] * 100);
                } else {
                    $item['cost_price'] = 0;
                    $error[] = '成本价有误';
                }
                if (is_numeric($v['freight']) && $v['freight'] >= 0) {
                    $item['freight'] = intval($v['freight'] * 100);
                } else {
                    $item['freight'] = 0;
//                    $error[] = '运费有误';
                }
                if ($v['sell_online'] == '否') {
                    $item['sell_online'] = 2;
                } else {
                    $item['sell_online'] = 1;
                }
                // 图片
                if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                    $material_id = '';
                    foreach ($v['imgArr'] as $k1 => $v1) {
                        $material_id = $material_id . ',' . $v1['id'];
                    }
                    $item['img_id'] = trim($material_id, ',');
                } else {
                    $item['img_id'] = 0;
                }
                // 图片
                if (isset($v['videoArr']) && count($v['videoArr']) > 0) {
                    $material_id = '';
                    foreach ($v['videoArr'] as $k1 => $v1) {
                        $material_id = $v1['id'];
                    }
                    $item['video_id'] = $material_id;
                } else {
                    $item['video_id'] = 0;
                }
                // 规格头部
                $skuArr = array();
                if ($v['sku1']) {
                    $skuArr[0] = $v['sku1'];
                }
                if ($v['sku2']) {
                    $skuArr[1] = $v['sku2'];
                }
                if ($v['sku3']) {
                    $skuArr[2] = $v['sku3'];
                }
                if (count($skuArr) == 0) {
                    $error[] = '规格分类必须存在';
                }
                $item['sku'] = $skuArr;
                if (count($error) == 0) {
                    if($item['product_barcode']){
                        $one = $ProductModel->getBarcodeFind($item['product_barcode'], $merchantid);
                        if (!$one) {
                            $error[] = '商品条码重复';
                        }
                    }
                    $nameMap = array(
                        'merchant_id' => $merchantid,
                        'product_name' => $item['product_name'],
                        'status' => array('in', '1,2')
                    );
                    $one = $ProductModel->getFind($nameMap, 'id');
                    if ($one && $one['id']) {
                        $error[] = '商品名称重复';
                    }
                    if (count($error) == 0) {
                        $v['error'] = false;
                        $newData[] = array(
                            'product' => $item,
                            'sku' => array()
                        );
                    } else {
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else if ($v['productType'] == '规格') {
                $type = 0;
                $error2 = array();
                if (count($error) > 0) {
                    $error2[] = '服务有误';
                    $v['error'] = $error2;
                    continue;
                }
                $item = array();
                $item['product_barcode'] = $v['product_barcode'];
                // 售价
                if (is_numeric($v['price']) && $v['price'] > 0) {
                    $item['price'] = intval($v['price'] * 100);
                } else {
                    $item['price'] = 0;
                    $error2[] = '售价有误';
                }
                if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                    $item['cost_price'] = intval($v['cost_price'] * 100);
                } else {
                    $item['cost_price'] = 0;
                    $error2[] = '成本价有误';
                }
                // 图片
                if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                    $material_id = '';
                    foreach ($v['imgArr'] as $k1 => $v1) {
                        $material_id = $material_id . ',' . $v1['id'];
                    }
                    $item['img_id'] = trim($material_id, ',');
                } else {
                    $item['img_id'] = 0;
                }
                $service = $newData[count($newData) - 1];
                $skuTitle = $service['product']['sku'];
                // 规格头部
                $skuArr = array();
                if ($v['sku1'] && (isset($skuTitle[0]) && $skuTitle[0])) {
                    $skuArr[0] = $v['sku1'];
                }
                if ($v['sku2'] && (isset($skuTitle[1]) && $skuTitle[1])) {
                    $skuArr[1] = $v['sku2'];
                }
                if ($v['sku3'] && (isset($skuTitle[2]) && $skuTitle[2])) {
                    $skuArr[2] = $v['sku3'];
                }
                if (count($skuArr) != count($skuTitle)) {
                    $error2[] = '规格与服务规格数量不对应';
                }
                $item['sku'] = $skuArr;
                if (count($error2) == 0) {
                    $v['error'] = false;
                    $item['error'] = false;
                    $newData[count($newData) - 1]['sku'][] = $item;
                } else {
                    $v['error'] = $error2;
                    $info[$index]['error'] = '规格数据有误';
                }
            }

        }
        $infoJson = json_encode($info);
//        $data = array(
//            'info'=>$info,
//            'data'=>$newData
//        );
//        return $this->ajaxSuccess($data);
        $RandombarcodeCtrl = new RandombarcodeCtrl;

//        $ProductSkuAttrModel = new ProductSkuAttrModel;
        //
        foreach ($newData as $key => $val) {
            $sku = $val['sku'];
            $flag = true;
            foreach ($sku as $k => $v) {
                if ($v['error']) {
                    $flag = false;
                    break;
                }
            }
            if(count($sku)==0){
                $errorNum++;
                continue;
            }
            if (!$flag) {
                $errorNum++;
                continue;
            }
            // 添加服务，生成SKU
            $addData = $val['product'];
            unset($addData['error']);
            // 将数据添加入服务表
            // 条码
            if (!$addData['product_barcode']) {
                $addData['product_barcode'] = $this->barCode(2);
            }
            // 分类
            $addData['product_classid'] = $this->productClassNameToClassId($addData['product_classid'], $merchantid);
            // 标签
            // $addData['product_labelids'] = $this->productLabelNameToClassId($addData['product_labelids'], $merchantid);
            $skuTitle = $addData['sku'];
            unset($addData['sku']);
            $one = $RandombarcodeCtrl->getProductBarcode($addData['product_barcode'], $merchantid);
            if (!$one) {
                $errorNum++;
                continue;
            }
            $pidArr = array();
            foreach ($skuTitle as $k => $v) {
                $pidArr[] = $this->getProductSkuId($v, $merchantid, 0);
            }
            $sku = $val['sku'];
            $skuAddData = array();
            $hasError = false;
            foreach ($sku as $k1 => $v1) {
                if ($hasError) {
                    break;
                }
                $skuSku = $v1['sku'];
                $sku_val_id = '';
                foreach ($skuSku as $k2 => $v2) {
                    $sku_id = $this->getProductSkuId($v2, $merchantid, $pidArr[$k2]);
                    $sku_val_id = $sku_val_id . ',' . $sku_id;
                }
                $sku_val_id = trim($sku_val_id, ',');
                $skuItem = array(
                    'product_id' => 0,
                    'merchant_id' => $merchantid,
                    'sku_val_id' => $sku_val_id,
                    'sku' => implode(',', $skuSku),
                    'price' => $v1['price'],
                    'cost_price' => $v1['cost_price'],
                    'img' => $v1['img_id'] ? $v1['img_id'] : 0
                );
                if ($v1['product_barcode']) {
                    $skuItem['barcode'] = $v1['product_barcode'];
                } else {
                    $skuItem['barcode'] = $this->barCode(2);
                }
                $one = $RandombarcodeCtrl->getProductBarcode($skuItem['barcode'], $merchantid);
                if (!$one) {
                    $hasError = true;
                }
                $skuAddData[] = $skuItem;
            }
            if ($hasError) {
                $errorNum++;
            } else {
                $res = $ProductModel->add($addData, $skuAddData);
                if ($res) {
                    $successNum++;
                } else {
                    $errorNum++;
                }
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => $infoJson,
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    // 耗材产品导入

    public function consumableImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 5
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $this->getProductClass($merchantid);
        $this->getProductLabel($merchantid);
        $addCommon = array(
            'merchant_id' => $userInfo['merchantid'],
            'issku' => 2,
            'detail' => '',
            'adjust_price_id' => 0,
            'status' => 6,
            'addtime' => time(),
            'price_tag' => '',
            'freight_id' => 0,
            'consumable_class' => 1, // 服务耗材
        );
        $ProductModel = new ProductModel;
        $successNum = 0;
        $errorNum = 0;
        // 有条码的先添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if (!$v['product_barcode']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            $item['product_name'] = $v['product_name'];
            if(!$v['product_name']){
                $error[] = "名称不能为空";
            }
            // 服务条码
            if ($v['product_barcode']) {
                $item['product_barcode'] = $v['product_barcode'];
            } else {
                $item['product_barcode'] = $this->barCode(2);
            }
            if (!$item['product_barcode']) {
                $error[] = '条码有误';
            }
            if ($v['unit']) {
                $item['unit'] = $v['unit'];
            } else {
                $item['unit'] = '件';
            }
            if(isset($v['small_unit'])){
                $item['small_unit'] = $v['small_unit'];     // 耗用单位
            }else{
                $item['small_unit'] = $item['unit'];        // 耗用单位
            }
            if(isset($v['small_content'])){
                if(floor($v['small_content']) == $v['small_content']){
                    if($v['small_content']<1){
                        $error[] = "含量有误，含量必须是整数，且不小于1";
                    }else{
                        $item['small_content'] = floor($v['small_content']);     // 耗用含量
                    }
                }else{
                    $error[] = "含量有误，含量必须是整数";
                }
            }else{
                $item['small_content'] = 1;     // 耗用含量
            }
            if(isset($v['consumable_class'])){
                if($v['consumable_class']==1 || $v['consumable_class']==2 || $v['consumable_class']==3){
                    $item['consumable_class'] = $v['consumable_class'];     // 服务耗材
                }else{
                    $error[] = "耗材类型有误";
                }
            }else{
                $item['consumable_class'] = 1;     // 服务耗材
            }
            // 分类
            $item['product_classid'] = $this->productClassNameToClassId($v['product_classid'], $merchantid);
            if(!$v['product_classid']){
                $error[] = "分类有误";
            }
            if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                $item['cost_price'] = intval($v['cost_price'] * 100);
            } else {
                $item['cost_price'] = 0;
                $error[] = '成本价有误';
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['img_id'] = trim($material_id, ',');
            } else {
                $item['img_id'] = 0;
            }
            if (count($error) == 0) {
                $one = $ProductModel->getBarcodeFind($item['product_barcode'], $merchantid);
                if (!$one) {
                    $error[] = '商品条码重复';
                }
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'product_name' => $item['product_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ProductModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '商品名称重复';
                }
                if (count($error) == 0) {
                    $res = $ProductModel->add($item);
                    if ($res) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ProductModel->error ? $ProductModel->error : '产品添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }
        // 无条码的后添加
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            if ($v['product_barcode']) {
                continue;
            }
            $item = $addCommon;
            $error = array();
            // 服务名称
            $item['product_name'] = $v['product_name'];
            if(!$v['product_name']){
                $error[] = "名称不能为空";
            }
            // 服务条码
            if ($v['product_barcode']) {
                $item['product_barcode'] = $v['product_barcode'];
            } else {
                $item['product_barcode'] = $this->barCode(2);
            }
            if (!$item['product_barcode']) {
                $error[] = '条码有误';
            }
            if ($v['unit']) {
                $item['unit'] = $v['unit'];
            } else {
                $item['unit'] = '件';
            }
            if(isset($v['small_unit'])){
                $item['small_unit'] = $v['small_unit'];     // 耗用单位
            }else{
                $item['small_unit'] = $item['unit'];        // 耗用单位
            }
            if(isset($v['small_content'])){
                if(floor($v['small_content']) == $v['small_content']){
                    if($v['small_content']<1){
                        $error[] = "含量有误，含量必须是整数，且不小于1";
                    }else{
                        $item['small_content'] = floor($v['small_content']);     // 耗用含量
                    }
                }else{
                    $error[] = "含量有误，含量必须是整数";
                }
            }else{
                $item['small_content'] = 1;     // 耗用含量
            }
            if(isset($v['consumable_class'])){
                if($v['consumable_class']==1 || $v['consumable_class']==2 || $v['consumable_class']==3){
                    $item['consumable_class'] = $v['consumable_class'];     // 服务耗材
                }else{
                    $error[] = "耗材类型有误";
                }
            }else{
                $item['consumable_class'] = 1;     // 服务耗材
            }
            // 分类
            $item['product_classid'] = $this->productClassNameToClassId($v['product_classid'], $merchantid);
            if(!$v['product_classid']){
                $error[] = "分类有误";
            }
            if (is_numeric($v['cost_price']) && $v['cost_price'] > 0) {
                $item['cost_price'] = intval($v['cost_price'] * 100);
            } else {
                $item['cost_price'] = 0;
                $error[] = '成本价有误';
            }
            // 图片
            if (isset($v['imgArr']) && count($v['imgArr']) > 0) {
                $material_id = '';
                foreach ($v['imgArr'] as $k1 => $v1) {
                    $material_id = $material_id . ',' . $v1['id'];
                }
                $item['img_id'] = trim($material_id, ',');
            } else {
                $item['img_id'] = 0;
            }
            if (count($error) == 0) {
                $one = $ProductModel->getBarcodeFind($item['product_barcode'], $merchantid);
                if (!$one) {
                    $error[] = '商品条码重复';
                }
                $nameMap = array(
                    'merchant_id' => $merchantid,
                    'product_name' => $item['product_name'],
                    'status' => array('in', '1,2')
                );
                $one = $ProductModel->getFind($nameMap, 'id');
                if ($one && $one['id']) {
                    $error[] = '商品名称重复';
                }
                if (count($error) == 0) {
                    $res = $ProductModel->add($item);
                    if ($res) {
                        $successNum++;
                        $v['error'] = false;
                    } else {
                        $error[] = $ProductModel->error ? $ProductModel->error : '产品添加失败';
                        $v['error'] = $error;
                        $errorNum++;
                    }
                } else {
                    $v['error'] = $error;
                    $errorNum++;
                }
            } else {
                $v['error'] = $error;
                $errorNum++;
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    protected $productClassArr = array();

    protected $productLabelArr = array();

    public function getProductClass($merchantid)
    {
        $ProductLabelClassModel = new ProductLabelClassModel;
        $where = array(
            'merchantid' => $merchantid,
            'storeid' => 0,
            'type' => 1,
            'status' => 1
        );
        $data = $ProductLabelClassModel->getLabelClassData($where, 1, [], 'id,name');
        if (is_object($data)) {
            $data = $data->toArray();
        }
        $this->productClassArr = $data;
    }

    public function getProductLabel($merchantid)
    {
        $ProductLabelClassModel = new ProductLabelClassModel;
        $where = array(
            'merchantid' => $merchantid,
            'storeid' => 0,
            'type' => 2,
            'status' => 1
        );
        $data = $ProductLabelClassModel->getLabelClassData($where, 1, [], 'id,name');
        if (is_object($data)) {
            $data = $data->toArray();
        }
        $this->productLabelArr = $data;
    }

    // 服务分类名称转分类id
    public function productClassNameToClassId($name, $merchantid)
    {
        if (!$name) {
            return 0;
        }
        $classArr = $this->productClassArr;
        foreach ($classArr as $k => $v) {
            if ($name == $v['name']) {
                return $v['id'];
            }
        }
        $ProductLabelClassModel = new ProductLabelClassModel;
        $addData = array(
            'merchantid' => $merchantid,
            'name' => $name,
            'addtime' => time(),
            'type' => 1
        );
        $res = $ProductLabelClassModel->addLabelClass($addData);
        if ($res) {
            $id = $res;
            $this->productClassArr[] = array('id' => $id, 'name' => $name);
            return $id;
        }
        return 0;
    }

    public function productLabelNameToClassId($name, $merchantid)
    {
        if (!$name) {
            return 0;
        }
        $nameArr = explode('/', $name); // 将标签以/分割开
        $classArr = $this->productLabelArr;
        $ProductLabelClassModel = new ProductLabelClassModel;
        $labelIds = '';
        foreach ($nameArr as $key => $val) {
            $flag = false;
            foreach ($classArr as $k => $v) {
                if ($val == $v['name']) {
                    $labelIds = $labelIds . ',' . $v['id'];
                    $flag = true;
                    break;
                }
            }
            if (!$flag) {
                $addData = array(
                    'merchantid' => $merchantid,
                    'name' => $val,
                    'addtime' => time(),
                    'type' => 2
                );
                $res = $ProductLabelClassModel->addLabelClass($addData);
                if ($res) {
                    $id = $res;
                    $this->productLabelArr[] = array('id' => $id, 'name' => $val);
                    $labelIds = $labelIds . ',' . $id;
                    $classArr = $this->productLabelArr;
                }
            }
        }
        return $labelIds ? trim($labelIds, ',') : 0;
    }

    // 处理产品规格信息

    public function getProductSkuId($name, $merchantid, $pid)
    {
        $ProductSkuModel = new ProductSkuModel;
        $data = array(
            'map' => array(
                'name' => $name,
                'pid' => $pid,
                'merchantid' => $merchantid,
                'status' => 1
            ),
            'field' => array('id,name')
        );
        $one = $ProductSkuModel->getFindData($data);
        if ($one && isset($one['id'])) {
            return $one['id'];
        } else {
            $addData = array(
                'name' => $name,
                'pid' => $pid,
                'merchantid' => $merchantid,
                'status' => 1,
                'addtime' => time()
            );
            $one = $ProductSkuModel->add($addData);
            return $one ? $one : 0;
        }
    }

    //下载模版 非规格服务
    public function downloadNoSkuService()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('N')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('O')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('P')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(28);
        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A18')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A18')->getFont()->setSize(16);         //第二行字体大小
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:P1');
        $objPHPExcel->getActiveSheet()->mergeCells('A18:P18');
        for ($i = 2; $i < 18; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:P{$i}");
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '名称')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '最长支持255个字符')
            ->setCellValue('A3', '条码')
            ->setCellValue('B3', '非必填')
            ->setCellValue('C3', '不填则自动生成，最多14个字符')
            ->setCellValue('A4', '分类名')
            ->setCellValue('B4', '必填项。')
            ->setCellValue('C4', '分类列表中没有则自动添加，最多支持20个字符')
            ->setCellValue('A5', '标签')
            ->setCellValue('B5', '必填项')
            ->setCellValue('C5', '分类列表中没有则自动添加，多个标签以“/”分隔，每个标签最多支持20个字符')
            ->setCellValue('A6', '售价')
            ->setCellValue('B6', '必填项(数字)。')
            ->setCellValue('C6', '单位：元，最多支持2位小数，不得超过1亿，例：198.98')
            ->setCellValue('A7', '时长')
            ->setCellValue('B7', '必填项(数字)。')
            ->setCellValue('C7', '必须是15的倍数，例：15,30；如果不是15的倍数，系统会自动计算为15的倍数，如16=>30、29=>30')
            ->setCellValue('A8', '网店展示')
            ->setCellValue('B8', '非必填')
            ->setCellValue('C8', '1，是，2，否，默认 否')
            ->setCellValue('A9', '上门服务')
            ->setCellValue('B9', '非必填')
            ->setCellValue('C9', '1，是，2，否，默认 否')
            ->setCellValue('A10', '预约上门定金')
            ->setCellValue('B10', '非必填')
            ->setCellValue('C10', '1，付全款，2，付定金，默认 付定金 （付全款功能已关闭）')
            ->setCellValue('A11', '预约上门付定金比例')
            ->setCellValue('B11', '非必填')
            ->setCellValue('C11', '，1，百分比，2，固定金额，默认百分比')
            ->setCellValue('A12', '预约上门百分比')
            ->setCellValue('B12', '非必填(数字)')
            ->setCellValue('C12', '只能填写1-100的整数包括1和100，默认0')
            ->setCellValue('A13', '预约上门固定金额')
            ->setCellValue('B13', '非必填(数字)')
            ->setCellValue('C13', '只能填写小于售价的价格，默认0')
            ->setCellValue('A14', '预约到店定金')
            ->setCellValue('B14', '非必填')
            ->setCellValue('C14', '1，付全款，2，到店付，3，付定金，默认 到店付 （付全款功能已关闭）')
            ->setCellValue('A15', '预约到店付定金比例')
            ->setCellValue('B15', '非必填')
            ->setCellValue('C15', '1，百分比，2，固定金额，默认 百分比')
            ->setCellValue('A16', '预约到店百分比')
            ->setCellValue('B16', '非必填(数字)')
            ->setCellValue('C16', '只能填写1-100的整数包括1和100，默认20')
            ->setCellValue('A17', '预约到店固定金额')
            ->setCellValue('B17', '非必填(数字)')
            ->setCellValue('C17', '只能填写小于售价的价格，默认0,如果大于售价，默认售价')
            ->setCellValue('A18', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A19', '名称（必填）')
            ->setCellValue('B19', '条码（非必填）')
            ->setCellValue('C19', '分类名（必填）')
            ->setCellValue('D19', '标签（必填）')
            ->setCellValue('E19', '售价（必填）')
            ->setCellValue('F19', '时长（必填）')
            ->setCellValue('G19', '网店展示（是|否）')
            ->setCellValue('H19', '上门服务（是|否）')
            ->setCellValue('I19', '预约上门定金（付定金）')
            ->setCellValue('J19', '上门付定金比例（百分比|固定金额）')
            ->setCellValue('K19', '上门百分比（0-100）')
            ->setCellValue('L19', '上门固额（金额，小于售价)')
            ->setCellValue('M19', '预约到店定金（到店付|付定金)')
            ->setCellValue('N19', '到店定金比例（百分比|固定金额)')
            ->setCellValue('O19', '到店百分比（0-100)')
            ->setCellValue('P19', '到店固额（金额，小于售价)');

        $objPHPExcel->getActiveSheet()->setTitle('非规格服务导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="非规格服务导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    //下载模版 规格服务
    public function downloadSkuService()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('N')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('O')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('P')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('Q')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('R')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('S')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('T')->setWidth(28);
        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A24')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A24')->getFont()->setSize(16);         //第二行字体大小
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:T1');
        $objPHPExcel->getActiveSheet()->mergeCells('A24:T24');
        for ($i = 2; $i < 24; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:T{$i}");
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '类型')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '1，服务，2，规格，每一条服务数据后面的若干条规格数据是同一数据，其中规格必填项目为【类型、售价、规格1】，选填项目【条码、规格2、规格3】')
            ->setCellValue('A3', '名称')
            ->setCellValue('B3', '必填项。')
            ->setCellValue('C3', '最长支持255个字符，类型是规格时无需填写')
            ->setCellValue('A4', '条码')
            ->setCellValue('B4', '非必填')
            ->setCellValue('C4', '不填则自动生成，最多14个字符，类型是规格时需单独填写，否则自动生成')
            ->setCellValue('A5', '分类名')
            ->setCellValue('B5', '必填项。')
            ->setCellValue('C5', '分类列表中没有则自动添加，最多支持20个字符，类型是规格时无需填写')
            ->setCellValue('A6', '标签')
            ->setCellValue('B6', '必填项')
            ->setCellValue('C6', '分类列表中没有则自动添加，多个标签以“/”分隔，每个标签最多支持20个字符，类型是规格时无需填写')
            ->setCellValue('A7', '售价')
            ->setCellValue('B7', '必填项(数字)。')
            ->setCellValue('C7', '单位：元，最多支持2位小数，不得超过1亿，例：198.98，类型是规格时需填写')
            ->setCellValue('A8', '时长')
            ->setCellValue('B8', '必填项(数字)。')
            ->setCellValue('C8', '必须是15的倍数，例：15,30；如果不是15的倍数，系统会自动计算为15的倍数，如16=>30、29=>30')
            ->setCellValue('A9', '规格1')
            ->setCellValue('B9', '必填项。')
            ->setCellValue('C9', '类型是服务时，是规格分类，类型是规格时，是规格名称，最多10个字符')
            ->setCellValue('A10', '规格2')
            ->setCellValue('B10', '非必填项。')
            ->setCellValue('C10', '含义和‘规格1相同’，规格分类填写，规格也需要填写')
            ->setCellValue('A11', '规格3')
            ->setCellValue('B11', '非必填项。')
            ->setCellValue('C11', '含义和‘规格1相同’，规格分类填写，规格也需要填写')
            ->setCellValue('A12', '网店展示')
            ->setCellValue('B12', '非必填')
            ->setCellValue('C12', '1，是，2，否，默认 否，类型是规格时无需填写')
            ->setCellValue('A13', '提示1：')
            ->setCellValue('B13', '')
            ->setCellValue('C13', '以下所有字段，类型是服务可以选择填写，类型是规格无需填写，填写也不会生效')
            ->setCellValue('A14', '提示2：')
            ->setCellValue('B14', '')
            ->setCellValue('C14', '以下所有的售价，指的是规格售价')
            ->setCellValue('A15', '上门服务')
            ->setCellValue('B15', '非必填')
            ->setCellValue('C15', '1，是，2，否，默认 否，类型是规格时无需填写')
            ->setCellValue('A16', '预约上门定金')
            ->setCellValue('B16', '非必填')
            ->setCellValue('C16', '1，付全款，2，付定金，默认 付定金 （付全款功能已关闭），类型是规格时无需填写')
            ->setCellValue('A17', '预约上门付定金比例')
            ->setCellValue('B17', '非必填')
            ->setCellValue('C17', '，1，百分比，2，固定金额，默认百分比，类型是规格时无需填写')
            ->setCellValue('A18', '预约上门百分比')
            ->setCellValue('B18', '非必填(数字)')
            ->setCellValue('C18', '只能填写1-100的整数包括1和100，默认0，类型是规格时无需填写')
            ->setCellValue('A19', '预约上门固定金额')
            ->setCellValue('B19', '非必填(数字)')
            ->setCellValue('C19', '只能填写小于各规格售价的价格，默认0，类型是规格时无需填写')
            ->setCellValue('A20', '预约到店定金')
            ->setCellValue('B20', '非必填')
            ->setCellValue('C20', '1，付全款，2，到店付，3，付定金，默认 到店付 （付全款功能已关闭），类型是规格时无需填写')
            ->setCellValue('A21', '预约到店付定金比例')
            ->setCellValue('B21', '非必填')
            ->setCellValue('C21', '1，百分比，2，固定金额，默认 百分比，类型是规格时无需填写')
            ->setCellValue('A22', '预约到店百分比')
            ->setCellValue('B22', '非必填(数字)')
            ->setCellValue('C22', '只能填写1-100的整数包括1和100，默认20，类型是规格时无需填写')
            ->setCellValue('A23', '预约到店固定金额')
            ->setCellValue('B23', '非必填(数字)')
            ->setCellValue('C23', '只能填写小于售价的价格，默认0,如果大于售价，默认售价，类型是规格时无需填写')
            ->setCellValue('A24', '请在下面的表格填入数据，一行服务包括接下来的规格是一条数据')
            ->setCellValue('A25', '类型（必填:服务|规格）')
            ->setCellValue('B25', '名称（必填）')
            ->setCellValue('C25', '条码（非必填）')
            ->setCellValue('D25', '分类名（必填）')
            ->setCellValue('E25', '标签（必填）')
            ->setCellValue('F25', '售价（必填）')
            ->setCellValue('G25', '时长（必填）')
            ->setCellValue('H25', '规格1（必填）')
            ->setCellValue('I25', '规格2（非必填）')
            ->setCellValue('J25', '规格3（非必填）')
            ->setCellValue('K25', '网店展示（是|否）')
            ->setCellValue('L25', '上门服务（是|否）')
            ->setCellValue('M25', '预约上门定金（付定金）')
            ->setCellValue('N25', '上门付定金比例（百分比|固定金额）')
            ->setCellValue('O25', '上门百分比（0-100）')
            ->setCellValue('P25', '上门固额（金额，小于售价)')
            ->setCellValue('Q25', '预约到店定金（到店付|付定金)')
            ->setCellValue('R25', '到店定金比例（百分比|固定金额)')
            ->setCellValue('S25', '到店百分比（0-100)')
            ->setCellValue('T25', '到店固额（金额，小于售价)');

        $objPHPExcel->getActiveSheet()->setTitle('规格服务导入模版');               //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                                         //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="规格服务导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    //下载模版 非规格产品
    public function downloadNoSkuProduct()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(28);

        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A11')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A11')->getFont()->setSize(16);         //第二行字体大小
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:I1');
        $objPHPExcel->getActiveSheet()->mergeCells('A11:I11');
        for ($i = 2; $i < 11; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:I{$i}");
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '名称')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '最长支持255个字符')
            ->setCellValue('A3', '条码')
            ->setCellValue('B3', '非必填')
            ->setCellValue('C3', '不填则自动生成，最多14个字符')
            ->setCellValue('A4', '分类名')
            ->setCellValue('B4', '必填项。')
            ->setCellValue('C4', '分类列表中没有则自动添加，最多支持20个字符')
            ->setCellValue('A5', '标签')
            ->setCellValue('B5', '必填项')
            ->setCellValue('C5', '分类列表中没有则自动添加，多个标签以“/”分隔，每个标签最多支持20个字符')
            ->setCellValue('A6', '单位')
            ->setCellValue('B6', '必填项')
            ->setCellValue('C6', '产品单位')
            ->setCellValue('A7', '成本价')
            ->setCellValue('B7', '必填项(数字)。')
            ->setCellValue('C7', '单位：元，最多支持2位小数，不得超过1亿，例：98.98')
            ->setCellValue('A8', '售价')
            ->setCellValue('B8', '必填项(数字)。')
            ->setCellValue('C8', '单位：元，最多支持2位小数，不得超过1亿，例：198.98')
            ->setCellValue('A9', '运费')
            ->setCellValue('B9', '非必填项(数字)。')
            ->setCellValue('C9', '单位：元，最多支持2位小数，不得超过1亿，例：198.98,默认为0')
            ->setCellValue('A10', '网店出售')
            ->setCellValue('B10', '非必填')
            ->setCellValue('C10', '1，是，2，否，默认 是')
            ->setCellValue('A11', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A12', '名称（必填）')
            ->setCellValue('B12', '条码（非必填）')
            ->setCellValue('C12', '分类名（必填）')
            ->setCellValue('D12', '标签（必填）')
            ->setCellValue('E12', '单位（必填）')
            ->setCellValue('F12', '成本价（必填）')
            ->setCellValue('G12', '售价（必填）')
            ->setCellValue('H12', '运费（非必填）')
            ->setCellValue('I12', '网店出售（是|否）');


        $objPHPExcel->getActiveSheet()->setTitle('非规格产品导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="非规格产品导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    public function downloadSkuProduct()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(28);
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(28);

        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A15')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A15')->getFont()->setSize(16);         //第二行字体大小
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:M1');
        $objPHPExcel->getActiveSheet()->mergeCells('A15:M15');
        for ($i = 2; $i < 16; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:M{$i}");
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '类型')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '1，产品，2，规格，每一条产品数据后面的若干条规格数据是同一数据，其中规格必填项目为【类型、成本价、售价、规格1】，选填项目【条码、规格2、规格3】')
            ->setCellValue('A3', '名称')
            ->setCellValue('B3', '必填项。')
            ->setCellValue('C3', '最长支持255个字符')
            ->setCellValue('A4', '条码')
            ->setCellValue('B4', '非必填')
            ->setCellValue('C4', '不填则自动生成，最多14个字符')
            ->setCellValue('A5', '分类名')
            ->setCellValue('B5', '必填项。')
            ->setCellValue('C5', '分类列表中没有则自动添加，最多支持20个字符')
            ->setCellValue('A6', '标签')
            ->setCellValue('B6', '必填项')
            ->setCellValue('C6', '分类列表中没有则自动添加，多个标签以“/”分隔，每个标签最多支持20个字符')
            ->setCellValue('A7', '单位')
            ->setCellValue('B7', '必填项')
            ->setCellValue('C7', '产品单位')
            ->setCellValue('A8', '成本价')
            ->setCellValue('B8', '必填项(数字)。')
            ->setCellValue('C8', '单位：元，最多支持2位小数，不得超过1亿，例：98.98')
            ->setCellValue('A9', '售价')
            ->setCellValue('B9', '必填项(数字)。')
            ->setCellValue('C9', '单位：元，最多支持2位小数，不得超过1亿，例：198.98')
            ->setCellValue('A10', '运费')
            ->setCellValue('B10', '非必填项(数字)。')
            ->setCellValue('C10', '单位：元，最多支持2位小数，不得超过1亿，例：198.98,默认为0')
            ->setCellValue('A11', '网店出售')
            ->setCellValue('B11', '非必填')
            ->setCellValue('C11', '1，是，2，否，默认 是')
            ->setCellValue('A12', '规格1')
            ->setCellValue('B12', '必填')
            ->setCellValue('C12', '类型是服务时，是规格分类，类型是规格时，是规格名称，最多10个字符')
            ->setCellValue('A13', '规格2')
            ->setCellValue('B13', '选填')
            ->setCellValue('C13', '含义和‘规格1相同’，规格分类填写，规格也需要填写')
            ->setCellValue('A14', '规格3')
            ->setCellValue('B14', '选填')
            ->setCellValue('C14', '含义和‘规格1相同’，规格分类填写，规格也需要填写')
            ->setCellValue('A15', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A16', '类型（产品|规格）')
            ->setCellValue('B16', '名称（必填）')
            ->setCellValue('C16', '条码（非必填）')
            ->setCellValue('D16', '分类名（必填）')
            ->setCellValue('E16', '标签（必填）')
            ->setCellValue('F16', '单位（必填）')
            ->setCellValue('G16', '成本价（必填）')
            ->setCellValue('H16', '售价（必填）')
            ->setCellValue('I16', '运费（非必填）')
            ->setCellValue('J16', '网店出售（是|否）')
            ->setCellValue('K16', '规格1（必填）')
            ->setCellValue('L16', '规格2（选填）')
            ->setCellValue('M16', '规格3（选填）');


        $objPHPExcel->getActiveSheet()->setTitle('规格产品导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="规格产品导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    // 耗材
    public function downloadConsumableProduct()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(24);

        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A10')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A10')->getFont()->setSize(16);         //第二行字体大小
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:G1');
        $objPHPExcel->getActiveSheet()->mergeCells('A10:H10');
        for ($i = 2; $i < 10; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:H{$i}");
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删),仅支持导入服务耗材')
            ->setCellValue('A2', '名称')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '最长支持255个字符')
            ->setCellValue('A3', '条码')
            ->setCellValue('B3', '非必填')
            ->setCellValue('C3', '不填则自动生成，最多14个字符')
            ->setCellValue('A4', '分类名')
            ->setCellValue('B4', '必填项。')
            ->setCellValue('C4', '分类列表中没有则自动添加，最多支持20个字符')
            ->setCellValue('A5', '基本单位')
            ->setCellValue('B5', '必填项')
            ->setCellValue('C5', '产品单位，耗材在库存管理（采购、出入库、调拨等）使用，一般为大单位，例：瓶')
            ->setCellValue('A6', '成本价')
            ->setCellValue('B6', '必填项(数字)。')
            ->setCellValue('C6', '单位：元，最多支持2位小数，不得超过1亿，例：98.98')

            ->setCellValue('A7', '含量')
            ->setCellValue('B7', '必填项(数字)。')
            ->setCellValue('C7', '1个基本单位包含的耗用单位数量，正整数，最小填写1，例：一瓶洗发水500ml，基本单位填写“瓶”，含量填500，耗用单位填写“ml”')

            ->setCellValue('A8', '耗用单位')
            ->setCellValue('B8', '必填项')
            ->setCellValue('C8', '产品耗用单位，耗材消耗时使用的单位；一般为小单位，例：ml')

            ->setCellValue('A9', '耗材类型')
            ->setCellValue('B9', '必填项')
            ->setCellValue('C9', '服务耗材填“1”，院装填“2”，套盒填“3”；')

            ->setCellValue('A10', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A11', '名称（必填）')
            ->setCellValue('B11', '条码（非必填）')
            ->setCellValue('C11', '分类名（必填）')
            ->setCellValue('D11', '基本单位（必填）')
            ->setCellValue('E11', '成本价（必填）')
            ->setCellValue('F11', '含量（必填|数字）')
            ->setCellValue('G11', '耗用库存（必填）')
            ->setCellValue('H11', '耗材类型（必填）');


        $objPHPExcel->getActiveSheet()->setTitle('耗材导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="耗材导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }


    // 上传文件
    public function upLoadfile()
    {
        $Request = Request::instance();
        $file = $Request->file('file');
        if ($file) {
            $file_types = explode(".", $_FILES ['file'] ['name']); // ["name"] => string(25) "excel文件名.xls"

            $file_type = $file_types [count($file_types) - 1];//xls后缀

            $file_name = $file_types [count($file_types) - 2];//xls去后缀的文件名
            /*判别是不是.xls文件，判别是不是excel文件*/
            if (strtolower($file_type) != "xls" && strtolower($file_type) != "xlsx") {
                return $this->ajaxFail('不是Excel文件，重新上传');
            }
            $user = session('userinfo');
            $str = $user['merchantid'] . '_' . $user['storeid'];
            $file_name = 'FGGFWDR';
            $file_name = md5($file_name);
            $fileName = $file_name . '_' . $str . '.' . $file_type;
            //移到/public/uploads/excel/下
            $info = $file->move('upload' . DS . 'excel', $file_name . '_' . $str);
            $type = input('post.type');
            if ($info) {
                vendor('PHPExcel.Classes.PHPExcel');
                vendor('PHPExcel.Classes.PHPExcel.PHPExcel_IOFactory');
                vendor('PHPExcel.Classes.PHPExcel.PHPExcel_Cell');
                $file_path = 'upload/excel/' . $fileName;//上传后的EXCEL路径
                if ($type == 1) {
                    $re = $this->actionRead($file_path, 'utf-8', 19);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'service_name',         //  服务名称
                        'bar_code',             //   条形码 14位
                        'classification_id',   //  分类id
                        'label_id',             //  标签id，多选时已英文逗号分隔
                        'price',                //  售价，以分为单位，（未开启规格时的价格）
                        'duration',             //  服务时长 以分（时间分）为单位
                        'shop_display',         //  网店展示 1展示 2不展示
                        'door_service',         //  上门服务 2开启 1关闭
                        'pre_door',             //  预约上门 1付全款 2付定金
                        'pre_door_mode',        //  上门支付 1百分比支付 2 固定金额支付
                        'door_percentage',      //  上门百分比支付
                        'door_fixed_amount',    //  上门固定金额支付
                        'pro_shop',             //   预约到店 1付全款 2到店付 3付定金
                        'pro_shop_mode',        //   到店支付 1百分比支付  2固定金额支付
                        'shop_percentage',      //   到店百分比支付
                        'shop_fixed_amount'    //   到店固定金额
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            } else {
                                switch ($k) {
                                    case 6:
                                        $v = '否';
                                        break;
                                    case 7:
                                        $v = '否';
                                        break;
                                    case 8:
                                        $v = '付定金';
                                        break;
                                    case 9:
                                        $v = '百分比';
                                        break;
                                    case 10:
                                        $v = 20;
                                        break;
                                    case 11:
                                        $v = 0;
                                        break;
                                    case 12:
                                        $v = '到店付';
                                        break;
                                    case 13:
                                        $v = '百分比';
                                        break;
                                    case 14:
                                        $v = 20;
                                        break;
                                    case 15:
                                        $v = 0;
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                } elseif ($type == 2) {
                    $re = $this->actionRead($file_path, 'utf-8', 25);
                    array_splice($re, 1, 0);
                    $keys = array(
                        "serviceType",          // 类型
                        'service_name',         //  服务名称
                        'bar_code',             //   条形码 14位
                        'classification_id',   //  分类id
                        'label_id',             //  标签id，多选时已英文逗号分隔
                        'price',                //  售价，以分为单位，（未开启规格时的价格）
                        'duration',             //  服务时长 以分（时间分）为单位
                        'sku1',                 //  规格1
                        'sku2',                 //  规格2
                        'sku3',                 //  规格2
                        'shop_display',         //  网店展示 1展示 2不展示
                        'door_service',         //  上门服务 2开启 1关闭
                        'pre_door',             //  预约上门 1付全款 2付定金
                        'pre_door_mode',        //  上门支付 1百分比支付 2 固定金额支付
                        'door_percentage',      //  上门百分比支付
                        'door_fixed_amount',    //  上门固定金额支付
                        'pro_shop',             //   预约到店 1付全款 2到店付 3付定金
                        'pro_shop_mode',        //   到店支付 1百分比支付  2固定金额支付
                        'shop_percentage',      //   到店百分比支付
                        'shop_fixed_amount'    //   到店固定金额
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if ($k == 0) {
                                continue;
                            }
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            } else {
                                // 服务
                                if ($vals[0] == '服务') {
                                    // 服务
                                    $num = $k - 4;
                                    switch ($num) {
                                        case 6:
                                            $v = '否';
                                            break;
                                        case 7:
                                            $v = '否';
                                            break;
                                        case 8:
                                            $v = '付定金';
                                            break;
                                        case 9:
                                            $v = '百分比';
                                            break;
                                        case 10:
                                            $v = 20;
                                            break;
                                        case 11:
                                            $v = 0;
                                            break;
                                        case 12:
                                            $v = '到店付';
                                            break;
                                        case 13:
                                            $v = '百分比';
                                            break;
                                        case 14:
                                            $v = 20;
                                            break;
                                        case 15:
                                            $v = 0;
                                            break;
                                        default:
                                            break;
                                    }
                                } else {
                                    // 规格
                                    switch ($k) {
                                        case 2:
                                        case 5:
                                        case 7:
                                        case 8:
                                        case 9:
                                            break;
                                        default:
                                            $v = '';
                                            break;
                                    }
                                }
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                } elseif ($type == 3) {
                    $re = $this->actionRead($file_path, 'utf-8', 12);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'product_name',         //  产品名称
                        'product_barcode',     //   条形码 14位
                        'product_classid',     //  分类id
                        'product_labelids',    //  标签id，多选时已英文逗号分隔
                        'unit',                 //  单位
                        'cost_price',           //  成本价
                        'price',                 //  售价
                        'freight',              //  运费
                        'sell_online',           //  网店出售
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if ($k == 0) {
                                continue;
                            }
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            } else {
                                switch ($k) {
                                    case 8:
                                        $v = '是';
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                } elseif ($type == 4) {
                    $re = $this->actionRead($file_path, 'utf-8', 16);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'productType',          // 类型
                        'product_name',         //  产品名称
                        'product_barcode',     //   条形码 14位
                        'product_classid',     //  分类id
                        'product_labelids',    //  标签id，多选时已英文逗号分隔
                        'unit',                 //  单位
                        'cost_price',           //  成本价
                        'price',                //  售价
                        'freight',              //  运费
                        'sell_online',          //  网店出售
                        'sku1',                  //规格1
                        'sku2',                   //规格2
                        'sku3',                 //规格3
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if ($k == 0) {
                                continue;
                            }
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            } else {
                                switch ($k) {
                                    case 9:
                                        $v = '是';
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                } elseif ($type == 5) {
                    $re = $this->actionRead($file_path, 'utf-8', 11);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'product_name',         //  产品名称
                        'product_barcode',     //   条形码 14位
                        'product_classid',     //  分类id
                        'unit',                 //  单位
                        'cost_price',                 //  成本价
                        'small_content',
                        'small_unit',
                        'consumable_class'
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if ($k == 0) {
                                continue;
                            }
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                }
                $addData = array(
                    'merchantid' => $user['merchantid'],
                    'addtime' => time(),
                    'total_num' => count($data) - 1,
                    'success_num' => 0,
                    'error_num' => 0,
                    'info' => json_encode($data),
                    'isimport' => 0,
                    'type' => $type,
                    'admin' => $user['nickname'],
                    'department' => $user['storetag'],
                    'position' => $user['group_name']
                );
                $res = db('goods_import')->insert($addData);
                if ($res) {
                    echo json_encode($this->ajaxSuccess($data, '上传成功'));
                } else {
                    echo json_encode($this->ajaxFail('上传失败'));
                }
            } else {
                echo json_encode($this->ajaxFail('上传失败'));
            }
        }
    }

    public function actionRead($filename, $encode = 'utf-8', $minRow)
    {
        $objPHPE = new \PHPExcel();
        $file_types = explode(".", $filename);
        $file_type = $file_types [count($file_types) - 1];//xls后缀
        if ($file_type == 'xlsx') {
            $PHPReader = new \PHPExcel_Reader_Excel2007($objPHPE);//实例化PHPExcel类
        } else {
            $PHPReader = new \PHPExcel_Reader_Excel5($objPHPE);//实例化PHPExcel类
        }
        $PHPReader->setReadDataOnly(true);
        $objPHPExcel = $PHPReader->load($filename);
        $objWorksheet = $objPHPExcel->getActiveSheet();
        $highestRow = $objWorksheet->getHighestRow();
        $highestColumn = $objWorksheet->getHighestColumn();
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
        $excelData = array();
        for ($row = $minRow; $row <= $highestRow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $excelData[$row][] = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            }
        }
        return $excelData;
    }

    /*-----------------------------------权益导入-------------------------------------*/
    public function equity()
    {
        $this->outPut();
    }

    // 下载服务条码
    public function downloadServiceCode()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '服务名称')
            ->setCellValue('B1', '服务条码');
        $ServiceModel = new ServiceModel;
        $map = array(
            'merchant_id' => $this->userinfo['merchantid'],
            'status' => array('in', array(1, 2))
        );
        $serviceData = $ServiceModel->getAll($map, 'addtime desc', 'id,service_name as name,bar_code as code');
        if ($serviceData && is_object($serviceData)) {
            $serviceData = $serviceData->toArray();
        }
        $serviceData = $serviceData ? $serviceData : array();

        foreach ($serviceData as $k => $v) {
            $index = $k + 2;
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $index, $v['name']);
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $index, $v['code']);
        }
        $objPHPExcel->getActiveSheet()->setTitle('服务条码信息');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="服务条码信息.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    // 下载会员信息
    public function downloadMemberInfo()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '会员名称')
            ->setCellValue('B1', '会员备注名')
            ->setCellValue('C1', '会员手机号');
        $MemberModel = new MemberModel;
        $map = array(
            'merchantid' => $this->userinfo['merchantid']
//            'status'=>array('in',array(1,2))
        );
        $serviceData = $MemberModel->getAll($map, 'id,member_name as name,remarks_name as remark,phone');
        if ($serviceData && is_object($serviceData)) {
            $serviceData = $serviceData->toArray();
        }
        $serviceData = $serviceData ? $serviceData : array();

        foreach ($serviceData as $k => $v) {
            $index = $k + 2;
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $index, $v['name'] . ' ');
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $index, $v['remark'] . ' ');
            $objPHPExcel->getActiveSheet()->setCellValue('C' . $index, $v['phone']);
        }
        $objPHPExcel->getActiveSheet()->setTitle('会员信息');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员信息.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    // 下载次数导入权益
    public function downloadOnceEquity()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(100);

        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A8')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A8')->getFont()->setSize(16);         //第二行字体大小

        $objPHPExcel->getActiveSheet()->getStyle('D')->getNumberFormat()
            ->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
        $objPHPExcel->getActiveSheet()->getStyle('E')->getNumberFormat()
            ->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);

        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:E1');
        $objPHPExcel->getActiveSheet()->mergeCells('A8:E8');
        for ($i = 2; $i < 8; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:E{$i}");
        }
        $StoreModel = new StoreModel;
        $map = array(
            'merchantid' => $this->userinfo['merchantid'],
            'status' => 1
        );
        $StoreList = $StoreModel->getAll($map, 'id,storetag as name');
        if ($StoreList && is_object($StoreList)) {
            $StoreList = $StoreList->toArray();
        }
        $StoreList = $StoreList ? $StoreList : array();
        $storeString = '';
        foreach ($StoreList as $k => $v) {
            $storeString .= ' ' . $v['id'] . ':' . $v['name'] . ';';
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '手机号')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '请填写正确手机号。如果会员信息中未找到手机号，则该条数据不会被导入，请先导入“会员”创建会员后再进行权益导入')
            ->setCellValue('A3', '服务条形码')
            ->setCellValue('B3', '必填项。')
            ->setCellValue('C3', '请填写服务条形码。服务条形码为权益导入时服务唯一识别方式。例：面部清洁条码为“f001002”,填写f001002')
            ->setCellValue('A4', '次数')
            ->setCellValue('B4', '必填')
            ->setCellValue('C4', '请填入正整数，例:导入面部清洁2次，填写2')
            ->setCellValue('A5', '有效期')
            ->setCellValue('B5', '必填项。')
            ->setCellValue('C5', '可填写数字（或固定日期），永久有效填0,天数最大可填写3650,固定日期需大于当前日期；例：100，表示从导入时开始计算的100天有效，2020-01-01，表示有效期到2020-01-01日')
            ->setCellValue('A6', '可用店铺')
            ->setCellValue('B6', '必填项')
            ->setCellValue('C6', '填0，表示所有店铺，其他填写店铺标识，多个店铺标识以“/”分隔开;例：标识（1：A店，2：B店，3：C店），可用店铺为A店+B店，填 1/2 ')
            ->setCellValue('A7', '店铺标识')
            ->setCellValue('B7', '提示项')
            ->setCellValue('C7', '店铺标识：' . $storeString)
            ->setCellValue('A8', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A9', '手机号(必填)')
            ->setCellValue('B9', '服务条形码（必填）')
            ->setCellValue('C9', '次数（必填）')
            ->setCellValue('D9', '有效期（必填）')
            ->setCellValue('E9', '可用店铺（必填）');


        $objPHPExcel->getActiveSheet()->setTitle('会员次数权益导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员次数权益导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    // 下载折扣导入权益
    public function downloadDiscountEquity()
    {
        $path = dirname(__FILE__); //找到当前脚本所在路径
        vendor("PHPExcel.Classes.PHPExcel");
        vendor("PHPExcel.Classes.PHPExcel.Writer.IWriter");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Abstract");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel5");
        vendor("PHPExcel.Classes.PHPExcel.Writer.Excel2007");
        vendor("PHPExcel.Classes.PHPExcel.IOFactory");
        $objPHPExcel = new \PHPExcel();
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);
        // 设置表头信息
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(24);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(100);

        //$objPHPExcel->getActiveSheet()->getDefaultStyle()->getFont()->setSize(12);      //字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A2:D2')->getFont()->setBold(false); //第二行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A8')->getFont()->setBold(true);      //第一行是否加粗
        $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(16);         //第一行字体大小
        $objPHPExcel->getActiveSheet()->getStyle('A8')->getFont()->setSize(16);         //第二行字体大小

        $objPHPExcel->getActiveSheet()->getStyle('D')->getNumberFormat()
            ->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
        $objPHPExcel->getActiveSheet()->getStyle('E')->getNumberFormat()
            ->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
        // 合并表格
        $objPHPExcel->getActiveSheet()->mergeCells('A1:E1');
        $objPHPExcel->getActiveSheet()->mergeCells('A8:E8');
        for ($i = 2; $i < 8; $i++) {
            $objPHPExcel->getActiveSheet()->mergeCells("C{$i}:E{$i}");
        }
        $StoreModel = new StoreModel;
        $map = array(
            'merchantid' => $this->userinfo['merchantid'],
            'status' => 1
        );
        $StoreList = $StoreModel->getAll($map, 'id,storetag as name');
        if ($StoreList && is_object($StoreList)) {
            $StoreList = $StoreList->toArray();
        }
        $StoreList = $StoreList ? $StoreList : array();
        $storeString = '';
        foreach ($StoreList as $k => $v) {
            $storeString .= ' ' . $v['id'] . ':' . $v['name'] . ';';
        }
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A1', '模板使用须知(勿删)')
            ->setCellValue('A2', '手机号')
            ->setCellValue('B2', '必填项。')
            ->setCellValue('C2', '请填写正确手机号。如果会员信息中未找到手机号，则该条数据不会被导入，请先导入“会员”创建会员后再进行权益导入')
            ->setCellValue('A3', '服务条形码')
            ->setCellValue('B3', '必填项。')
            ->setCellValue('C3', '请填写服务条形码。服务条形码为权益导入时服务唯一识别方式。例：面部清洁条码为“f001002”,填写f001002')
            ->setCellValue('A4', '折扣')
            ->setCellValue('B4', '必填项。')
            ->setCellValue('C4', '请填入0-10之间(包括10)的数字（最多2位小数），例:导入面部清洁 8.88 折，填写 8.88 ')
            ->setCellValue('A5', '有效期')
            ->setCellValue('B5', '必填项。')
            ->setCellValue('C5', '可填写数字（或固定日期），永久有效填0,天数最大可填写3650,固定日期需大于当前日期；例：100，表示从导入时开始计算的100天有效，2020-01-01，表示有效期到2020-01-01日')
            ->setCellValue('A6', '可用店铺')
            ->setCellValue('B6', '必填项')
            ->setCellValue('C6', '填0，表示所有店铺，其他填写店铺标识，多个店铺标识以“/”分隔开;例：标识（1：A店，2：B店，3：C店），可用店铺为A店+B店，填 1/2 ')
            ->setCellValue('A7', '店铺标识')
            ->setCellValue('B7', '提示项')
            ->setCellValue('C7', '店铺标识：' . $storeString)
            ->setCellValue('A8', '请在下面的表格填入数据，一行为一条数据')
            ->setCellValue('A9', '手机号(必填)')
            ->setCellValue('B9', '服务条形码（必填）')
            ->setCellValue('C9', '折扣（必填）')
            ->setCellValue('D9', '有效期（必填）')
            ->setCellValue('E9', '可用店铺（必填）');


        $objPHPExcel->getActiveSheet()->setTitle('会员折扣权益导入模版');      //设置sheet的名称
        $objPHPExcel->setActiveSheetIndex(0);                   //设置sheet的起始位置
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');   //通过PHPExcel_IOFactory的写函数将上面数据写出来
        $PHPWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        header('Content-Disposition: attachment;filename="会员折扣权益导入模版.xlsx"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $PHPWriter->save("php://output"); //表示在$path路径下面生成demo.xlsx文件
    }

    // 上传文件
    public function upLoadEquityFile()
    {
        $Request = Request::instance();
        $file = $Request->file('file');
        if ($file) {
            $file_types = explode(".", $_FILES ['file'] ['name']); // ["name"] => string(25) "excel文件名.xls"

            $file_type = $file_types [count($file_types) - 1];//xls后缀

            $file_name = $file_types [count($file_types) - 2];//xls去后缀的文件名
            /*判别是不是.xls文件，判别是不是excel文件*/
            if (strtolower($file_type) != "xls" && strtolower($file_type) != "xlsx") {
                return $this->ajaxFail('不是Excel文件，重新上传');
            }
            $user = session('userinfo');
            $str = $user['merchantid'] . '_' . $user['storeid'];
            $file_name = 'FGGFWDR';
            $file_name = md5($file_name);
            $fileName = $file_name . '_' . $str . '.' . $file_type;
            //移到/public/uploads/excel/下
            $info = $file->move('upload' . DS . 'excel', $file_name . '_' . $str);
            $type = input('post.type');
            if ($info) {
                vendor('PHPExcel.Classes.PHPExcel');
                vendor('PHPExcel.Classes.PHPExcel.PHPExcel_IOFactory');
                vendor('PHPExcel.Classes.PHPExcel.PHPExcel_Cell');
                $file_path = 'upload/excel/' . $fileName;//上传后的EXCEL路径
				/*$re = $this->actionRead($file_path, 'utf-8', 2);
				$str = $re[3][41];
				$str = str_replace(" ",'',$str);
				$arr = explode("\n", $str);
				$newArr = [];
				$skuArr = [];
				foreach ($arr as $k=>$v){
					if($k==0){
						$newArr['skuTitle'] = explode("；",$v);
					}else{
						$skuArr[] = explode("；",$v);
					}
				}
				$newArr['skuArr'] = $skuArr;
				pre([$str,$arr,$newArr]);*/
                if ($type == 6) {
                    $re = $this->actionRead($file_path, 'utf-8', 9);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'phone',            //  手机号
                        'code',             //   服务条形码 14位
                        'num',              //  次数
                        'indate',           //  有效期
                        'canuserstore',    //  可用门店
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            }
                            if ($k == 3) {
                                $v = $this->excelTime($v);
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                } else if ($type == 7) {
                    $re = $this->actionRead($file_path, 'utf-8', 9);
                    array_splice($re, 1, 0);
                    $keys = array(
                        'phone',            //  手机号
                        'code',             //   服务条形码 14位
                        'discount',         //  次数
                        'indate',           //  有效期
                        'canuserstore',    //  可用门店
                    );
                    $data = array();
                    foreach ($re as $i => $vals) {
                        $isNotNull = false;
                        foreach ($vals as $k => &$v) {
                            if (is_string($v) || is_numeric($v)) {
                                $v = trim(strval($v));
                            }
                            if ($v) {
                                $isNotNull = true;
                            }
                            if ($k == 3) {
                                $v = $this->excelTime($v);
                            }
                        }
                        if ($isNotNull) {
                            $data[] = array_combine($keys, $vals);
                        }
                    }
                }
                $addData = array(
                    'merchantid' => $user['merchantid'],
                    'addtime' => time(),
                    'total_num' => count($data) - 1,
                    'success_num' => 0,
                    'error_num' => 0,
                    'info' => json_encode($data),
                    'isimport' => 0,
                    'type' => $type,
                    'admin' => $user['nickname'],
                    'department' => $user['storetag'],
                    'position' => $user['group_name']
                );
                $res = db('goods_import')->insert($addData);
                if ($res) {
                    echo json_encode($this->ajaxSuccess($data, '上传成功'));
                } else {
                    echo json_encode($this->ajaxFail('上传失败'));
                }
            } else {
                echo json_encode($this->ajaxFail('上传失败'));
            }
        }
    }

    // 次数权益导入
    public function onceEquityImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 6
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $addCommon = array(
            'merchantid' => $userInfo['merchantid'],
            'isgive' => 2,
            'card_type' => 1,
            'once_cardtype' => 1,
            'usenum' => 0,
            'addtime' => time(),
            'goods_type' => 1,        //  类型：1，服务，2，产品
            'memberid' => 0,          //  会员id
            'card_id' => 0,           //  卡项id
            'membercard_id' => 0,     // 卡项数据表 id（member_card）
            'goods_id' => 0,          //  服务或者商品id，按type分类
            'indate' => 0,              //  有效期
        );
        $successNum = 0;
        $errorNum = 0;
        $newData = array();
        $error = array();
        $MemberCardDetailModel = new MemberCardDetailModel;
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            $error = array();
            $memberId = $this->phone2memberId($v['phone']);
            if (!$memberId) {
                $error[] = '会员信息不存在(手机号码有误)';
            }
            $serviceId = $this->code2serviceId($v['code']);
            if (!$serviceId) {
                $error[] = '服务信息不存在(服务条码有误)';
            }
            if ($memberId) {
                $membercardId = $this->memberId2memberCardId($memberId);
                if (!$membercardId) {
                    $error[] = '权益导入失败';
                }
            } else {
                $membercardId = 0;
            }
            $indate = $this->indate2time($v['indate']);
            if ($indate === false || $indate < 0) {
                $error[] = '有效期填写有误';
            }
            if (is_numeric($v['num'])) {
                $num = intval($v['num']);
            } else {
                $num = false;
            }
            if (!$num) {
                $error[] = '次数有误';
            }
            $canuserstore = 0;
            if ($v['canuserstore'] === 0 || $v['canuserstore'] === '0') {
                $canuserstore = -1;
            } else {
                if (!$v['canuserstore']) {
                    $canuserstore = false;
                } else {
                    $arr = explode('/', $v['canuserstore']);
                    $canuserstore = implode(',', $arr);
                }
            }
            if ($canuserstore === false) {
                $error[] = '可用店铺有误';
            }
            if (count($error) > 0) {
                $v['error'] = $error;
                $errorNum++;
                continue;
            }
            $item = $addCommon;
            // 重置导入信息
            $item['memberid'] = $memberId;
            $item['card_id'] = -1;
            $item['membercard_id'] = $membercardId;
            $item['goods_id'] = $serviceId;
            $item['indate'] = $indate;
            $item['canuserstore'] = $canuserstore;
            $item['num'] = $num;
            $res = $MemberCardDetailModel->addData($item);
            if ($res) {
                $v['error'] = false;
                $successNum++;
            } else {
                $errorNum++;
                $error[] = '权益导入失败';
                $v['error'] = $error;
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }


    // 折扣权益导入
    public function discountEquityImport()
    {
        $userInfo = $this->userinfo;
        $id = input('post.id');
        $GoodsImportModel = new GoodsimportModel;
        $where = array(
            'id' => $id,
            'merchantid' => $userInfo['merchantid'],
            'isimport' => 0,
            'type' => 7
        );
        $goodsImport = $GoodsImportModel->getFindData(array('map' => $where));
        if (!$goodsImport || !$goodsImport['info']) {
            return $this->ajaxFail('未找到需导入数据');
        }
        $info = $goodsImport['info'];
        // 循环数据生成可入库的数据
        $merchantid = $userInfo['merchantid'];
        $addCommon = array(
            'merchantid' => $userInfo['merchantid'],
            'isgive' => 2,
            'card_type' => 2,
            'once_cardtype' => 1,
            'usenum' => 0,
            'addtime' => time(),
            'goods_type' => 1,        //  类型：1，服务，2，产品
            'memberid' => 0,          //  会员id
            'card_id' => 0,           //  卡项id
            'membercard_id' => 0,     // 卡项数据表 id（member_card）
            'goods_id' => 0,          //  服务或者商品id，按type分类
            'indate' => 0,              //  有效期
        );
        $successNum = 0;
        $errorNum = 0;
        $newData = array();
        $error = array();
        $MemberCardDetailModel = new MemberCardDetailModel;
        foreach ($info as $k => &$v) {
            if ($k == 0) {
                continue;
            }
            $error = array();
            $memberId = $this->phone2memberId($v['phone']);
            if (!$memberId) {
                $error[] = '会员信息不存在(手机号码有误)';
            }
            $serviceId = $this->code2serviceId($v['code']);
            if (!$serviceId) {
                $error[] = '服务信息不存在(服务条码有误)';
            }
            if ($memberId) {
                $membercardId = $this->memberId2memberCardId($memberId, 2);
                if (!$membercardId) {
                    $error[] = '权益导入失败';
                }
            } else {
                $membercardId = 0;
            }
            $indate = $this->indate2time($v['indate']);
            if ($indate === false || $indate < 0) {
                $error[] = '有效期填写有误';
            }
            if (is_numeric($v['discount'])) {
                $num = round($v['discount'], 2);
                if ($num <= 0 || $num > 10) {
                    $num = false;
                }
            } else {
                $num = false;
            }
            if (!$num) {
                $error[] = '折扣有误';
            }
            $canuserstore = 0;
            if ($v['canuserstore'] === 0 || $v['canuserstore'] === '0') {
                $canuserstore = -1;
            } else {
                if (!$v['canuserstore']) {
                    $canuserstore = false;
                } else {
                    $arr = explode('/', $v['canuserstore']);
                    $canuserstore = implode(',', $arr);
                }
            }
            if ($canuserstore === false) {
                $error[] = '可用店铺有误';
            }
            if (count($error) > 0) {
                $v['error'] = $error;
                $errorNum++;
                continue;
            }
            $item = $addCommon;
            // 重置导入信息
            $item['memberid'] = $memberId;
            $item['card_id'] = -1;
            $item['membercard_id'] = $membercardId;
            $item['goods_id'] = $serviceId;
            $item['indate'] = $indate;
            $item['canuserstore'] = $canuserstore;
            $item['discount'] = $num;
            $res = $MemberCardDetailModel->addData($item);
            if ($res) {
                $v['error'] = false;
                $successNum++;
            } else {
                $errorNum++;
                $error[] = '权益导入失败';
                $v['error'] = $error;
            }
        }
        $editData = array(
            'id' => $goodsImport['id'],
            'info' => json_encode($info),
            'success_num' => $successNum,
            'error_num' => $errorNum,
            'isimport' => 1
        );
        $res = $GoodsImportModel->editData($editData);
        $data = array(
            'successNum' => $successNum,
            'errorNum' => $errorNum
        );
        return $this->ajaxSuccess($data);
    }

    // 手机号转会员id
    protected $memberData = array();

    private function phone2memberId($phone)
    {
        $memberData = $this->memberData;
        foreach ($memberData as $k => $v) {
            if ($v['phone'] == $phone) {
                return $v['id'];
            }
        }
        $map = array(
            'phone' => $phone,
            'merchantid' => $this->userinfo['merchantid']
        );
        $MemberModel = new MemberModel;
        $data = $MemberModel->getFinds($map, 'id,phone');
        if ($data && is_object($data)) {
            $data = $data->toArray();
        }
        if ($data && isset($data['id'])) {
            $this->memberData[] = array(
                'phone' => $phone,
                'id' => $data['id']
            );
            return $data['id'];
        } else {
            $this->memberData[] = array(
                'phone' => $phone,
                'id' => false
            );
            return false;
        }
    }

    // 条码转服务id
    protected $serviceData = array();

    private function code2serviceId($code)
    {
        $serviceData = $this->serviceData;
        foreach ($serviceData as $k => $v) {
            if ($v['code'] == $code) {
                return $v['id'];
            }
        }
        $map = array(
            'bar_code' => array('in', $code),
            'merchant_id' => $this->userinfo['merchantid']
        );
        $ServiceModel = new ServiceModel;
        $data = $ServiceModel->getFind($map, 'id,bar_code as code');
        if ($data && is_object($data)) {
            $data = $data->toArray();
        }
        if ($data && isset($data['id'])) {
            $this->serviceData[] = array(
                'code' => $code,
                'id' => $data['id']
            );
            return $data['id'];
        } else {
            $this->serviceData[] = array(
                'code' => $code,
                'id' => false
            );
            return false;
        }
    }

    private function indate2time($indate)
    {
        if (is_numeric($indate)) {
            if ($indate > 3650) {
                return false;
            } else if ($indate == 0) {
                return 0;
            }
            return strtotime(date('Y-m-d')) + 86400 * $indate;
        } else {
            // 判断字符串是否是合法的年月日
            if ($indate != date('Y-m-d', strtotime($indate))) {
                return false;
            }
            return strtotime($indate);
        }
    }

    protected $memberCardId = array();

    // 会员id 找 会员导入卡项id ，没有则添加
    private function memberId2memberCardId($memberId, $type = 1)
    {
        $memberCardId = $this->memberCardId;
        foreach ($memberCardId as $k => $v) {
            if ($v['memberid'] == $memberId) {
                return $v['id'];
            }
        }
        $MemberCardModel = new MemberCardModel;
        $map = array(
            'merchantid' => $this->userinfo['merchantid'],
            'memberid' => $memberId,
            'cardid' => -1,
            'cardtype'=>$type,              // 卡项类型：1，次卡，2，充值卡。。。
        );
        $data = $MemberCardModel->getFind($map, 'id');
        if ($data && is_object($data)) {
            $data = $data->toArray();
        }
        if ($data && isset($data['id'])) {
            $this->memberCardId[] = array(
                'memberid' => $memberId,
                'id' => $data['id']
            );
            return $data['id'];
        } else {
            if ($type == 1) {
                $addData = array(
                    'merchantid' => $this->userinfo['merchantid'],
                    'memberid' => $memberId,
                    'cardid' => -1,
                    'storeid' => 0,
                    'acquire_type' => 4,                        //  卡项获取类型，1，购买，2，接收礼品卡，3，商家赠送
                    'presenter' => 0,                           //  赠送人
                    'cardtype' => 1,
                    'card_name' => '其他权益次卡',
                    'buytime' => time(),
                    'buyprice' => 0,                            // 购买价格
                    'payid' => 0,                               // 支付流水的id
                    'order_id' => 0,                            // 购卡时 订单的id
                    'status' => 1,
                    'failcause' => '',
                    'activatetime' => '',
                    'isactivate' => 1,                          //1，激活，2，未激活。
                    'indate' => 0,
                    'once_cardtype' => 1,
                    'allnum' => 0,
                    'canusestore' => -1,
                    'usebalance' => 0,
                    'totalbalance' => 0,
                    'capitalbalance' => 0,
                    'presentbalance' => 0
                );
            } else {
                $addData = array(
                    'merchantid' => $this->userinfo['merchantid'],
                    'memberid' => $memberId,
                    'cardid' => -1,
                    'storeid' => 0,
                    'acquire_type' => 4,                        //  卡项获取类型，1，购买，2，接收礼品卡，3，商家赠送
                    'presenter' => 0,                           //  赠送人
                    'cardtype' => 2,
                    'card_name' => '其他权益折扣卡',
                    'buytime' => time(),
                    'buyprice' => 0,                            // 购买价格
                    'payid' => 0,                               // 支付流水的id
                    'order_id' => 0,                            // 购卡时 订单的id
                    'status' => 1,
                    'failcause' => '',
                    'activatetime' => '',
                    'isactivate' => 1,                          //1，激活，2，未激活。
                    'indate' => 0,
                    'once_cardtype' => 1,
                    'allnum' => 0,
                    'canusestore' => -1,
                    'usebalance' => 0,
                    'totalbalance' => 0,
                    'capitalbalance' => 0,
                    'presentbalance' => 0
                );
            }
            $id = $MemberCardModel->notCommitAdd($addData, array());
            $this->memberCardId[] = array(
                'memberid' => $memberId,
                'id' => $id
            );
            return $id;
        }
    }

    public function excelTime($date, $time = false)
    {
        if (function_exists('GregorianToJD')) {
            if ($date <= 3650) {
                return $date;
            }
            if (is_numeric($date)) {
                $jd = GregorianToJD(1, 1, 1970);
                $gregorian = JDToGregorian($jd + intval($date) - 25569);
                $date = explode('/', $gregorian);
                $date_str = str_pad($date [2], 4, '0', STR_PAD_LEFT)
                    . "-" . str_pad($date [0], 2, '0', STR_PAD_LEFT)
                    . "-" . str_pad($date [1], 2, '0', STR_PAD_LEFT)
                    . ($time ? " 00:00:00" : '');
                return $date_str;
            }
        } else {
            if ($date <= 3650) {
                return $date;
            }
            $date = $date > 25568 ? $date + 1 : 25569;
            /*There was a bug if Converting date before 1-1-1970 (tstamp 0)*/
            $ofs = (70 * 365 + 17 + 2) * 86400;
            $date = date("Y-m-d", ($date * 86400) - $ofs) . ($time ? " 00:00:00" : '');
        }
        return $date;
    }

    public function phone2member()
    {
        $phone = input('post.phone');
        $phone = json_decode(htmlspecialchars_decode($phone), true);
//        return $this->ajaxSuccess($phone);
        $map = array(
            'phone' => array('in', $phone),
            'merchantid' => $this->userinfo['merchantid']
        );
        $MemberModel = new MemberModel;
        $data = $MemberModel->getAll($map, 'id,member_name as name,phone');
        if ($data) {
            return $this->ajaxSuccess($data);
        } else {
            return $this->ajaxFail();
        }
    }

    public function code2service()
    {
        $code = input('post.code');
        $code = json_decode(htmlspecialchars_decode($code), true);
        $map = array(
            'bar_code' => array('in', $code),
            'merchant_id' => $this->userinfo['merchantid']
        );
        $ServiceModel = new ServiceModel;
        $data = $ServiceModel->getAll($map, 'addtime desc', 'id,service_name as name,bar_code as code');
        if ($data) {
            return $this->ajaxSuccess($data);
        } else {
            return $this->ajaxFail();
        }
    }
}