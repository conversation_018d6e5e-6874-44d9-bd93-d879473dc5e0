<!--[meiye_09_01]-->
<div class="layui-fluid">
  <div id="productList" v-cloak style="height: 100%">
    <!--分类标签管理-->
    <el-dialog
      :title="labelclass==1?'产品分类管理':'产品标签管理'"
      :visible.sync="labelClassDialog"
      append-to-body
    >
      <el-popover
        placement="right"
        width="200"
        trigger="click"
        v-model="showadd"
      >
        <el-form ref="form" :model="labelclassData" size="small" label-width="">
          <el-form-item label="">
            <el-col :span="24">
              <el-input placeholder="输入名称" v-model="labelclassData.name">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              :loading="addnow"
              @click="confirmAdd"
              >确定</el-button
            >
            <el-button type="primary" size="small" @click="showadd = false"
              >取消</el-button
            >
          </el-form-item>
        </el-form>
        <el-button type="primary" size="small" slot="reference"
          ><span v-text="labelclass==1?'添加分类':'添加标签'"></span
        ></el-button>
      </el-popover>
      <el-button type="primary" size="small" @click="shw=!shw"
        ><span v-text="shw?'完成':'编辑'"></span
      ></el-button>
      <el-table :data="labelclassTableData" style="width: 100%">
        <el-table-column prop="name" label="名称">
          <template slot-scope="scope">
            <el-input
              v-if="shw"
              v-model="scope.row.name"
              size="medium"
            ></el-input>
            <span v-else>{{scope.row.name}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="s_addtime" label="创建时间"> </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="delLabelClass(scope.row.id)"
              >删除
            </el-button>
            <el-button
              v-if="shw"
              type="text"
              size="mini"
              @click="editLabelClass(scope.row.name,scope.row.id)"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          v-if="PageObj.total>0"
          background
          :current-page="PageObj.page"
          @current-change="currentChange"
          :page-size="PageObj.limit"
          layout="total, prev, pager, next"
          :total="PageObj.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <el-card
      class="box-card"
      style="min-height: 100%"
      shadow="never"
      v-loading="productlistloading"
    >
      <div slot="header" class="clearfix">
        <el-tabs v-model="productlisttab" type="card" v-if="!isstore">
          <el-tab-pane label="总部产品" name="first">
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <div class="el-row--flex" style="flex-wrap: wrap">
                  <div style="margin: 0 0 10px 10px">
                    <el-button type="primary" size="small" @click="toAddProduct"
                      >添加产品</el-button
                    >
                  </div>
                  <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="manageLabelClass(1)"
                      >管理分类
                    </el-button>
                  </div>
                  <!-- <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="manageLabelClass(2)"
                      >管理标签
                    </el-button>
                  </div> -->
                  <!-- <div style="margin: 0 0 10px 10px">
                    <el-button size="small" @click="adjustPriceDialog=true"
                      >调价管理
                    </el-button>
                  </div> -->
                  <!-- <div style="margin: 0 0 10px 10px">
                    <el-button
                      size="small"
                      @click="productFreightDialog=true"
                      >运费管理
                    </el-button>
                  </div> -->
                </div>
              </el-col>
              <el-col style="max-width: 300px">
                <div style="max-width: 300px; float: right">
                  <el-input
                    placeholder="请输入名称"
                    size="small"
                    v-model="keyword"
                  >
                    <el-button
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="门店产品" name="second">
            <el-row
              type="flex"
              style="margin-bottom: 20px"
              justify="space-between"
            >
              <el-col>
                <el-select
                  v-model="storeid"
                  size="small"
                  placeholder="请选择"
                  @change="handleClick"
                >
                  <el-option
                    v-for="item in storeList"
                    :key="item.id"
                    :label="item.storetag"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    placeholder="请输入名称"
                    size="small"
                    v-model="keyword"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="productlisttab" type="card" v-if="isstore">
          <el-tab-pane label="门店产品" name="second">
            <el-row
              type="flex"
              style="margin-bottom: 20px; min-height: 40px"
              justify="space-between"
            >
              <el-col
                ><el-button type="primary" size="small" @click="toAddProduct"
                  >添加产品</el-button
                ></el-col
              >
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    placeholder="请输入名称"
                    size="small"
                    v-model="keyword"
                  >
                    <el-button
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="总部产品" name="first">
            <el-row
              type="flex"
              style="margin-bottom: 20px; min-height: 40px"
              justify="space-between"
            >
              <el-col></el-col>
              <el-col>
                <div style="max-width: 300px; float: right">
                  <el-input
                    placeholder="请输入名称"
                    size="small"
                    v-model="keyword"
                  >
                    <el-button
                      slot="append"
                      size="small"
                      icon="el-icon-search"
                      @click="handleClick"
                    ></el-button>
                  </el-input>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <el-row type="flex">
          <el-col :span="16">
            <div class="el-row--flex" style="flex-wrap: wrap">
              <div>
                <div
                  class="el-button el-button--default is-plain"
                  style="border: none; color: #606266 !important"
                >
                  <span>选择分类</span>
                </div>
                <el-select
                  v-model="classKeyword"
                  style="width: 125px"
                  size="small"
                  placeholder="全部分类"
                >
                  <el-option key="0" label="全部分类" value=""> </el-option>
                  <el-option
                    v-for="item in classData"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
              <!--  <div>
                <div
                  class="el-button el-button--default is-plain"
                  style="border: none; color: #606266 !important"
                >
                  <span>选择标签</span>
                </div>
                <el-select
                  v-model="labelKeyword"
                  style="width: 125px"
                  size="small"
                  placeholder="全部标签"
                >
                  <el-option key="0" label="全部标签" value=""> </el-option>
                  <el-option
                    v-for="item in labelData"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div> -->
              <div>
                <div
                  class="el-button el-button--default is-plain"
                  style="border: none; color: #606266 !important"
                >
                  <span>产品状态</span>
                </div>
                <el-select
                  v-model="statusKeyword"
                  style="width: 125px"
                  size="small"
                  placeholder="全部标签"
                >
                  <el-option
                    v-for="item in statusData"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="productlisttab == 'first' && isstore">
            <div style="max-width: 300px; float: right">
              <el-button
                size="mini"
                plain
                type="primary"
                @click="batchAddToStore()"
                >批量添加至门店
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div>
        <template v-if="productlisttab == 'first'">
          <el-table
            ref="productListTable"
            :key="'first'"
            :data="tableData"
            @selection-change="handleSelectionChange"
          >
            <!-- <template slot="header" slot-scope="scope">
                <span style="float: left">
                  <el-popover placement="left" width="100" trigger="click">
                    <div>
                      <template v-for="(item,key) in productTableColumnFirst">
                        <div style="margin-top: 10px">
                          <el-checkbox
                            v-model="item.checked"
                            :true-label="1"
                            :disabled="item.disabled"
                            :false-label="0"
                          >
                            <span v-text="item.name"></span>
                          </el-checkbox>
                        </div>
                      </template>
                    </div>
                    <i class="el-icon-setting" slot="reference"></i>
                  </el-popover>
                </span>
              </template> -->
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              v-if="productTableColumnFirst[0]['checked']"
              label="产品名称"
              width="250"
            >
              <template slot-scope="scope">
                <el-row type="flex" align="middle">
                  <div
                    style="
                      flex-shrink: 0;
                      display: flex;
                      flex-direction: column;
                    "
                  >
                    <template>
                      <el-image
                        :preview-src-list="(scope.row.imgarr && scope.row.imgarr.length>0)?scope.row.imgarr:[]"
                        style="width: 50px; height: 50px"
                        :src="(scope.row.imgarr && scope.row.imgarr.length>0)?scope.row.imgarr[0]:''"
                        fit="contain"
                      >
                        <div
                          slot="error"
                          class="image-slot"
                          style="
                            width: 50px;
                            height: 50px;
                            font-size: 20px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                          "
                        >
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </template>
                    <template v-if="scope.row.issku==1">
                      <el-tag size="mini">有规格</el-tag>
                    </template>
                  </div>
                  <div
                    class="el-row--flex"
                    style="
                      padding-left: 10px;
                      flex-direction: column;
                      line-height: 14px;
                    "
                  >
                    <div style="white-space: nowrap; overflow: hidden">
                      <p
                        v-text="scope.row.product_name"
                        v-if="productlisttab=='first'"
                      ></p>
                    </div>
                    <p
                      style="color: silver"
                      v-text="scope.row.product_barcode"
                    ></p>
                    <p v-text="scope.row.s_price"></p>
                  </div>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column
              v-if="productTableColumnFirst[1]['checked']"
              prop="name"
              label="分类"
            >
              <template slot-scope="scope">
                <span v-text="scope.row.s_class"></span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              v-if="productTableColumnFirst[2]['checked']"
              show-overflow-tooltip
              prop="name"
              label="标签"
            >
              <template slot-scope="scope">
                <span v-text="scope.row.s_label"></span>
              </template>
            </el-table-column> -->
            <el-table-column
              v-if="productTableColumnFirst[3]['checked']"
              width="80"
              align="center"
              prop="name"
              label="网店销售"
            >
              <template slot-scope="scope">
                <el-tag
                  size="medium"
                  :type="scope.row.sell_online==1?'':'info'"
                >
                  {{ scope.row.sell_online==1?'支持':'不支持' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="productTableColumnFirst[4]['checked']"
              align="center"
              v-if="!isstore"
              prop="name"
              label="在售门店"
            >
              <template slot-scope="scope">
                <span
                  class="cursor-pointer text-primary"
                  @click="showhasAddStore(scope.row.hasAddStoreList)"
                  v-text="scope.row.hasAddStoreList.length"
                  v-if="scope.row.hasAddStoreList && scope.row.hasAddStoreList.length>0"
                ></span>
                <span
                  v-if="!scope.row.hasAddStoreList || scope.row.hasAddStoreList.length==0"
                  >0</span
                >
              </template>
            </el-table-column>
            <!-- <el-table-column
                  v-if="productTableColumnFirst[5]['checked']"
                  show-overflow-tooltip
                  prop="freight"
                  align="center"
                  label="运费"
                >
                  <template slot="header">
                    <el-tooltip class="item" effect="light" placement="right">
                      <div slot="content" style="width: 500px">
                        <h3
                          style="
                            color: #333333;
                            font-weight: bolder;
                            font-size: 16px;
                            line-height: 18px;
                          "
                        >
                          tips:
                        </h3>
                        <p style="line-height: 18px">
                          例1：产品A(单位是个)
                          <br />
                          使用运费模板B（首件数量2，首件运费10元，加件数量2，加件运费8元）
                          <br />
                          则购买
                          <br />
                          1~2个产品A，需支付运费10元（首件数量之内），
                          <br />
                          3个，需支付运费10+8=18元（首件运费+加件运费--加件数量1不足2，算1次加件），
                          <br />
                          4个，需支付运费10+8=18元（首件运费+加件运费），
                          <br />
                          5个，需支付运费10+8+8=26元，依次累加
                        </p>
                        <p style="line-height: 18px">
                          例2：购买了产品B
                          <br />
                          使用固定运费6元
                          <br />
                          则购买
                          <br />
                          需支付运费购买数量乘以6
                        </p>
                      </div>
                      <span>运费 <i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <template v-if="scope.row.freight_id==0">
                      <p>固定</p>
                      <p v-text="toMoney(scope.row.freight)"></p>
                    </template>
                    <template v-if="scope.row.freight_id>0">
                      <p>模板</p>
                      <p v-text="scope.row.freightInfo.name"></p>
                    </template>
                  </template>
                </el-table-column> -->
            <el-table-column
              align="center"
              v-if="productTableColumnFirst[6]['checked']"
              label="状态"
            >
              <template slot-scope="scope">
                <el-tag size="medium" :type="scope.row.status==2?'danger':''">
                  {{ scope.row.status==2?'已下架':'已上架' }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- <el-table-column
                  v-if="productTableColumnFirst[7]['checked']"
                  align="center"
                  label="耗材"
                >
                  <template slot="header">
                    <el-tooltip class="item" effect="light" placement="right">
                      <div slot="content">
                        <p>1.非规格产品可设置为耗材使用；</p>
                        <p>
                          2.因耗材不设置规格，故规格产品不能设置为耗材使用；
                        </p>
                        <p>
                          3.产品设置耗材类型一经确定，将不再能修改，请谨慎操作。
                        </p>
                        <p>
                          4.产品设置为耗材使用，仍不在耗材列表呈现，请知晓。
                        </p>
                        <p>
                          5.产品设置为耗材使用，归属于小包装耗材，不能继续拆分。
                        </p>
                      </div>
                      <span>耗材 <i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <span v-if="scope.row.consumable_class==0">纯产品</span>
                    <span v-if="scope.row.consumable_class==1">服务耗材</span>
                    <span v-if="scope.row.consumable_class==2">院装产品</span>
                    <span v-if="scope.row.consumable_class==3">套盒产品</span>
                  </template>
                </el-table-column> -->
            <el-table-column
              v-if="productTableColumnFirst[8]['checked']"
              align="center"
              prop="unit"
              label="基本单位"
            >
              <template slot="header" slot-scope="scope">
                <span>基本单位 </span>
                <el-tooltip class="item" effect="light" placement="bottom">
                  <div slot="content">
                    <p>产品在库存管理、直接销售时的使用单位</p>
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <!-- <el-table-column
              v-if="productTableColumnFirst[9]['checked']"
              align="center"
              prop="small_content"
              label="含量"
            >
              <template slot="header" slot-scope="scope">
                <span>含量 </span>
                <el-tooltip class="item" effect="light" placement="bottom">
                  <div slot="content">
                    <p>1个基本单位中包含的耗用单位数量，仅支持整数</p>
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
            </el-table-column> -->
            <!-- <el-table-column
                  v-if="productTableColumnFirst[10]['checked']"
                  align="center"
                  prop="small_unit"
                  label="耗用单位"
                >
                  <template slot="header" slot-scope="scope">
                    <span>耗用单位 </span>
                    <el-tooltip class="item" effect="light" placement="bottom">
                      <div slot="content">
                        <p>耗材在消耗时使用的单位（仅用于耗材消耗）</p>
                      </div>
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <span
                      v-if="scope.row.small_unit"
                      v-text="scope.row.small_unit"
                    ></span>
                    <span v-else>--</span>
                  </template>
                </el-table-column> -->
            <el-table-column
              v-if="productTableColumnFirst[11]['checked']"
              align="right"
              fixed="right"
              width="280"
              label="操作"
            >
              <template slot-scope="scope">
                <el-dropdown
                  @command="handleCommand"
                  style="margin-right: 10px"
                >
                  <el-button plain size="mini"> 更多 </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <!-- <el-dropdown-item
                      @click.native.stop="setMemberPrice(scope.row,2)"
                    >
                      会员价
                    </el-dropdown-item> -->
                    <el-dropdown-item
                      @click.native.stop="toDetail(scope.row.id)"
                    >
                      详情
                    </el-dropdown-item>
                    <!-- <template v-if="!isstore">
                      <template v-if="scope.row.issku==1">
                        <el-tooltip
                          v-if="scope.row.issku==1"
                          class="item"
                          effect="dark"
                          content="规格产品，不能设置为耗材"
                          placement="top"
                        >
                          <el-dropdown-item> 耗材 </el-dropdown-item>
                        </el-tooltip>
                      </template>
                      <template v-if="scope.row.issku==2">
                        <template v-if="scope.row.consumable_class==0 || 1==1">
                          <el-dropdown-item
                            @click.native.stop="toConsumable(scope.row)"
                          >
                            耗材
                          </el-dropdown-item>
                        </template>
                      </template>
                    </template> -->
                    <el-dropdown-item
                      v-if="!isstore"
                      @click.native.stop="toPutaway(scope.row.id)"
                    >
                      上架
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="!isstore"
                      @click.native.stop="toSoldOut(scope.row.id)"
                    >
                      下架
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="!isstore"
                      @click.native.stop="toDel(scope.row.id)"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <template v-if="isstore">
                  <template v-if="!scope.row.isadd">
                    <el-button
                      size="mini"
                      plain
                      type="primary"
                      @click="addToStore(scope.row.id)"
                      >加&nbsp;&nbsp;入
                    </el-button>
                  </template>
                  <template v-if="scope.row.isadd">
                    <el-button size="mini" type="primary">已加入 </el-button>
                  </template>
                </template>
                <template v-if="!isstore">
                  <el-dropdown style="margin-right: 10px">
                    <el-button plain size="mini">
                      &nbsp;&nbsp;&nbsp;编辑&nbsp;&nbsp;&nbsp;
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        @click.native.stop="toEdit(scope.row.id)"
                      >
                        &nbsp;&nbsp;信息&nbsp;&nbsp;
                      </el-dropdown-item>
                      <el-dropdown-item
                        @click.native.stop="toEditeditor(scope.row.id)"
                      >
                        &nbsp;&nbsp;图文&nbsp;&nbsp;
                      </el-dropdown-item>
                      <!-- <el-dropdown-item
                        @click.native.stop="toEditFreight(scope.row)"
                      >
                        &nbsp;&nbsp;运费&nbsp;&nbsp;
                      </el-dropdown-item> -->
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <div
            class="block"
            style="margin-top: 20px; overflow: hidden"
            v-if="total>0 && !productlistloading"
          >
            <div style="float: left" v-if="!isstore">
              <el-dropdown>
                <el-button size="small"> 批量操作 </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="hQbatch(1)"
                    >上架</el-dropdown-item
                  >
                  <el-dropdown-item @click.native="hQbatch(2)"
                    >下架</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="isMerchant==1"
                    @click.native="hQbatch(3)"
                    >删除</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
              <el-dropdown>
                <el-button size="small"> 批量设置 </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="hQbatch(4)"
                    >调价范围</el-dropdown-item
                  >
                  <!-- <el-dropdown-item @click.native="hQbatch(5)"
                    >运费管理</el-dropdown-item
                  > -->
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <el-pagination
              background
              style="float: right"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-sizes="[10, 20, 30, 40,100]"
              :page-size="limit"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </template>
        <template v-if="productlisttab != 'first'">
          <el-table
            :border="false"
            ref="productListTable"
            :key="'second'"
            :data="tableData"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <!-- <template slot="header" slot-scope="scope">
                <span style="float: left">
                  <el-popover placement="left" width="100" trigger="click">
                    <div>
                      <template v-for="(item,key) in productTableColumnSecond">
                        <div style="margin-top: 10px">
                          <el-checkbox
                            v-model="item.checked"
                            :true-label="1"
                            :disabled="item.disabled"
                            :false-label="0"
                          >
                            <span v-text="item.name"></span>
                          </el-checkbox>
                        </div>
                      </template>
                    </div>
                    <i class="el-icon-setting" slot="reference"></i>
                  </el-popover>
                </span>
              </template> -->
            <el-table-column v-if="isstore" type="selection" width="55">
            </el-table-column>
            <el-table-column
              v-if="productTableColumnSecond[0]['checked']"
              label="产品名称"
              width="250"
            >
              <template slot-scope="scope">
                <el-row type="flex" align="middle">
                  <div
                    style="
                      flex-shrink: 0;
                      display: flex;
                      flex-direction: column;
                    "
                  >
                    <template>
                      <el-image
                        :preview-src-list="(scope.row.imgarr && scope.row.imgarr.length>0)?scope.row.imgarr:[]"
                        style="width: 50px; height: 50px"
                        :src="(scope.row.imgarr && scope.row.imgarr.length>0)?scope.row.imgarr[0]:''"
                        fit="contain"
                      >
                        <div
                          slot="error"
                          class="image-slot"
                          style="
                            width: 50px;
                            height: 50px;
                            font-size: 20px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                          "
                        >
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </template>
                    <template v-if="scope.row.issku==1">
                      <el-tag size="mini">有规格</el-tag>
                    </template>
                  </div>
                  <div
                    class="el-row--flex"
                    style="
                      padding-left: 10px;
                      flex-direction: column;
                      line-height: 14px;
                    "
                  >
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <div style="white-space: nowrap; overflow: hidden">
                          <p v-text="scope.row.product_name"></p>
                          <div
                            v-if="scope.row.product_name!=scope.row.originalName"
                            style="padding-top: 10px"
                          >
                            总部:{{scope.row.originalName}}
                          </div>
                        </div>
                      </div>
                      <div style="white-space: nowrap; overflow: hidden">
                        <p v-text="scope.row.product_name"></p>
                        <div
                          v-if="scope.row.product_name!=scope.row.originalName && !productlistloading"
                        >
                          【{{scope.row.originalName}}】
                        </div>
                      </div>
                    </el-tooltip>
                    <p
                      style="color: silver"
                      v-text="scope.row.product_barcode"
                    ></p>
                    <p v-text="scope.row.s_price"></p>
                  </div>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column
              v-if="productTableColumnSecond[1]['checked']"
              prop="name"
              label="分类"
            >
              <template slot-scope="scope">
                <span v-text="scope.row.s_class"></span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              v-if="productTableColumnSecond[2]['checked']"
              prop="name"
              label="标签"
            >
              <template slot-scope="scope">
                <span v-text="scope.row.s_label"></span>
              </template>
            </el-table-column> -->
            <el-table-column
              v-if="productTableColumnSecond[3]['checked']"
              prop="name"
              align="center"
              label="网店销售"
            >
              <template slot-scope="scope">
                <el-tag
                  size="medium"
                  :type="scope.row.sell_online==1?'':'info'"
                >
                  {{ scope.row.sell_online==1?'支持':'不支持' }}
                </el-tag>
              </template>
            </el-table-column>
            <!-- <el-table-column
                  v-if="productTableColumnSecond[4]['checked']"
                  prop="totalnum"
                  align="center"
                  label="总库存"
                >
                </el-table-column> -->
            <el-table-column
              v-if="productTableColumnSecond[5]['checked']"
              prop="totalsell"
              align="center"
              label="总销量"
            >
            </el-table-column>
            <!-- <el-table-column
                  v-if="productTableColumnSecond[6]['checked']"
                  show-overflow-tooltip
                  prop="freight"
                  label="运费"
                >
                  <template slot="header">
                    <el-tooltip class="item" effect="light" placement="right">
                      <div slot="content" style="width: 500px">
                        <h3
                          style="
                            color: #333333;
                            font-weight: bolder;
                            font-size: 16px;
                            line-height: 18px;
                          "
                        >
                          tips:
                        </h3>
                        <p style="line-height: 18px">
                          例1：产品A(单位是个)
                          <br />
                          使用运费模板B（首件数量2，首件运费10元，加件数量2，加件运费8元）
                          <br />
                          则购买
                          <br />
                          1~2个产品A，需支付运费10元（首件数量之内），
                          <br />
                          3个，需支付运费10+8=18元（首件运费+加件运费--加件数量1不足2，算1次加件），
                          <br />
                          4个，需支付运费10+8=18元（首件运费+加件运费），
                          <br />
                          5个，需支付运费10+8+8=26元，依次累加
                        </p>
                        <p style="line-height: 18px">
                          例2：购买了产品B
                          <br />
                          使用固定运费6元
                          <br />
                          则购买
                          <br />
                          需支付运费购买数量乘以6
                        </p>
                      </div>
                      <span>运费 <i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <template v-if="scope.row.freight_id==0">
                      <p>固定运费</p>
                      <p v-text="toMoney(scope.row.freight)"></p>
                    </template>
                    <template v-if="scope.row.freight_id>0">
                      <p>模板运费</p>
                      <p
                        v-text="scope.row.freightInfo.first_num+'件'+scope.row.freightInfo.first_fee+'元，加件'+scope.row.freightInfo.add_num+'件'+scope.row.freightInfo.add_fee+'元'"
                      ></p>
                    </template>
                  </template>
                </el-table-column> -->
            <el-table-column
              align="center"
              v-if="productTableColumnSecond[7]['checked']"
              label="状态"
            >
              <template slot-scope="scope">
                <el-tag
                  size="medium"
                  :type="scope.row.status=='未上架'?'danger':''"
                >
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- <el-table-column
                  v-if="productTableColumnSecond[8]['checked']"
                  align="center"
                  label="耗材"
                >
                  <template slot="header">
                    <el-tooltip class="item" effect="light" placement="right">
                      <div slot="content">
                        <p>1.非规格产品可设置为耗材使用；</p>
                        <p>
                          2.因耗材不设置规格，故规格产品不能设置为耗材使用；
                        </p>
                        <p>
                          3.产品设置耗材类型一经确定，将不再能修改，请谨慎操作。
                        </p>
                        <p>
                          4.产品设置为耗材使用，仍不在耗材列表呈现，请知晓。
                        </p>
                        <p>
                          5.产品设置为耗材使用，归属于小包装耗材，不能继续拆分。
                        </p>
                      </div>
                      <span>耗材 <i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <span v-if="scope.row.consumable_class==0">纯产品</span>
                    <span v-if="scope.row.consumable_class==1">服务耗材</span>
                    <span v-if="scope.row.consumable_class==2">院装产品</span>
                    <span v-if="scope.row.consumable_class==3">套盒产品</span>
                  </template>
                </el-table-column> -->
            <el-table-column
              v-if="productTableColumnSecond[9]['checked']"
              align="center"
              prop="unit"
              label="基本单位"
            >
              <template slot="header" slot-scope="scope">
                <span>基本单位 </span>
                <el-tooltip class="item" effect="light" placement="bottom">
                  <div slot="content">
                    <p>产品在库存管理、直接销售时的使用单位</p>
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-if="productTableColumnSecond[10]['checked']"
              align="center"
              prop="small_content"
              label="含量"
            >
              <template slot="header" slot-scope="scope">
                <span>含量 </span>
                <el-tooltip class="item" effect="light" placement="bottom">
                  <div slot="content">
                    <p>1个基本单位中包含的耗用单位数量，仅支持整数</p>
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <!-- <el-table-column
                  v-if="productTableColumnSecond[11]['checked']"
                  align="center"
                  prop="small_unit"
                  label="耗用单位"
                >
                  <template slot="header" slot-scope="scope">
                    <span>耗用单位 </span>
                    <el-tooltip class="item" effect="light" placement="bottom">
                      <div slot="content">
                        <p>耗材在消耗时使用的单位（仅用于耗材消耗）</p>
                      </div>
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <span
                      v-if="scope.row.small_unit"
                      v-text="scope.row.small_unit"
                    ></span>
                    <span v-else>--</span>
                  </template>
                </el-table-column> -->
            <el-table-column
              v-if="isstore"
              align="center"
              prop="sort"
              width="100"
              label="排序"
            >
              <template slot="header">
                <el-tooltip class="item" effect="light" placement="right">
                  <div slot="content">
                    <p>排序数值越大越靠前；</p>
                    <p>数值需设置为整数；</p>
                    <p>数值最大9999；</p>
                    <p>
                      商品排序可应用于商城小程序【商城页面】和收银机点单页面；
                    </p>
                    <p>回车保存排序数值。</p>
                  </div>
                  <span>排序 <i class="el-icon-question"></i></span>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-input
                  @keyup.enter.native="saveSort(scope.row)"
                  maxlength="4"
                  show-word-limit
                  v-model="scope.row.sort"
                  size="mini"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
              v-if="productTableColumnSecond[12]['checked']"
              align="right"
              fixed="right"
              width="250"
              label="操作"
            >
              <template slot-scope="scope">
                <el-dropdown
                  @command="handleCommand"
                  style="margin-right: 10px"
                >
                  <el-button plain size="mini">
                    &nbsp;&nbsp;&nbsp;更多&nbsp;&nbsp;&nbsp;
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <!-- <el-dropdown-item
                      @click.native.stop="setMemberPrice(scope.row,4)"
                    >
                      会员价
                    </el-dropdown-item> -->
                    <el-dropdown-item
                      @click.native.stop="toDetail(scope.row.product_id)"
                    >
                      详情
                    </el-dropdown-item>
                    <el-tooltip
                      v-if="scope.row.headquarterStatus==2"
                      class="item"
                      effect="dark"
                      content="总部已下架"
                      placement="top"
                    >
                      <el-dropdown-item v-if="isstore"> 上架 </el-dropdown-item>
                    </el-tooltip>
                    <el-dropdown-item
                      v-if="isstore && scope.row.headquarterStatus!=2"
                      @click.native.stop="toPutaway(scope.row.id)"
                    >
                      上架
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="isstore"
                      @click.native.stop="toSoldOut(scope.row.id)"
                    >
                      下架
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="!isstore"
                      @click.native.stop="toDel(scope.row.id)"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-popover placement="right" width="320" trigger="click">
                  <div>
                    <p style="text-align: center; margin-top: 16px">
                      扫一扫，在手机上查看并分享
                    </p>
                    <div style="text-align: center">
                      <el-image :src="productCode" alt="" width="240px">
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </div>
                    <div style="text-align: center; margin: 10px 0px">
                      <el-link
                        :href="productCode"
                        :download="'【产品】'+scope.row.product_name"
                        style="color: #3e63dd"
                        :disabled="productCode==''"
                        :underline="false"
                        >下载图片</el-link
                      >
                    </div>
                  </div>
                  <el-button
                    slot="reference"
                    @click="getCode(scope.row.id,scope.row.storeId)"
                    type="primary"
                    size="mini"
                    plain
                    >推广</el-button
                  >
                </el-popover> -->
                <template v-if="isstore && productlisttab!='first'">
                  <el-button
                    @click="toEdit(scope.row.product_id)"
                    size="mini"
                    type="primary"
                    plain
                  >
                    编辑
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <div
            class="block"
            style="margin-top: 20px; overflow: hidden"
            v-if="total>0 && !productlistloading"
          >
            <div style="float: left" v-if="isstore">
              <el-dropdown>
                <el-button type="primary" size="small"> 批量操作 </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="sTbatch(1)"
                    >上架</el-dropdown-item
                  >
                  <el-dropdown-item @click.native="sTbatch(2)"
                    >下架</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <el-pagination
              background
              style="float: right"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-sizes="[10, 20, 30, 40,100]"
              :page-size="limit"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </template>
      </div>
    </el-card>
    <el-dialog
      title="提示"
      :visible.sync="showAddStore"
      append-to-body
      width="900px"
    >
      <el-table :data="showAddStoreArr" style="width: 100%">
        <el-table-column label="门店">
          <template slot-scope="scope">
            <el-row type="flex" align="middle">
              <div style="flex-shrink: 0">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="scope.row.logoImg"
                  fit="contain"
                >
                  <div
                    slot="error"
                    class="image-slot"
                    style="
                      width: 50px;
                      height: 50px;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <i
                      class="el-icon-picture-outline"
                      style="font-size: 20px"
                    ></i>
                  </div>
                </el-image>
              </div>
              <div
                class="el-row--flex"
                style="
                  padding-left: 10px;
                  flex-direction: column;
                  line-height: 14px;
                "
              >
                <p v-text="scope.row.storetag"></p>
                <p v-text="scope.row.alias"></p>
              </div>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="地址"> </el-table-column>
        <el-table-column label="负责人" prop="dutyman_name"> </el-table-column>
        <el-table-column label="负责人联系方式" prop="dutyman_phone">
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="showAddStore = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      title="调价管理"
      :visible.sync="adjustPriceDialog"
      append-to-body
      width="900px"
    >
      <el-popover
        placement="right"
        width="300"
        v-model="adjustPriceEdit"
        trigger="click"
      >
        <el-form
          ref="adjustPriceForm"
          :model="adjustPriceData"
          :rules="adjustPriceRules"
          size="small"
          label-width="60px"
        >
          <el-form-item label="">
            <el-col :span="24">
              <span style="font-weight: bolder">方案设置</span>
            </el-col>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-col :span="24">
              <el-input placeholder="方案名称" v-model="adjustPriceData.name">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-col :span="24">
              <el-radio
                v-model="adjustPriceData.type"
                :label="1"
                :disabled="adjustPriceData.id>0"
                >数值调整
              </el-radio>
              <el-radio
                v-model="adjustPriceData.type"
                :label="2"
                :disabled="adjustPriceData.id>0"
                >比例调整
              </el-radio>
            </el-col>
          </el-form-item>
          <el-form-item label="上调" prop="increase">
            <el-col :span="24">
              <el-input v-model="adjustPriceData.increase">
                <template slot="prepend">上调</template>
                <template slot="append"
                  ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
                </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="下调" prop="decrease">
            <el-col :span="24">
              <el-input v-model="adjustPriceData.decrease">
                <template slot="prepend">下调</template>
                <template slot="append"
                  ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
                </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              :loading="addnow"
              @click="confirmAdjustPrice"
              >确定
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="adjustPriceEdit = false"
              >取消</el-button
            >
          </el-form-item>
        </el-form>
        <el-button
          @click="toAddAdjustPrice"
          type="primary"
          size="small"
          slot="reference"
        >
          <span v-text="'设置调价方案'"></span>
        </el-button>
      </el-popover>
      <el-table
        :data="adjustPriceTableData"
        style="width: 100%; margin-top: 15px"
      >
        <el-table-column show-overflow-tooltip prop="name" label="名称">
        </el-table-column>
        <el-table-column prop="typeName" label="类型"> </el-table-column>
        <el-table-column prop="increase" label="上调"> </el-table-column>
        <el-table-column prop="decrease" label="下调"> </el-table-column>
        <el-table-column label="使用次数">
          <el-table-column prop="productNum" label="产品"> </el-table-column>
          <el-table-column prop="serviceNum" label="服务"> </el-table-column>
        </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="delAdjustPrice(scope.row.id)"
              >删除
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="adjustPriceData=scope.row;adjustPriceEdit=true;"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          v-if="adjustPricePageObj.total>0"
          background
          :current-page="adjustPricePageObj.page"
          @current-change="adjustPriceCurrentChange"
          :page-size="adjustPricePageObj.limit"
          layout="total, prev, pager, next"
          :total="adjustPricePageObj.total"
        >
        </el-pagination>
      </div>
    </el-dialog>

    <el-dialog
      title="调价设置"
      :visible.sync="adjustPriceSetDialog"
      append-to-body
      width="900px"
    >
      <el-table
        :data="adjustPriceTableData"
        style="width: 100%; margin-top: 15px"
      >
        <el-table-column show-overflow-tooltip prop="name" label="名称">
        </el-table-column>
        <el-table-column prop="typeName" label="类型"> </el-table-column>
        <el-table-column prop="increase" label="上调"> </el-table-column>
        <el-table-column prop="decrease" label="下调"> </el-table-column>
        <el-table-column label="使用次数">
          <el-table-column prop="productNum" label="产品"> </el-table-column>
          <el-table-column prop="serviceNum" label="服务"> </el-table-column>
        </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="dobatchAdjustPrice(scope.row.id)"
              >确定
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          v-if="adjustPricePageObj.total>0"
          background
          :current-page="adjustPricePageObj.page"
          @current-change="adjustPriceCurrentChange"
          :page-size="adjustPricePageObj.limit"
          layout="total, prev, pager, next"
          :total="adjustPricePageObj.total"
        >
        </el-pagination>
      </div>
    </el-dialog>

    <el-dialog
      title="运费管理"
      :visible.sync="productFreightDialog"
      append-to-body
      width="900px"
    >
      <el-alert style="margin-bottom: 10px" :closable="false" type="info">
        <div slot="title">
          <div>
            运费设置帮助
            <el-tooltip class="item" effect="light" placement="right">
              <div slot="content" style="width: 500px">
                <h3
                  style="
                    color: #333333;
                    font-weight: bolder;
                    font-size: 16px;
                    line-height: 18px;
                  "
                >
                  首件和加件:
                </h3>
                <p style="line-height: 20px">
                  这里的"件"不是产品单位，而是多个单位产品的集合
                </p>
                <h3
                  style="
                    color: #333333;
                    font-weight: bolder;
                    font-size: 16px;
                    line-height: 18px;
                  "
                >
                  tips:
                </h3>
                <p style="line-height: 18px">
                  例1：产品A(单位是个)
                  <br />
                  使用运费模板B（首件数量2，首件运费10元，加件数量2，加件运费8元）
                  <br />
                  则购买
                  <br />
                  1~2个产品A，需支付运费10元（首件数量之内），
                  <br />
                  3个，需支付运费10+8=18元（首件运费+加件运费--加件数量1不足2，算1次加件），
                  <br />
                  4个，需支付运费10+8=18元（首件运费+加件运费），
                  <br />
                  5个，需支付运费10+8+8=26元，依次累加
                </p>
                <p style="line-height: 18px">
                  例2：购买了产品A和产品B
                  <br />
                  最终运费为产品A的运费+产品B的运费
                </p>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
        </div>
      </el-alert>
      <el-popover
        placement="right"
        width="600"
        v-model="productFreightEdit"
        trigger="click"
      >
        <el-form
          ref="productFreightForm"
          :model="productFreightData"
          :rules="productFreightRules"
          size="small"
          label-width="120px"
        >
          <el-form-item label="">
            <el-col :span="24">
              <span style="font-weight: bolder">模板设置</span>
            </el-col>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-col :span="12">
              <el-input
                placeholder="模板名称"
                v-model="productFreightData.name"
              >
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="首件数量" prop="first_num">
            <el-col :span="12">
              <el-input v-model="productFreightData.first_num">
                <!--<template slot="prepend">数量</template>-->
                <template slot="append"><span v-text="'件'"></span> </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="首件运费" prop="first_fee">
            <el-col :span="12">
              <el-input v-model="productFreightData.first_fee">
                <!--<template slot="prepend">运费</template>-->
                <template slot="append"><span v-text="'元'"></span> </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="加件数量" prop="add_num">
            <el-col :span="12">
              <el-input v-model="productFreightData.add_num">
                <!--<template slot="prepend">数量</template>-->
                <template slot="append"><span v-text="'件'"></span> </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="加件运费" prop="add_fee">
            <el-col :span="12">
              <el-input v-model="productFreightData.add_fee">
                <!--<template slot="prepend">运费</template>-->
                <template slot="append"><span v-text="'元'"></span> </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              :loading="addnow"
              @click="confirmProductFreight"
              >确定
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="productFreightEdit = false"
              >取消</el-button
            >
          </el-form-item>
        </el-form>
        <el-button
          @click="toAddProductFreight"
          type="primary"
          size="small"
          slot="reference"
        >
          <span v-text="'设置运费模板'"></span>
        </el-button>
      </el-popover>
      <el-table
        :data="productFreightTableData"
        style="width: 100%; margin-top: 15px"
      >
        <el-table-column show-overflow-tooltip prop="name" label="名称">
        </el-table-column>
        <el-table-column prop="first_num" label="首件数量" align="center">
        </el-table-column>
        <el-table-column prop="first_fee" label="首件价格" align="center">
        </el-table-column>
        <el-table-column prop="add_num" label="加件数量" align="center">
        </el-table-column>
        <el-table-column prop="add_fee" label="加件价格" align="center">
        </el-table-column>
        <el-table-column label="使用产品数" prop="productNum" align="center">
        </el-table-column>
        <el-table-column prop="status" label="操作" align="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="delProductFreight(scope.row.id)"
              >删除
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="productFreightData=JSON.parse(JSON.stringify(scope.row));productFreightEdit=true;"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          v-if="productFreightPageObj.total>0"
          background
          :current-page="productFreightPageObj.page"
          @current-change="productFreightCurrentChange"
          :page-size="productFreightPageObj.limit"
          layout="total, prev, pager, next"
          :total="productFreightPageObj.total"
        >
        </el-pagination>
      </div>
    </el-dialog>

    <el-dialog
      title="运费设置"
      :visible.sync="editFreightDialog"
      append-to-body
      width="900px"
    >
      <div v-loading="editFreightDialogLoading">
        <el-alert style="margin-bottom: 10px" :closable="false" type="info">
          <div slot="title">
            <div>
              运费设置帮助
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="width: 500px">
                  <h3
                    style="
                      color: #333333;
                      font-weight: bolder;
                      font-size: 16px;
                      line-height: 18px;
                    "
                  >
                    首件和加件:
                  </h3>
                  <p style="line-height: 20px">
                    这里的"件"不是产品单位，而是多个单位产品的集合
                  </p>
                  <h3
                    style="
                      color: #333333;
                      font-weight: bolder;
                      font-size: 16px;
                      line-height: 18px;
                    "
                  >
                    tips:
                  </h3>
                  <p style="line-height: 18px">
                    例1：产品A(单位是个)
                    <br />
                    使用运费模板B（首件数量2，首件运费10元，加件数量2，加件运费8元）
                    <br />
                    则购买
                    <br />
                    1~2个产品A，需支付运费10元（首件数量之内），
                    <br />
                    3个，需支付运费10+8=18元（首件运费+加件运费--加件数量1不足2，算1次加件），
                    <br />
                    4个，需支付运费10+8=18元（首件运费+加件运费），
                    <br />
                    5个，需支付运费10+8+8=26元，依次累加
                  </p>
                  <p style="line-height: 18px">
                    例2：购买了产品A和产品B
                    <br />
                    最终运费为产品A的运费+产品B的运费
                  </p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </div>
        </el-alert>
        <el-table
          :data="productFreightTableData"
          style="width: 100%; margin-top: 15px"
        >
          <el-table-column show-overflow-tooltip prop="name" label="名称">
          </el-table-column>
          <el-table-column prop="first_num" label="首件数量" align="center">
          </el-table-column>
          <el-table-column prop="first_fee" label="首件价格" align="center">
          </el-table-column>
          <el-table-column prop="add_num" label="加件数量" align="center">
          </el-table-column>
          <el-table-column prop="add_fee" label="加件价格" align="center">
          </el-table-column>
          <el-table-column label="使用产品数" prop="productNum" align="center">
          </el-table-column>
          <el-table-column prop="status" label="操作" align="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="sureProductFreight(scope.row)"
                >确定
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="overflow: hidden">
          <el-pagination
            style="margin-top: 10px; float: right"
            v-if="productFreightPageObj.total>0"
            background
            :current-page="productFreightPageObj.page"
            @current-change="productFreightCurrentChange"
            :page-size="productFreightPageObj.limit"
            layout="total, prev, pager, next"
            :total="productFreightPageObj.total"
          >
          </el-pagination>
        </div>
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>运费设置</span>
          </div>
          <el-form :model="editFreightInfo" label-width="120px" size="mini">
            <el-form-item label="运费">
              <el-radio-group v-model="editFreightInfo.type">
                <el-radio :label="0">固定运费</el-radio>
                <el-radio :label="1">使用运费模板</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="固定运费值:" v-if="editFreightInfo.type==0">
              <el-col :span="12">
                <el-input v-model="editFreightInfo.freight"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="运费模板:" v-if="editFreightInfo.type==1">
              <template v-if="editFreightInfo.freightInfo.id==0">
                <span
                  v-text="'请选择一个运费模板'"
                  style="color: silver"
                ></span>
              </template>
              <template v-if="editFreightInfo.freightInfo.id>0">
                <span
                  v-text="editFreightInfo.freightInfo.name"
                  style="color: silver"
                ></span>
              </template>
            </el-form-item>
            <el-form-item v-if="editFreightInfo.product_id>0">
              <el-button type="primary" @click="doSetProductFreight"
                >确定</el-button
              >
            </el-form-item>
            <el-form-item v-else>
              <el-button type="primary" @click="dobatchFreight">确定</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </el-dialog>

    <el-dialog
      title="设置为耗材"
      :visible.sync="editToConsumable"
      append-to-body
      width="800px"
    >
      <div v-loading="editToConsumableLoading">
        <el-alert show-icon type="warning">
          <div slot="title">
            <p>1.非规格产品可设置为耗材使用；</p>
            <br />
            <p>2.因耗材不设置规格，故规格产品不能设置为耗材使用；</p>
            <br />
            <p>3.产品设置耗材类型一经确定，将不再能修改，请谨慎操作。</p>
            <br />
            <p>
              4.产品设置为耗材使用，仍不在耗材列表呈现，当可以参与耗材的消耗（服务耗材可被服务关联），请知晓。
            </p>
            <br />
            <p>5.产品设置为耗材使用，归属于小包装耗材，不能继续拆分。</p>
          </div>
        </el-alert>
        <div style="margin: 20px">
          <el-form label-width="80px" size="small">
            <el-form-item label="耗材类型">
              <el-radio-group
                v-model="editToConsumableType"
                size="small"
                :disabled="editToConsumableData.consumable_class>0"
              >
                <el-radio :label="0" border>纯产品</el-radio>
                <el-radio :label="1" border>服务耗材</el-radio>
                <el-radio :label="2" border>院装产品</el-radio>
                <el-radio :label="3" border>套盒产品</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="耗材含量">
              <el-input-number
                v-model="editToConsumableNum"
                controls-position="right"
                :min="1"
                :step="1"
                :precision="0"
              >
              </el-input-number>
            </el-form-item>
            <el-form-item label="耗用单位">
              <el-select
                v-model="editToConsumableUnit"
                filterable
                allow-create
                default-first-option
                collapse-tags
                placeholder="请选择/输入"
              >
                <el-option
                  v-for="(item,index) in unitListData"
                  :key="index"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div style="overflow: hidden">
          <div style="margin: 20px 0 0 0; float: right">
            <el-button
              size="small"
              @click="editToConsumableType=0,editToConsumable=false"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="submitEditToConsumable"
            >
              确定
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="会员价"
      :visible.sync="memberGoodsInfo.memberPriceDialog"
      append-to-body
      width="950px"
    >
      <div>
        <el-table
          :data="memberGoodsInfo.MemberPriceData"
          max-height="550"
          :span-method="objectSpanMethod"
          style="width: 100%"
        >
          <el-table-column prop="goodsName" label="名称" width="200">
            <template slot-scope="scope">
              <div v-text="scope.row.goodsName"></div>
              <el-tag size="small" v-if="scope.row.skuName"
                >{{scope.row.skuName}}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column prop="levelName" label="会员等级" width="100">
            <template slot-scope="scope">
              <div v-html="scope.row.levelName"></div>
            </template>
          </el-table-column>
          <el-table-column prop="originalPrice" label="原价" width="200">
          </el-table-column>
          <el-table-column prop="price" label="会员价" min-width="300">
            <template slot-scope="scope">
              <el-row type="flex" v-if="scope.row.set">
                <el-input
                  size="small"
                  placeholder="请输入会员价"
                  :readonly="scope.row.loading"
                  v-model="scope.row.price"
                  @keyup.enter.native="submitMemberPrice(scope.row)"
                ></el-input>
                <el-button
                  size="small"
                  style="margin-left: 10px"
                  type="primary"
                  :loading="scope.row.loading"
                  @click="submitMemberPrice(scope.row)"
                  >保存</el-button
                >
              </el-row>
              <template v-else>
                <el-input
                  size="small"
                  placeholder="会员价"
                  :readonly="true"
                  v-model="scope.row.price"
                ></el-input>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="small"
          type="primary"
          @click="memberGoodsInfo.memberPriceDialog = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</div>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#productList",
      data() {
        var money = (rule, value, callback) => {
          if (value === "") {
            callback();
          } else if (isNaN(Number(value))) {
            callback(new Error("必须是数字"));
          } else {
            if (
              /^(([1-9]\d*)(\.\d{1,2})?)$|^(0\.0([1-9]))$|^(0\.([1-9])\d?)$/.test(
                value
              ) ||
              value == 0
            ) {
              callback();
            } else {
              callback(new Error("数值最多保留2位小数"));
            }
          }
        };
        var rulesNum = (rule, value, callback) => {
          if (value === "") {
            callback(new Error("数量不能为空"));
          } else if (isNaN(Number(value))) {
            callback(new Error("数量必须是数字"));
          } else {
            if (value == parseInt(value) && value > 0) {
              if (parseInt(value) - 999999 > 0) {
                callback(new Error("长度必须在6个字符以内"));
              }
              callback();
            } else {
              callback(new Error("必须是正整数"));
            }
          }
        };
        return {
          productTableColumnFirst: [
            {checked: 1, name: "产品名称", disabled: true},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "网店"},
            {checked: 1, name: "在售门店"},
            {checked: 1, name: "运费"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "基本单位"},
            {checked: 1, name: "含量"},
            {checked: 1, name: "耗用单位"},
            {checked: 1, name: "操作", disabled: true},
          ],
          productTableColumnSecond: [
            {checked: 1, name: "产品名称", disabled: true},
            {checked: 1, name: "分类"},
            {checked: 1, name: "标签"},
            {checked: 1, name: "网店销售"},
            {checked: 1, name: "总库存"},
            {checked: 1, name: "总销量"},
            {checked: 1, name: "运费"},
            {checked: 1, name: "状态", disabled: true},
            {checked: 1, name: "耗材"},
            {checked: 1, name: "基本单位"},
            {checked: 0, name: "含量"},
            {checked: 0, name: "耗用单位"},
            {checked: 1, name: "操作", disabled: true},
          ],
          editFreightInfo: {
            product_id: 0,
            freight: 0,
            freightInfo: {
              id: 0,
              name: "",
            },
            type: 0,
          },
          editFreightDialog: false,
          editFreightDialogLoading: false,
          // 运费管理
          productFreightDialog: false,
          productFreightData: {
            id: 0,
            name: "",
            first_num: "",
            first_fee: "",
            add_num: "",
            add_fee: "",
          },
          productFreightRules: {
            add_fee: [
              {required: true, message: "加件运费不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: "blur"},
            ],
            first_fee: [
              {required: true, message: "首件运费不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: "blur"},
            ],
            name: [
              {
                required: true,
                message: "运费模板名称不能为空",
                trigger: "blur",
              },
              {max: 20, message: "长度在20个字符以内", trigger: "blur"},
            ],
            first_num: [
              {required: true, message: "首件数量", trigger: "blur"},
              {validator: rulesNum, trigger: "blur"},
            ],
            add_num: [
              {required: true, message: "方案名称不能为空", trigger: "blur"},
              {validator: rulesNum, trigger: "blur"},
            ],
          },
          productFreightSet: true,
          productFreightEdit: false,
          productFreightTableData: [],
          productFreightPageObj: {
            page: 1,
            limit: 10,
            total: 0,
          },
          // 调价管理
          adjustPriceDialog: false,
          adjustPriceData: {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          },
          adjustPriceRules: {
            increase: [
              {required: true, message: "上调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            decrease: [
              {required: true, message: "下调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            name: [
              {required: true, message: "方案名称不能为空", trigger: "blur"},
              {max: 20, message: "长度在20个字符以内", trigger: "blur"},
            ],
          },
          adjustPriceSet: true,
          adjustPriceEdit: false,
          adjustPriceTableData: [],
          adjustPricePageObj: {
            page: 1,
            limit: 10,
            total: 0,
          },
          // 调价管理
          statusData: [
            {id: 0, name: "全部"},
            {id: 1, name: "上架"},
            {id: 2, name: "下架"},
          ],
          // 管理分类标签
          statusKeyword: 0,
          labelClassDialog: false,
          labelclassData: {
            name: "",
          },
          labelclass: 1, // 1 分类  2 产品
          showadd: false,
          PageObj: {
            page: 1,
            limit: 10,
            total: 0,
          },
          labelclassTableData: [],
          shw: false,
          addnow: false,
          // 管理分类标签结束
          labelData: [], // 标签数据
          classData: [], //  分类数据
          classKeyword: "", // 分类关键字
          labelKeyword: "", // 标签关键字
          page: 1,
          limit: 10,
          total: 0,
          tableData: [],
          productlistloading: false,
          cardType: 0,
          keyword: "",
          productlisttab: "{$productlisttab}",
          storeList: [],
          storeid: {$storeid},
          isstore: {$isstore},
          showAddStore: false,
          showAddStoreArr: [],
          batchAddToStoreData: [],
          mchHisPage: 1,
          storeHisPage: 1,
          productCode: "",
          isMerchant: {$isMerchant} ?? 0,
          adjustPriceSetDialog: false,
          editToConsumable: false,
          editToConsumableLoading: false,
          editToConsumableType: 0,
          editToConsumableData: {},
          editToConsumableNum: 1,
          unitListData: {$unit}, //  单位数据
          editToConsumableUnit: "",
          /*设置会员价*/
          memberGoodsInfo: {
            id: "0",
            goodsName: "",
            isSku: "", // 1,规格商品，2，非规格商品
            type: 2, // 商品类型 1 服务 2产品 3卡项
            memberLevelData: [],
            memberPriceData: [],
            memberPriceDialog: false,
          },
          /*设置会员价结束*/
        };
      },
      methods: {
        /*设置排序*/
        saveSort(row) {
          console.log(row);
          if (row.sort != parseInt(row.sort)) {
            return this.$message.error("排序数值有误");
          }
          $.ajax({
            url: "{:url('setSort')}",
            data: {
              sort: parseInt(row.sort),
              id: row.id,
            },
            type: "post",
            success: (res) => {
              if (res.code == 1) {
                return this.$message.success(res.msg);
              }
              return this.$message.error(res.msg);
            },
          });
        },
        /*设置会员价*/
        setMemberPrice(row, type) {
          console.log(row);
          if (type == 2) {
            this.memberGoodsInfo = {
              id: row.id,
              goodsName: row.product_name,
              isSku: row.issku, // 1,规格商品，2，非规格商品
              type: 2, // 商品类型 1 服务 2产品 3卡项
              memberLevelData: [],
              memberPriceData: [],
              skuData: [],
              memberPriceDialog: false,
              skuid: 0,
              originalPrice: row.s_price,
              set: !this.isstore,
            };
          } else if (type == 4) {
            this.memberGoodsInfo = {
              id: row.product_id,
              goodsName: row.product_name,
              isSku: row.issku, // 1,规格商品，2，非规格商品
              type: 2, // 商品类型 1 服务 2产品 3卡项
              memberLevelData: [],
              memberPriceData: [],
              skuData: [],
              memberPriceDialog: false,
              skuid: 0,
              originalPrice: row.s_price,
              set: false,
            };
          }
          this.getMemberPriceData();
        },
        getMemberPriceData() {
          $.ajax({
            url: "{:url('Memberprice/getMemberPriceData')}",
            data: this.memberGoodsInfo,
            type: "POST",
            success: (res) => {
              console.log(res);
              let LevelData = res.data.levelData;
              let priceData = res.data.data;
              let skuData = res.data.skuData;
              let MemberPriceData = [];
              let goods_id = this.memberGoodsInfo["id"];
              let goodsName = this.memberGoodsInfo["goodsName"];
              let type = this.memberGoodsInfo["type"];
              if (this.memberGoodsInfo.isSku == 1) {
                if (res.data.skuData.length > 0) {
                  this.memberGoodsInfo.skuData = skuData;
                  this.memberGoodsInfo.skuid = res.data.skuData[0]["id"];
                } else {
                  return this.$message.error(
                    "当前商品设置为规格商品，但没有具体规格，请确认"
                  );
                }
                skuData.forEach((sku) => {
                  let sku_id = sku["id"];
                  let skuName = sku["skuName"];
                  let price = sku["price"];
                  let originalPrice = sku["originalPrice"];
                  LevelData.forEach((level) => {
                    let item = {
                      goods_id: goods_id,
                      sku_id: sku_id,
                      member_level_id: level["id"],
                      levelName: level["name"],
                      goodsName: goodsName,
                      skuName: skuName,
                      id: 0,
                      type: type,
                      price: "",
                      originalPrice: originalPrice,
                      loading: false,
                      set: this.memberGoodsInfo.set,
                    };
                    priceData.some((mgp) => {
                      if (
                        mgp["goods_id"] == item["goods_id"] &&
                        mgp["sku_id"] == item["sku_id"] &&
                        mgp["member_level_id"] == item["member_level_id"]
                      ) {
                        item["id"] = mgp["id"];
                        item["price"] = mgp["price"];
                        return true;
                      }
                      return false;
                    });
                    MemberPriceData.push(item);
                  });
                });
              } else {
                this.memberGoodsInfo.skuData = [];
                let sku_id = 0;
                let skuName = "";
                let price = "";
                LevelData.forEach((level) => {
                  let item = {
                    goods_id: goods_id,
                    sku_id: sku_id,
                    member_level_id: level["id"],
                    levelName: level["name"],
                    goodsName: goodsName,
                    skuName: skuName,
                    id: 0,
                    type: type,
                    price: price,
                    originalPrice: this.memberGoodsInfo.originalPrice,
                    loading: false,
                    set: this.memberGoodsInfo.set,
                  };
                  priceData.some((mgp) => {
                    if (
                      mgp["goods_id"] == item["goods_id"] &&
                      mgp["sku_id"] == item["sku_id"] &&
                      mgp["member_level_id"] == item["member_level_id"]
                    ) {
                      item["id"] = mgp["id"];
                      item["price"] = mgp["price"];
                      return true;
                    }
                    return false;
                  });
                  MemberPriceData.push(item);
                });
              }
              console.log(MemberPriceData);
              this.memberGoodsInfo.MemberPriceData = MemberPriceData;
              this.memberGoodsInfo.memberLevelData = LevelData;
              this.memberGoodsInfo.skuData = skuData;
              this.memberGoodsInfo.memberPriceDialog = true;
            },
          });
        },
        submitMemberPrice(row) {
          console.log(row);
          let value = row.price;
          if (
            /^(([1-9]\d*)(\.\d{1,2})?)$|^(0\.0([1-9]))$|^(0\.([1-9])\d?)$/.test(
              value
            )
          ) {
          } else {
            return this.$message.error("会员价格式有误，请确认");
          }
          row.loading = true;
          $.ajax({
            url: "{:url('Memberprice/setMemberPriceData')}",
            data: row,
            type: "POST",
            success: (res) => {
              row.loading = false;
              if (res.code == 1) {
                row.id = res.data.id;
                this.$message.success(res.msg);
              } else {
                this.$message.error(res.msg);
              }
            },
          });
        },
        objectSpanMethod({row, column, rowIndex, columnIndex}) {
          if (columnIndex === 0) {
            let num = this.memberGoodsInfo.memberLevelData.length;
            if (rowIndex % num === 0) {
              return {
                rowspan: num,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        },
        /*设置会员价结束*/
        // 设置为耗材
        toConsumable(val) {
          const row = JSON.parse(JSON.stringify(val));
          this.editToConsumableData = row;
          this.editToConsumableType = row.consumable_class;
          this.editToConsumableNum = row.small_content;
          this.editToConsumableUnit = row.small_unit;
          this.editToConsumable = true;
          console.log(row);
        },
        submitEditToConsumable() {
          if (this.editToConsumableData.issku == 1) {
            return this.$message({
              type: "error",
              message: "规格产品不能设置为耗材",
            });
          }
          if (
            this.editToConsumableData.consumable_class > 0 &&
            this.editToConsumableType !=
              this.editToConsumableData.consumable_class
          ) {
            return this.$message({
              type: "error",
              message: "该产品已设置为耗材，无需重复设置",
            });
          }
          if (this.editToConsumableType == 0) {
            return this.$message({
              type: "error",
              message: "未选择耗材类型，无需保存",
            });
          }
          if (!this.editToConsumableUnit) {
            return this.$message({
              type: "error",
              message: "耗材消耗单位不能为空",
            });
          }
          var vm = this;
          vm.editToConsumableLoading = true;
          $.ajax({
            url: "{:url('submitEditToConsumable')}",
            data: {
              product: vm.editToConsumableData.id,
              type: vm.editToConsumableType,
              smallContent: vm.editToConsumableNum,
              smallUnit: vm.editToConsumableUnit,
            },
            type: "POST",
            success(res) {
              // console.log(res);
              if (res["code"] == 1) {
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
                vm.editToConsumable = false;
                vm.editToConsumableType = 0;
                vm.getProductList();
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
              vm.editToConsumableLoading = false;
            },
          });
        },
        dobatchAdjustPrice(val) {
          var vm = this;
          const data = vm.batchAddToStoreData.map(function (item) {
            return item["id"];
          });
          this.$confirm("确定使用该调价范围吗", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              vm.adjustPriceSetDialog = false;
              vm.productlistloading = true;
              $.ajax({
                url: "{:url('batchAdjustPrice')}",
                data: {
                  val: val,
                  data: data.join(","),
                },
                type: "POST",
                success(res) {
                  vm.productlistloading = false;
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消批量操作",
              });
            });
        },
        batchAdjustPrice() {
          // 批量设置调价范围
          this.adjustPriceSetDialog = true;
        },
        batchFreight() {
          // 批量设置运费
          this.editFreightDialog = true;
        },
        dobatchFreight() {
          var vm = this;
          const data = vm.batchAddToStoreData.map(function (item) {
            return item["id"];
          });
          const editFreightInfo = vm.editFreightInfo;
          this.$confirm("确定使用该运费设置吗", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              vm.editFreightDialog = false;
              vm.productlistloading = true;
              $.ajax({
                url: "{:url('batchFreight')}",
                data: {
                  val: editFreightInfo,
                  data: data.join(","),
                },
                type: "POST",
                success(res) {
                  vm.productlistloading = false;
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getProductList();
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消批量操作",
              });
            });
        },
        sTbatch(num) {
          if (this.batchAddToStoreData.length == 0) {
            return this.$message({
              type: "warning",
              message: "请先选择",
            });
          } else {
            var str = "确定将选中产品上架(其中总部下架的产品无法上架)";
            if (num == 2) {
              str = "确定将选中产品下架";
            } else if (num == 3) {
              str = "确定将选中产品删除，删除后无法恢复";
            }
            const data = this.batchAddToStoreData.map(function (item) {
              return item["id"];
            });
            var vm = this;
            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                vm.productlistloading = true;
                $.ajax({
                  url: "{:url('sTbatchSet')}",
                  data: {
                    type: num,
                    data: data.join(","),
                  },
                  type: "POST",
                  success(res) {
                    // console.log(res);
                    vm.productlistloading = false;
                    if (res.code == 1) {
                      vm.$message({
                        type: "success",
                        message: res.msg,
                      });
                      vm.getProductList();
                    } else {
                      vm.$message({
                        type: "error",
                        message: res.msg,
                      });
                    }
                  },
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消批量操作",
                });
              });
          }
        },
        hQbatch(num) {
          if (num === 4 || num === 5) {
            if (this.batchAddToStoreData.length == 0) {
              return this.$message({
                type: "warning",
                message: "请先选择",
              });
            } else {
              if (num === 4) {
                this.batchAdjustPrice();
              } else {
                this.batchFreight();
              }
            }
            return;
          }
          if (this.batchAddToStoreData.length == 0) {
            return this.$message({
              type: "warning",
              message: "请先选择",
            });
          } else {
            var str = "确定将选中产品上架";
            if (num == 2) {
              str = "确定将选中产品下架(相应店铺产品也下架)";
            } else if (num == 3) {
              str = "确定将选中产品删除，删除后无法恢复";
            }
            const data = this.batchAddToStoreData.map(function (item) {
              return item["id"];
            });
            var vm = this;
            this.$confirm(str, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                vm.productlistloading = true;
                $.ajax({
                  url: "{:url('hQbatchSet')}",
                  data: {
                    type: num,
                    data: data.join(","),
                  },
                  type: "POST",
                  success(res) {
                    // console.log(res);
                    vm.productlistloading = false;
                    if (res.code == 1) {
                      vm.$message({
                        type: "success",
                        message: res.msg,
                      });
                      vm.getProductList();
                    } else {
                      vm.$message({
                        type: "error",
                        message: res.msg,
                      });
                    }
                  },
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消批量操作",
                });
              });
          }
        },
        doSetProductFreight() {
          if (
            this.editFreightInfo.type == 1 &&
            this.editFreightInfo.freightInfo.id == 0
          ) {
            return this.$message({
              type: "error",
              message: "请选择一个运费模板",
            });
          }
          var vm = this;
          this.editFreightDialogLoading = true;
          $.ajax({
            url: "{:url('Productfreight/editFreight')}",
            data: vm.editFreightInfo,
            type: "POST",
            success(res) {
              if (res.code == 1) {
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
                vm.getProductList();
                vm.editFreightDialogLoading = false;
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
            },
          });
        },
        sureProductFreight(row) {
          this.editFreightInfo.freightInfo.id = row.id;
          this.editFreightInfo.freightInfo.name = row.name;
        },
        toEditFreight(row) {
          this.editFreightDialog = true;
          var type = 0;
          if (row.freight_id > 0) {
            type = 1;
          }
          var name = "";
          if (row.freight_id > 0) {
            name = row.freightInfo.name;
          }
          this.editFreightInfo = {
            product_id: row.id,
            freight: this.toMoney(row.freight),
            freightInfo: {
              id: row.freight_id,
              name: name,
            },
            type: type,
          };
          // console.log(row);
        },
        toMoney(num) {
          if (isNaN(Number(num))) {
            return "N/A";
          }
          return (num / 100).toFixed(2);
        },
        // 运费管理
        confirmProductFreight() {
          var vm = this;
          this.$refs["productFreightForm"].validate(function (bol) {
            if (!bol) {
              vm.$message({
                showClose: true,
                message: "请完善您的设置信息，带*号为必填或者必选",
                type: "warning",
                duration: 3000,
              });
            } else {
              $.ajax({
                url: "{:url('Productfreight/save')}",
                data: vm.productFreightData,
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getProductFreightData();
                    vm.productFreightEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            }
          });
        },
        toAddProductFreight() {
          this.productFreightData = {
            id: 0,
            name: "",
            first_num: "",
            first_fee: "",
            add_num: "",
            add_fee: "",
          };
        },
        getProductFreightData() {
          var vm = this;
          $.ajax({
            url: "{:url('Productfreight/getPage')}",
            data: {
              page: vm.productFreightPageObj.page,
              limit: vm.productFreightPageObj.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.productFreightTableData = res.data;
                vm.productFreightPageObj.total = res.count;
              } else {
                vm.productFreightTableData = [];
              }
            },
          });
        },
        productFreightCurrentChange(val) {
          this.productFreightPageObj.page = val;
          this.getProductFreightData();
        },
        delProductFreight(id) {
          var vm = this;
          this.$confirm("确定要删除运费模板?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('Productfreight/del')}",
                data: {id: id},
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getProductFreightData();
                    vm.productFreightEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        // 调价管理
        confirmAdjustPrice() {
          var vm = this;
          this.$refs["adjustPriceForm"].validate(function (bol) {
            if (!bol) {
              vm.$message({
                showClose: true,
                message: "请完善您的设置信息，带*号为必填或者必选",
                type: "warning",
                duration: 3000,
              });
            } else {
              $.ajax({
                url: "{:url('Adjustprice/save')}",
                data: vm.adjustPriceData,
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            }
          });
        },
        toAddAdjustPrice() {
          this.adjustPriceData = {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          };
        },
        getAdjustPriceData() {
          var vm = this;
          $.ajax({
            url: "{:url('Adjustprice/getPage')}",
            data: {
              page: vm.adjustPricePageObj.page,
              limit: vm.adjustPricePageObj.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.adjustPriceTableData = res.data;
                vm.adjustPricePageObj.total = res.count;
              } else {
                vm.adjustPriceTableData = [];
              }
            },
          });
        },
        adjustPriceCurrentChange(val) {
          this.adjustPricePageObj.page = val;
          this.getAdjustPriceData();
        },
        delAdjustPrice(id) {
          var vm = this;
          this.$confirm("确定要删除调价方案?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('Adjustprice/del')}",
                data: {id: id},
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        // 调价管理
        imgError(e) {
          e.target.src = "/assets/common/images/img_load_error.png";
        },
        // 管理分类标签
        confirmAdd() {
          var vm = this;
          if (!vm.labelclassData.name) {
            vm.$message({
              showClose: false,
              message: "请输入名称",
              type: "warning",
              duration: 3000,
            });
            return;
          }
          vm.addnow = true;
          $.ajax({
            url: "{:url('Product/AddLabelClass')}",
            data: {
              name: vm.labelclassData.name,
              type: vm.labelclass,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  showClose: false,
                  message: "添加成功",
                  type: "success",
                  duration: 3000,
                });
                vm.getLabelClassData();
                vm.getLabelAndClassData();
              } else {
                vm.$message({
                  showClose: false,
                  message: res.msg,
                  type: "error",
                  duration: 3000,
                });
              }
              vm.labelclassData.name = "";
              vm.addnow = false;
            },
          });
        },
        getLabelClassData() {
          var vm = this;
          $.ajax({
            url: "{:url('Product/getLabelClassDataByPage')}",
            data: {
              type: vm.labelclass,
              page: vm.PageObj.page,
              limit: vm.PageObj.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.labelclassTableData = res.data;
                vm.PageObj.total = res.count;
              } else {
                vm.labelclassTableData = [];
              }
            },
          });
        },
        editLabelClass(name, id) {
          var vm = this;
          $.ajax({
            url: "{:url('Product/editLabelClass')}",
            data: {
              id: id,
              name: name,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  showClose: false,
                  message: "编辑成功",
                  type: "success",
                  duration: 3000,
                  onClose: function () {
                    vm.getLabelClassData();
                    vm.getLabelAndClassData();
                  },
                });
              } else {
                vm.$message({
                  showClose: false,
                  message: res.msg,
                  type: "error",
                  duration: 3000,
                });
              }
            },
          });
        },
        delLabelClass(id) {
          var vm = this;
          $.ajax({
            url: "{:url('Product/delLabelClass')}",
            data: {
              id: id,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  showClose: false,
                  message: "删除成功",
                  type: "success",
                  duration: 3000,
                });
                vm.getLabelClassData();
                vm.getLabelAndClassData();
              } else {
                vm.$message({
                  showClose: false,
                  message: res.msg,
                  type: "error",
                  duration: 3000,
                });
              }
            },
          });
        },
        currentChange(val) {
          // //console.log(val);
          this.PageObj.page = val;
          this.getLabelClassData();
        },
        manageLabelClass(type) {
          this.labelclass = type;
          this.labelClassDialog = true;
          this.PageObj = {
            page: 1,
            limit: 10,
            total: 0,
          };
          this.getLabelClassData();
        },

        getLabelAndClassData() {
          var vm = this;
          $.ajax({
            url: "{:url('getLabelAndClassData')}",
            data: {},
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.labelData = res.data["labelData"];
                vm.classData = res.data["classData"];
              } else {
                vm.$message({
                  showClose: false,
                  message: res.msg,
                  type: "error",
                });
              }
            },
          });
        },
        handleCommand(command) {
          return false;
          var c = command.split("-");
          this[c[0]](c[1]);
        },
        toEdit(id) {
          var lhash = "/product/index/" + id + "/step/edit";
          // window.open("#" + lhash);
          location.hash = lhash;
        },
        toEditeditor(id) {
          var lhash = "/product/index/" + id + "/step/editor";
          // window.open("#" + lhash);
          location.hash = lhash;
        },
        toPutaway(id) {
          var vm = this;
          this.$confirm("确定上架该产品吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('putawayAndSoldOut')}",
                data: {id: id, type: 1},
                type: "POST",
                success: function (res) {
                  // //console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: "上架成功!",
                    });
                    vm.getProductList();
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                      duration: 3000,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消上架",
              });
            });
        },
        toSoldOut(id) {
          var vm = this;
          this.$confirm("确定下架该产品吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              vm.productlistloading = true;
              $.ajax({
                url: "{:url('putawayAndSoldOut')}",
                data: {id: id, type: 2},
                type: "POST",
                success: function (res) {
                  // //console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: "下架成功!",
                      onClose() {
                        vm.getProductList();
                      },
                    });
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                  vm.productlistloading = false;
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消下架",
              });
            });
        },
        toDel(id) {
          var vm = this;
          this.$confirm("确定删除该产品吗，门店产品也将同时删除", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('putawayAndSoldOut')}",
                data: {id: id, type: -1, del: 1},
                type: "POST",
                success: function (res) {
                  // //console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: "删除成功!",
                    });
                    vm.getProductList();
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        toDetail(id) {
          var lhash = "";
          if (this.productlisttab == "first") {
            lhash = "/product/index/" + id + "/step/detail";
          } else {
            lhash = "/product/index/" + id + "/step/" + this.storeid;
          }
          // window.open("#" + lhash);
          location.hash = lhash;
        },
        toAddProduct() {
          location.hash = "/Product/addproduct";
        },
        getProductList() {
          var vm = this;
          vm.productlistloading = true;
          var storeid = this.storeid;
          if (this.productlisttab == "first") {
            storeid = 0;
            this.mchHisPage = vm.page;
          } else {
            this.storeHisPage = vm.page;
          }
          vm.total = 0;
          vm.tableData = [];
          $.ajax({
            url: "{:url('Product/getProductList')}",
            data: {
              cardType: vm.cardType,
              page: vm.page,
              limit: vm.limit,
              storeid: storeid,
              keyword: vm.keyword,
              classKeyword: vm.classKeyword,
              labelKeyword: vm.labelKeyword,
              statusKeyword: vm.statusKeyword,
              freightInfo: 1,
            },
            type: "POST",
            success: function (res) {
              // //console.log(res);
              vm.productlistloading = false;
              if (res.code == 0) {
                vm.total = res.count;
                vm.tableData = [];
                vm.tableData = res.data;
                // vm.$forceUpdate();
              }
            },
          });
        },
        handleSizeChange(val) {
          this.page = 1;
          this.limit = val;
          // //console.log(`每页 ${val} 条`);
          this.getProductList();
        },
        handleCurrentChange(val) {
          this.page = val;
          // //console.log(`当前页: ${val}`);
          this.getProductList();
        },
        handleClick() {
          this.getProductList();
        },
        getStoreList() {
          var vm = this;
          $.ajax({
            url: "{:url('TabStore/getStoreList')}",
            data: {},
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.storeList = res.data;
                if (res.data.length > 0) {
                  vm.storeid = res.data[0]["id"];
                }
              }
            },
          });
        },
        addToStore(id) {
          var vm = this;
          $.ajax({
            url: "{:url('Product/addToStore')}",
            data: {product_id: id},
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  type: "success",
                  message: "加入成功!",
                });
                vm.getProductList();
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
            },
          });
        },
        handleSelectionChange(val) {
          //console.log(val, '选中的数据');
          this.batchAddToStoreData = val;
          // console.log(val);
        },
        showhasAddStore(arr) {
          var storeList = this.storeList;
          var showAddStoreArr = [];
          for (let i = 0; i < storeList.length; i++) {
            if ($.inArray(storeList[i]["id"], arr) != -1) {
              showAddStoreArr.push(storeList[i]);
            }
          }
          this.showAddStore = true;
          this.showAddStoreArr = showAddStoreArr;
        },
        batchAddToStore() {
          ////console.log(this.batchAddToStoreData);
          if (this.batchAddToStoreData.length == 0) {
            return this.$message({
              message: "请先选择需要添加的产品",
              type: "warning",
            });
          } else {
            var batchArr = [];
            for (let i = 0; i < this.batchAddToStoreData.length; i++) {
              if (!this.batchAddToStoreData[i]["isadd"]) {
                batchArr.push({id: this.batchAddToStoreData[i]["id"]});
              }
            }
            if (batchArr.length == 0) {
              return this.$message({
                message: "所选产品已全部加入门店",
                type: "warning",
              });
            } else {
              batchArr = JSON.stringify(batchArr);
              var vm = this;
              vm.productlistloading = true;
              $.ajax({
                url: "{:url('batchAddToStore')}",
                data: {
                  batchProduct: batchArr,
                },
                type: "POST",
                success: function (res) {
                  //console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getProductList();
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                  vm.productlistloading = false;
                },
              });
            }
            //console.log(batchArr);
          }
        },
        // 生成二维码
        getCode(id, storeid) {
          var vm = this;
          $.ajax({
            url: "{:url('getProductCode')}",
            data: {
              id: id,
              storeid: storeid,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.productCode = res.data;
              }
            },
          });
        },
      },
      created: function () {
        // //console.log(this.productlisttab);
        this.getProductList();
        if (!this.isstore) {
          this.getStoreList();
        }
        this.getLabelAndClassData();
      },
      watch: {
        // 调价管理
        adjustPriceDialog(n) {
          if (n) {
            this.getAdjustPriceData();
          }
        },
        adjustPriceSetDialog(n) {
          if (n) {
            this.getAdjustPriceData();
          }
        },
        productFreightDialog(n) {
          if (n) {
            this.getProductFreightData();
          }
        },
        editFreightDialog(n) {
          if (n) {
            this.getProductFreightData();
          }
        },
        classKeyword: function (n) {
          this.page = 1;
          this.getProductList();
        },
        labelKeyword: function (n) {
          this.page = 1;
          this.getProductList();
        },
        statusKeyword: function (n) {
          this.page = 1;
          this.getProductList();
        },
        productlisttab(n) {
          if (n == "first") {
            this.page = this.mchHisPage;
          } else {
            this.page = this.storeHisPage;
          }
          this.batchAddToStoreData = [];
          this.getProductList();
        },
        batchAddToStoreData(n) {
          // console.log(n,111);
        },
      },
    });
  });
</script>
<style>
  .demo-table-expand {
    font-size: 0;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    /*width: 50%;*/
  }
  .demo-table-expand .el-form-item--small .el-form-item__content,
  .demo-table-expand .el-form-item--small .el-form-item__label {
    line-height: 20px;
  }
</style>
