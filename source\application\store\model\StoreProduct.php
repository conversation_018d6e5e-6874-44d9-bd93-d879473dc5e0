<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-04-22
 * Time: 15:39
 */

namespace app\store\model;


use think\Db;
use app\pagecommon\model\Material as MaterialModel;
use app\store\model\ProductSkuAttr as ProductSkuAttrModel;
use app\store\model\ProductLabelClass as ProductLabelClassModel;
use app\store\model\StoreProductSkuAttr as StoreProductSkuAttrModel;
use app\store\model\ProductSku as ProductSkuModel;
use think\Config;
use app\store\model\PayOrder as PayOrderModel;

// 自动拆分
use app\store\model\Productunwrap as ProductunwrapModel;
use app\store\model\Product as ProductModel;
use app\store\model\Memberlevel as MemberlevelModel;
use app\store\model\Member as MemberModel;
use app\store\model\MemberPrice as MemberPriceModel;

//<!--[meiye_09_01]-->
class StoreProduct extends BaseModel
{
    protected $name = 'store_product';
    protected $updateTime = false;
    public static $uid;
    public static $MaterialModel;
    public static $ProductSkuAttrModel;
    public static $ProductLabelClassModel;
    public static $StoreProductSkuAttrModel;
    public static $ProductSkuModel;

    public static $extraData;

    public function setExtraData($data)
    {
        self::$extraData = $data;
        return $this;
    }

    /**
     * 模型基类初始化
     */

    public static function init()
    {
        parent::init();
        self::$MaterialModel = new MaterialModel;
        self::$ProductSkuAttrModel = new ProductSkuAttrModel;
        self::$ProductLabelClassModel = new ProductLabelClassModel;
        self::$StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
        self::$ProductSkuModel = new ProductSkuModel;
    }

    /**
     * 加入门店数据
     * @param array $addData
     * @param array $skuAddData
     * @return bool|string
     */
    public function add(array $addData, array $skuAddData)
    {
        // 开启事务
        Db::startTrans();
        try {
            $time = time();
            if (!isset($addData['addtime'])) {
                $addData['addtime'] = $time;
            }
            // 添加商品
            $this->insert($addData);
            $id = $this->getLastInsID();
            if (count($skuAddData) > 0) {
                foreach ($skuAddData as $k => $v) {
                    $skuAddData[$k]['store_product_id'] = $id;
                    if (!isset($v['addtime'])) {
                        $skuAddData[$k]['addtime'] = $time;
                    }
                }
            }
            Db::name('store_product_skuattr')
                ->insertAll($skuAddData);
            Db::commit();
            return $id;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            Db::rollback();
        }
        return false;
    }

    /**
     * 编辑商品信息
     * @param array $editData
     * @param array $skueditData
     * @return bool
     */
    public function edit(array $editData, array $skueditData = array())
    {
        // 开启事务
        Db::startTrans();
        try {
            $this->update($editData);
            if (count($skueditData) > 0) {
                foreach ($skueditData as $k => $v) {
                    Db::name('store_product_skuattr')
                        ->update($v);
                }
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            Db::rollback();
        }
        return false;
    }

    /**
     * 查找信息
     * @param array $where
     * @param string $field
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getFind(array $where, $field = '*')
    {
        $data = $this->where($where)
            ->field($field)
            ->find();
        return $data;
    }

    public function getRealFind(array $where, $field = 's.*')
    {
        $prefix = Config::get('prefix');
        $data = $this
            ->alias('s')
            ->join("{$prefix}product p", 'p.id = s.product_id')
            ->where($where)
            ->field($field)
            ->cache(true, 10)
            ->find();
        return $data;
    }

    /**
     * 门店商品数据 分页
     * @param array $where
     * @param $page
     * @param $limit
     * @param array $append
     * @param string $order
     * @return array|false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getStoreProductDataByPage(array $where, $page, $limit, $append = array(), $order = 's.addtime desc')
    {
        $prefix = Config::get('prefix');
        $field = array(
            'p.product_name',
            'p.product_name as originalName',
            'p.product_barcode',
            'p.product_classid',
            'p.product_labelids',
            'p.status',
            'p.status as headquarterStatus',
            'p.video_id',
            'p.img_id',
            'p.unit',
            'p.allproductnum',
            'p.sellproductnum',
            'p.cost_price',
            'p.price',
       
            'p.price_tag',
            'p.freight',
            'p.sell_online',
            'p.addtime',
            'p.detail',
            's.id',
            's.addtime as s_addtime',
            's.product_name as store_product_name',
            's.product_id',
            's.issku',
            's.isset',
            's.material_id as s_material_id',
            's.price as s_s_price',
            's.totalnum',
            's.totalsell',
            's.status as store_status',
            's.store_id as storeId',
            's.istotalsell',
            's.member_price',
            's.id as goodsId',
            'p.consumable_type',    // 耗材包装类型
            'p.consumable_class',   // 耗材包装类型
            'p.sell',                // 耗材包装类型
            'p.small_content',      // 含量
            'p.small_unit',         // 耗用单位
            's.small_content as storeSmallContent',       // 消耗库存
            's.sort',               // 排序
        );
        // 库存预警
        if (in_array('stockWarningStatus', $append)) {
            $field[] = 's.stock_warning';
            $field[] = 's.default_warning_min_num';
            $field[] = 's.default_warning_max_num';
        }
        if (in_array('freightInfo', $append)) {
            $field[] = 'p.freight_id';
        }
        if (isset($where['_sql']) && $where['_sql']) {
            $newwhere = $where['_sql'];
            unset($where['_sql']);
            $data = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->where($newwhere)
                ->page($page, $limit)
                ->order($order)
                ->field($field)
                ->select();
        } else {
            $data = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->page($page, $limit)
                ->order($order)
                ->field($field)
                ->select();
        }
        if ($data && count($append) > 0) {
            if (isset($append['hide'])) {
                $hide = $append['hide'];
                unset($append['hide']);
            } else {
                $hide = array('addtime');
            }
            $data->append($append)->hidden($hide)->toArray();
        } else {
            $data = array();
        }
        return $data;
    }

    /**
     * 门店商品数据 分页
     * @param array $where
     * @param $page
     * @param $limit
     * @param array $append
     * @param string $order
     * @return array|false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getStoreProductDataAll(array $where, $append = array(), $order = 's.addtime desc')
    {
        $prefix = Config::get('prefix');
        $field = array(
            'p.product_name',
            'p.product_name as originalName',
            'p.product_barcode',
            'p.product_classid',
            'p.product_labelids',
            'p.status',
            'p.status as headquarterStatus',
            'p.video_id',
            'p.img_id',
            'p.unit',
            'p.allproductnum',
            'p.sellproductnum',
            'p.cost_price',
            'p.price',
            'p.price_tag',
            'p.freight',
            'p.sell_online',
            'p.addtime',
            'p.detail',
            's.id',
            's.addtime as s_addtime',
            's.product_name as store_product_name',
            's.product_id',
            's.issku',
            's.isset',
            's.material_id as s_material_id',
            's.price as s_s_price',
            's.totalnum',
            's.totalsell',
            's.status as store_status',
            's.store_id as storeId',
            's.istotalsell',
            's.id as goodsId'
        );
        // 库存预警
        if (in_array('stockWarningStatus', $append)) {
            $field[] = 's.stock_warning';
            $field[] = 's.default_warning_min_num';
            $field[] = 's.default_warning_max_num';
        }
        if (in_array('freightInfo', $append)) {
            $field[] = 'p.freight_id';
        }
        if (isset($where['_sql']) && $where['_sql']) {
            $newwhere = $where['_sql'];
            unset($where['_sql']);
            $data = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->where($newwhere)
                ->order($order)
                ->field($field)
                ->select();
        } else {
            $data = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->order($order)
                ->field($field)
                ->select();
        }
        if ($data && count($append) > 0) {
            if (isset($append['hide'])) {
                $hide = $append['hide'];
                unset($append['hide']);
            } else {
                $hide = array('addtime');
            }
            $data->append($append)->hidden($hide)->toArray();
        } else {
            $data = array();
        }
        return $data;
    }

    /**
     * 判断库存预警
     * @param $value
     * @param $data
     */
    public function getstockWarningStatusAttr($value, $data)
    {
        if (!isset($data['stock_warning']) || !isset($data['default_warning_min_num']) || !isset($data['default_warning_max_num'])) {
            return '库存预警字段缺失';
        }
        if (!isset($data['totalnum'])) {
            return '库存字段缺失';
        }
        $stockWarning = 0;
        // 是否开启预警：0，（开启并使用默认值），1，开启（自定义预警值），2，关闭预警。
        if ($data['stock_warning'] == 0) {
            $extraData = self::$extraData;
            if (!isset($extraData['storeGlobalSet'])) {
                return '库存预警默认缺失';
            }
            $storeGlobalSet = $extraData['storeGlobalSet'];
            if ($data['totalnum'] - $storeGlobalSet['default_warning_min_num'] < 0) {
                $stockWarning = -1;
            } else if ($data['totalnum'] - $storeGlobalSet['default_warning_max_num'] > 0) {
                $stockWarning = 1;
            }
        } else if ($data['stock_warning'] == 1) {
            if ($data['totalnum'] - $data['default_warning_min_num'] < 0) {
                $stockWarning = -1;
            } else if ($data['totalnum'] - $data['default_warning_max_num'] > 0) {
                $stockWarning = 1;
            }
        } else if ($data['stock_warning'] == 2) {
            $stockWarning = 2;
        } else if ($data['stock_warning'] == 3) {
            $stockWarning = 2;
        }
        return $stockWarning;
    }

    /**
     * 判断库存预警
     * @param $value
     * @param $data
     */
    public function getstockWarningNumAttr($value, $data)
    {
        $stockWarning = array(
            'maxNum' => 0,
            'minNum' => 0,
            'errorCode' => 0,
            'errorMsg' => 'success'
        );
        if (!isset($data['stock_warning']) || !isset($data['default_warning_min_num']) || !isset($data['default_warning_max_num'])) {
            $stockWarning['errorMsg'] = '库存预警字段缺失';
            $stockWarning['errorCode'] = 1;
            return $stockWarning;
        }
        if (!isset($data['totalnum'])) {
            $stockWarning['errorMsg'] = '库存字段缺失';
            $stockWarning['errorCode'] = 1;
            return $stockWarning;
        }
        // 是否开启预警：0，（开启并使用默认值），1，开启（自定义预警值），2，关闭预警，3，自定义并关闭预警。
        if ($data['stock_warning'] == 0 || $data['stock_warning'] == 2) {
            $extraData = self::$extraData;
            if (!isset($extraData['storeGlobalSet'])) {
                $stockWarning['errorMsg'] = '库存预警默认缺失';
                $stockWarning['errorCode'] = 1;
                return $stockWarning;
            }
            $storeGlobalSet = $extraData['storeGlobalSet'];
            $stockWarning['maxNum'] = $storeGlobalSet['default_warning_max_num'];
            $stockWarning['minNum'] = $storeGlobalSet['default_warning_min_num'];
        } else if ($data['stock_warning'] == 1 || $data['stock_warning'] == 3) {
            $stockWarning['maxNum'] = $data['default_warning_max_num'];
            $stockWarning['minNum'] = $data['default_warning_min_num'];
        }
        return $stockWarning;
    }

    /**
     * 获取待发货数量
     */
    public function getShipmentNumAttr($value, $data)
    {
        // $data  中 必须包含 storeId 字段 和  goodsId (店铺产品的 Id );
        if (!isset($data['storeId'])) {
            return 'storeId为必须字段';
        }
        if (!isset($data['goodsId'])) {
            return 'goodsId为必须字段';
        }
        $map = array(
            'payOrder.store_id' => $data['storeId'],
            'payOrder.is_refund' => 2,
            'payOrder.state' => 2,
            'orderDetail.type' => 2,
            'orderDetail.goods_id' => $data['goodsId'],
            'orderDetail.sku_id' => 0
        );
        $PayOrderModel = new PayOrderModel;
        $num = $PayOrderModel->sumNeedShipmentNum($map);
        return $num;
    }

    /**
     * 获取商品详情  find
     * @param array $where
     * @param $append
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getStoreProductData(array $where, $append)
    {
        if (isset($where['uid'])) {
            self::$uid = $where['uid'];
            unset($where['uid']);
        }
        $prefix = Config::get('prefix');

        $field = array(
            'p.*',
            'p.price as archivePrice',
            's.product_name as store_product_name',
            's.id as store_product_id',
            's.product_id',
            's.isset',
            's.member_price',
            's.material_id as s_material_id',
            's.price as s_s_price',
            's.cost_price as s_s_cost_price',
            's.totalnum',
            's.totalsell',
            's.store_id as storeId',
            's.addtime as s_addtime',
            's.istotalsell',
            's.status as store_status',
            'p.status as mer_status',
			'p.sell_online'
        );
        $data = $this
            ->alias('s')
            ->join("{$prefix}product p", 'p.id = s.product_id')
            ->where($where)
            ->field($field)
            ->find();
        if ($data) {
            if (isset($append['hide'])) {
                $hide = $append['hide'];
                unset($append['hide']);
            } else {
                $hide = array('addtime');
            }
            if (isset($data['adjust_price_id']) && $data['adjust_price_id'] && !in_array('adjustPriceInfo',$hide)) {
                $data->adjustPriceInfo;
            }
            $data->append($append)->hidden($hide)->toArray();
        } else {
            $data = array();
        }
        return $data;
    }

    /*
     * 关联模型
     * */
    public function adjustPriceInfo()
    {
        return $this->hasOne('app\store\model\Adjustprice', 'id', 'adjust_price_id')
            ->field('*,type as typeName');
    }

    public function getarchivePriceAttr($value)
    {
        return number_format(round($value / 100, 2), 2,'.','');
    }

    //合伙人
    public function getStoreProductDataByPages(array $where, $page, $limit, $append = array(), $order = 's.addtime desc')
    {
        $prefix = Config::get('prefix');
        $field = array(
            'p.product_name',
            'p.img_id',
            'p.price',
            'p.price_tag',
            's.id',
            's.product_id',
            's.issku',
            's.isset',
            's.price as s_s_price',
            's.store_id as storeId'
        );
        $data = $this
            ->alias('s')
            ->join("{$prefix}product p", 'p.id = s.product_id')
            ->where($where)
            ->page($page, $limit)
            ->order($order)
            ->field($field)
            ->select();

        if ($data) {
            $data->append($append)->toArray();
        } else {
            $data = array();
        }
        return $data;
    }

    /**
     * 获取非规格商品详情
     * @param array $where
     * @param $append
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getStoreNoSkuProductInfo(array $where, $append, $field = '')
    {
        $prefix = Config::get('prefix');
        $fields = array(
            'p.product_name',
            'p.product_barcode',
            'p.product_classid',
            'p.product_labelids',
            'p.status',
            'p.video_id',
            'p.img_id',
            'p.unit',
            'p.allproductnum',
            'p.sellproductnum',
            'p.cost_price',
            'p.price',
            'p.price_tag',
            'p.freight',
            'p.sell_online',
            'p.addtime',
            'p.detail',
            's.id',
            's.product_name as store_product_name',
            's.product_id',
            's.issku',
            's.isset',
            's.material_id as s_material_id',
            's.price as s_s_price',
            's.totalnum',
            's.totalsell',
            's.status as store_status',
            's.store_id as storeId'
        );
        $field = $field ? $field : $fields;
        $data = $this
            ->alias('s')
            ->join("{$prefix}product p", 'p.id = s.product_id')
            ->where($where)
            ->field($field)
            ->find();
        if ($data) {
            $data->append($append)->hidden(array('addtime'))->toArray();
        } else {
            $data = array();
        }
        return $data;
    }

    /**
     * 获取规格商品详情  是一个回调
     * @param array $where
     * @param $append
     * @return mixed
     */
    public function getStoreSkuProductInfo(array $where, $append)
    {
        $data = self::$StoreProductSkuAttrModel->getStoreSkuProductInfo($where, $append);
        return $data;
    }

    /**
     * 获取符合条件的数据条数
     * @param array $where
     * @return int|string
     * @throws \think\Exception
     */
    public function getCount(array $where)
    {
        $prefix = Config::get('prefix');
        if (isset($where['_sql']) && $where['_sql']) {
            $newwhere = $where['_sql'];
            unset($where['_sql']);
            $count = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->where($newwhere)
                ->count();
        } else {
            $count = $this
                ->alias('s')
                ->join("{$prefix}product p", 'p.id = s.product_id')
                ->where($where)
                ->count();
        }
        return $count;
    }

    /**
     * 获取某商品 有哪些门店加入。
     * @param array $where
     * @return array
     */
    public function hasAddStoreList(array $where)
    {
        $data = $this->where($where)
            ->column('store_id');
        return $data;
    }

    /**
     * 获取门店所有商品 所属的分类 ，标签
     * @param array $where
     * @param string $column
     * @return array
     */
    public function getColumnGroupClassLabel(array $where, $column = 's.id')
    {
        $prefix = Config::get('prefix');
        $data = $this->alias('s')
            ->join("{$prefix}product p", 'p.id = s.product_id')
            ->where($where)
            ->cache(30)
            ->group($column)
            ->column($column);
        return $data;
    }

    /*
     * 门店非规格商品添加库存 并添加记录
     * */
    public function addnoSkuStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $before_num = $this
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $re = $this
            ->where(array('id' => $where['id']))
            ->setInc('totalnum', $num);
        if (!$re) {
            return false;
        }
        $after_num = $before_num + $num;
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        if ($num > 0) {
            $common['type'] = 1;
        } else {
            $common['type'] = 2;
        }
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
     * 门店非规格商品添加库存 并添加记录
     * */
    public function addSkuStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $thiss = Db::name('store_product_skuattr');
        $before_num = $thiss
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $re = $thiss->where(array('id' => $where['id']))->setInc('totalnum', $num);
        if (!$re) {
            return false;
        }
        $after_num = $before_num + $num;
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        if ($num > 0) {
            $common['type'] = 1;
        } else {
            $common['type'] = 2;
        }
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
    * 门店非规格商品减少库存 并添加记录
    * */
    public function reduceNoSkuStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $before_num = $this
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $re = $this
            ->where(array('id' => $where['id']))
            ->setDec('totalnum', $num);
        if (!$re) {
            return false;
        }
        $after_num = $before_num - $num;
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        $common['type'] = 2;
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
     * 门店非规格商品减少库存 并添加记录
     * */
    public function reduceSkuStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $thiss = Db::name('store_product_skuattr');
        $before_num = $thiss
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $re = $thiss->where(array('id' => $where['id']))->setDec('totalnum', $num);
        if (!$re) {
            return false;
        }
        $after_num = $before_num - $num;
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        $common['type'] = 2;
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
     * 产品销售(非规格产品)减少库存，并添加库存记录 （耗材出库：09-20 新增大包装 耗材自动 拆分）
     * */
    public function noSkuGoodsSellStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        // 耗材领料出库
        $storeProductRealStatus = false;
        if(isset($common['storeProductRealStatus']) && $common['storeProductRealStatus'] == 6){
            $storeProductRealStatus = true;
        }
        $field = array(
            'id',
            'product_id',
            'totalnum',
            'status as storeProductRealStatus',
            'status',
            'small_content',    // 散装数量
        );
        $storeProduct = $this
            ->where(array('id' => $where['id'],'store_id'=>$common['storeid']))
            ->field($field)
            ->find();
        if (!$storeProduct) {
            return false;
        } else if ($storeProductRealStatus) {
            $res = $this->automaticSplit($storeProduct, $num, $common);
            if ($res === false) {
                return false;
            } else if ($res === true) {
                if($storeProductRealStatus){
                    $before_num = $storeProduct['small_content'];
                }else{
                    $before_num = $storeProduct['totalnum'];
                }
            } else {
                $before_num = $res;
            }
        } else {
            $before_num = $storeProduct['totalnum'];
        }
        if($storeProductRealStatus){
            unset($common['storeProductRealStatus']);
        }
        // 耗材消耗出库
        if($storeProductRealStatus){
            $arr = array(
                'small_content' => array('dec', $num),
            );
            $re = $this->where(array('id' => $where['id']))->setField($arr);
            if (!$re) {
                return false;
            }
            $after_num = $before_num - $num; // 减少库存
            $common['before_small_num'] = $before_num;
            $common['small_num'] = $num;
            $common['after_small_num'] = $after_num;
            $common['addtime'] = time();
            if(isset($common['type']) && $common['type']){

            }else{
                $common['type'] = 2;
            }
            $re = Db::name('store_product_record')->insert($common);
            if (!$re) {
                return false;
            }
            return true;
        }else{
            $arr = array(
                'totalnum' => array('dec', $num),
                'totalsell' => array('inc', $num)
            );
            $re = $this->where(array('id' => $where['id']))->setField($arr);
            if (!$re) {
                return false;
            }
            $after_num = $before_num - $num; // 减少库存
            $common['before_num'] = $before_num;
            $common['num'] = $num;
            $common['after_num'] = $after_num;
            $common['addtime'] = time();
            if(isset($common['type']) && $common['type']){
                //$common['type'] = 2;
            }else{
                $common['type'] = 2;
            }
            $re = Db::name('store_product_record')->insert($common);
            if (!$re) {
                return false;
            }
            return true;
        }
    }

    /**
     * @param $id   店铺产品（耗材）的id
     * @param $num  拆分后需要的数量
     */
    public function automaticSplit($storeProduct, $num, $common)
    {
        // 未找到该产品返回失败
        if (!$storeProduct) {
            return false;
        } else {
            if(isset($common['storeProductRealStatus']) && $common['storeProductRealStatus'] == 6){
                // 耗材领料出库
//                echo 1;
                //unset($common['storeProductRealStatus']);
                if($storeProduct['small_content'] - $num >= 0){
                    // 耗材领料
                    // 散装数量充足
                    return true;
                }else{
                    // 散装数量不足
                    try {// 自动拆分   1，查询出拆分设置
                        // 查询1基本单位的含量
                        $ProductModel = new ProductModel;
                        $map = array(
                            'id'=>$storeProduct['product_id'],
                            'merchant_id'=>$common['merchantid'],
                            'issku'=>2,                             // 非规格
                        );
                        $fields = array(
                            'small_content',
                            'small_unit',
                            'id'
                        );
                        $ProductData = $ProductModel->getFind($map,$fields);
                        $smallContent = 0;// 1基本单位对应的含量
                        if($ProductData && isset($ProductData['small_content'])){
                            $smallContent = $ProductData['small_content'];
                        }else{
                            return false;
                        }
                        // 计算需要拆分的 基本单位数量
                        $needSmallNum = ceil(($num - $storeProduct['small_content'])/$smallContent); // 最小拆分数量
//                        echo '('.$needSmallNum.')';
                        $storeid = $common['storeid'];
                        if($storeProduct['totalnum'] - $needSmallNum >= 0){
                            // 当前数量基本单位数量足够拆分
//                            echo 2;
                        }else{
                            // 拆分大包装商品
                            $ProductunwrapModel = new ProductunwrapModel;
                            $mapData = array(
                                'map' => array(
                                    'merchantid' => $common['merchantid'],
                                    'small_package_id' => $storeProduct['product_id']
                                ),
                                'field' => array(
                                    'num', 'big_package_id as product_id'
                                ),
                                'order' => 'sort desc,addtime asc'
                            );
                            $unwrapData = $ProductunwrapModel->getAllData($mapData);
                            $minSplitNum = $num - $needSmallNum;// 最小拆分数量
                            $beforeNum = $storeProduct['totalnum'];
                            $allSplitNum = 0;
                            foreach ($unwrapData as $k => $v) {
                                $hasSplitNum = $this->doAutomaticSplit($v['num'], $v['product_id'], $minSplitNum, $storeid, $common['merchantid'], $storeProduct['id'], $beforeNum);
                                if ($hasSplitNum > 0) {
                                    $allSplitNum += $hasSplitNum;
                                    $minSplitNum -= $hasSplitNum;
                                    $beforeNum += $hasSplitNum;
                                }
                                if ($minSplitNum <= 0) {
                                    break;
                                }
                            }
                        }
                        // 无论基本单位耗材是否足够，均把 $needSmallNum 数量拆成散装（基本单位可能变为负数）
                        // 拆分
                        $arr = array(
                            'totalnum' => array('dec', $needSmallNum),
                            'totalsell' => array('inc', $needSmallNum),
                            'small_content'=>array('inc',$needSmallNum*$smallContent)
                        );
//                        print_r($arr);
                        $re = $this->where(array('id' => $storeProduct['id']))->setField($arr);
                        if ($re) {
//                            echo 3;
                            $common = array(
                                'userid' => 0,
                                'addtime' => time(),
                                'bill_type' => 3,// 大包装拆分成小包装，
                                'bill_no' => '',
                                'storeid' => $storeid,
                                'merchantid' => $common['merchantid']
                            );
                            $common['store_product_id'] = $storeProduct['id'];
                            $common['store_sku_id'] = 0;
                            $common['before_num'] = $storeProduct['totalnum'];
                            $common['num'] = $needSmallNum;
                            $common['after_num'] = $storeProduct['totalnum'] - $needSmallNum;
                            // 散装数量增加
                            $common['before_small_num'] = $storeProduct['small_content'];
                            $common['small_num'] = $needSmallNum*$smallContent;
                            $common['after_small_num'] = $storeProduct['small_content'] + $needSmallNum*$smallContent;
                            $common['small_content'] = $smallContent;
                            $common['type'] = 4;    // 类型：1，入库，2，出库，3，拆分增加，4，拆分减少
                            $re = Db::name('store_product_record')->insert($common);
//                            echo $common['after_small_num'];
                            return $common['after_small_num']; // 拆分散装后的数量
                        }else{
                            return false;
                        }
                    } catch (\Exception $e) {
//                        echo $e->getMessage();
                        return false;
                    }
                }
            }else{
                // 非耗材领料出库
                if ($storeProduct['totalnum'] - $num >= 0) {
                    // 非耗材领料出库
                    // 可用库存大于出库库存 返回成功，无需拆分
                    return true;
                } else {
                    try {// 自动拆分   1，查询出拆分设置
                        $ProductunwrapModel = new ProductunwrapModel;
                        $storeid = $common['storeid'];
                        $mapData = array(
                            'map' => array(
                                'merchantid' => $common['merchantid'],
                                'small_package_id' => $storeProduct['product_id']
                            ),
                            'field' => array(
                                'num', 'big_package_id as product_id'
                            ),
                            'order' => 'sort desc,addtime asc'
                        );
                        $unwrapData = $ProductunwrapModel->getAllData($mapData);
                        $minSplitNum = $num - $storeProduct['totalnum'];// 最小拆分数量
                        $beforeNum = $storeProduct['totalnum'];
                        $allSplitNum = 0;
                        foreach ($unwrapData as $k => $v) {
                            $hasSplitNum = $this->doAutomaticSplit($v['num'], $v['product_id'], $minSplitNum, $storeid, $common['merchantid'], $storeProduct['id'], $beforeNum);
                            if ($hasSplitNum > 0) {
                                $allSplitNum += $hasSplitNum;
                                $minSplitNum -= $hasSplitNum;
                                $beforeNum += $hasSplitNum;
                            }
                            if ($minSplitNum <= 0) {
                                return $beforeNum;
                            }
                        }
                        return $beforeNum;
                    } catch (\Exception $e) {
                        return true;
                    }
                }
            }
        }
    }
    // 确认拆分 并添加拆分记录  返回拆分数量
    /*
     * store_product_record
        单据类型：
        1，采购入库（出库），
        2，销售出库(入库)，
        3，大包装拆分成小包装，
        6，产品赠送出库（退回入库）
        11，门店要货(退货)，
        12，盘点(盘盈/盘亏/盘平)，
        13，报损（报溢），
        14，其他入库（出库），
        15，领料出库，
        16，仓库被要货出库
        17，调拨单
        18，退货给供应商
        19，退货单
        20，耗材领料出库（回库）
        其他，请写备注*/
    /**
     * @param $num          可拆分数量
     * @param $productId    大包装id
     * @param $minSplitNum  目标数量
     * @return int          返回拆分后小包装的数量
     */
    private function doAutomaticSplit($num, $productId, $minSplitNum, $storeid, $merchantid, $smallProductId, $beforeNum)
    {
        // 单据类型：1，采购入库（出库），2，销售出库(入库)，3，大包装拆分成小包装，
        //1.查询大包装是否 在本店存在
        $map = array(
            'merchant_id' => $merchantid,
            'store_id' => $storeid,
            'product_id' => $productId,
            'status' => array('in', '6,7'),
            'issku' => 2,
            'totalnum' => array('gt', 0)
        );
        $field = array(
            'totalnum', 'id'
        );
        $product = $this
            ->where($map)
            ->field($field)
            ->find();
        if (!$product) {
            return 0;
        } else {
            $minNum = ceil($minSplitNum / $num);
            $splitNum = ($product['totalnum'] >= $minNum) ? $minNum : $product['totalnum'];
            // 拆分
            $arr = array(
                'totalnum' => array('dec', $splitNum),
                'totalsell' => array('inc', $splitNum)
            );
            $re = $this->where(array('id' => $product['id']))->setField($arr);
            if ($re) {
                $common = array(
                    'userid' => 0,
                    'addtime' => time(),
                    'bill_type' => 3,// 大包装拆分成小包装，
                    'bill_no' => '',
                    'storeid' => $storeid,
                    'merchantid' => $merchantid
                );
                $common['store_product_id'] = $product['id'];
                $common['store_sku_id'] = 0;
                $common['before_num'] = $product['totalnum'];
                $common['num'] = $splitNum;
                $common['after_num'] = $product['totalnum'] - $splitNum;
                $common['type'] = 4;    // 类型：1，入库，2，出库，3，拆分增加，4，拆分减少
                $re = Db::name('store_product_record')->insert($common);
                if ($re) {
                    // 拆分
                    $arr2 = array(
                        'totalnum' => array('inc', $splitNum * $num)
                    );
                    $re = $this->where(array('id' => $smallProductId))->setField($arr2);
                    $common = array(
                        'userid' => 0,
                        'addtime' => time(),
                        'bill_type' => 3,// 门店采购入库
                        'bill_no' => '',
                        'storeid' => $storeid,
                        'merchantid' => $merchantid
                    );
                    $common['store_product_id'] = $smallProductId;
                    $common['store_sku_id'] = 0;
                    $common['before_num'] = $beforeNum;
                    $common['num'] = $splitNum * $num;
                    $common['after_num'] = $beforeNum + $splitNum * $num;
                    $common['type'] = 3;    // 类型：1，入库，2，出库，3，拆分增加，4，拆分减少
                    $re = Db::name('store_product_record')->insert($common);
                    return $splitNum * $num;
                }
            }
        }
        return 0;
    }

    /*
     * 产品销售(非规格产品)减少库存，并添加库存记录
     * */
    public function skuGoodsSellStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $thiss = Db::name('store_product_skuattr');
        $before_num = $thiss
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $arr = array(
            'totalnum' => array('dec', $num),
            'totalsell' => array('inc', $num)
        );
        $re = $thiss->where(array('id' => $where['id']))->setField($arr);
        if (!$re) {
            return false;
        }
        $after_num = $before_num - $num; // 减少库存
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        if(isset($common['type']) && $common['type']){
            //$common['type'] = 2;
        }else{
            $common['type'] = 2;
        }
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
     * 产品退款(非规格产品)增加库存，并添加库存记录
     * */
    public function noSkuGoodsRefundStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        // 耗材领料出库
        if(isset($common['storeProductRealStatus']) && $common['storeProductRealStatus'] == 6){
            unset($common['storeProductRealStatus']);
            $before_num = $this
                ->where(array('id' => $where['id']))
                ->value('small_content');
            $arr = array(
                'small_content' => array('inc', $num)
            );
            $re = $this->where(array('id' => $where['id']))->setField($arr);
            if (!$re) {
                return false;
            }
            $after_num = $before_num + $num; // 减少库存
            $common['before_small_num'] = $before_num;
            $common['small_num'] = $num;
            $common['after_small_num'] = $after_num;
            $common['addtime'] = time();
            if(isset($common['type']) && $common['type']){
                //$common['type'] = 2;
            }else{
                $common['type'] = 1;//类型：1，入库，2，出库。
            }

            $re = Db::name('store_product_record')->insert($common);
            if (!$re) {
                return false;
            }
            return true;
        }else{
            $before_num = $this
                ->where(array('id' => $where['id']))
                ->value('totalnum');
            $arr = array(
                'totalnum' => array('inc', $num),
                'totalsell' => array('dec', $num)
            );
            $re = $this->where(array('id' => $where['id']))->setField($arr);
            if (!$re) {
                return false;
            }
            $after_num = $before_num + $num;// 增加库存
            $common['before_num'] = $before_num;
            $common['num'] = $num;
            $common['after_num'] = $after_num;
            if(isset($common['type']) && $common['type']){
                //$common['type'] = 2;
            }else{
                $common['type'] = 1;//类型：1，入库，2，出库。
            }

            $re = Db::name('store_product_record')->insert($common);
            if (!$re) {
                return false;
            }
            return true;
        }
    }

    /*
     * 产品退款(非规格产品)增加库存，并添加库存记录
     * */
    public function skuGoodsRefundStock($common, $where, $num)
    {
        if ($num == 0) {
            return false;
        }
        $thiss = Db::name('store_product_skuattr');
        $before_num = $thiss
            ->where(array('id' => $where['id']))
            ->value('totalnum');
        $arr = array(
            'totalnum' => array('inc', $num),
            'totalsell' => array('dec', $num)
        );
        $re = $thiss->where(array('id' => $where['id']))->setField($arr);
        if (!$re) {
            return false;
        }
        $after_num = $before_num + $num;// 增加库存
        $common['before_num'] = $before_num;
        $common['num'] = $num;
        $common['after_num'] = $after_num;
        if(isset($common['type']) && $common['type']){
            //$common['type'] = 2;
        }else{
            $common['type'] = 1;//类型：1，入库，2，出库。
        }
        $re = Db::name('store_product_record')->insert($common);
        if (!$re) {
            return false;
        }
        return true;
    }

    /*
         * 往查询数据追加信息
         * */
    public function getskuIdAttr($value, $data)
    {
        return 0;
    }

    // 获取门店价格
    public function getpriceAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            return $data['price'];
        }
        if (isset($data['isset']) && $data['isset'] == 1) {
            // 使用 门店自己设置的价格
            $data['s_s_price'] = $data['s_s_price'] ? $data['s_s_price'] : 0;
            return $data['s_s_price'];
        } else if (isset($data['isset']) && $data['isset'] == 2) {
            // 使用 总部设置的价格
            $data['price'] = $data['price'] ? $data['price'] : 0;
            return $data['price'];
        }
    }

    // 获取门店成本价
    public function getcostPriceAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            return $data['cost_price'];
        }
        // 使用 门店自己设置的成本价。
        if (isset($data['isset']) && $data['isset'] == 1) {
            if (isset($data['s_s_cost_price']) && $data['s_s_cost_price']) {
                return $data['s_s_cost_price'];
            } else {
                $data['cost_price'] = $data['cost_price'] ? $data['cost_price'] : 0;
                return $data['cost_price'];
            }
        } else if (isset($data['isset']) && $data['isset'] == 2) {
            // 使用 总部设置的价格
            $data['cost_price'] = $data['cost_price'] ? $data['cost_price'] : 0;
            return $data['cost_price'];
        } else {
            return '数据错误';
        }
    }

    protected $imgarrAttrData = array();
    // 商品图片信息
    public function getimgarrAttr($value, $data)
    {
        if (isset($data['isset']) && $data['isset'] == 2) {
            if (isset($data['img_id']) && $data['img_id'] != 0) {
                $imgarrAttrData = $this->imgarrAttrData;
                foreach ($imgarrAttrData as $k=>$v){
                    if($v['imgId'] == $data['img_id']){
                        return $v['data'];
                    }
                }
                $arr = self::$MaterialModel->getFilePathByIDs($data['img_id']);
                $this->imgarrAttrData[] = array(
                    'imgId'=>$data['img_id'],
                    'data'=>$arr
                );
                return $arr;
            } else {
                return array();
            }
        } else {
            if (isset($data['s_material_id']) && $data['s_material_id'] != 0) {
                $imgarrAttrData = $this->imgarrAttrData;
                foreach ($imgarrAttrData as $k=>$v){
                    if($v['imgId'] == $data['s_material_id']){
                        return $v['data'];
                    }
                }
                $arr = self::$MaterialModel->getFilePathByIDs($data['s_material_id']);
                $this->imgarrAttrData[] = array(
                    'imgId'=>$data['s_material_id'],
                    'data'=>$arr
                );
                return $arr;
            } else {
                return array();
            }
        }
    }

    public function getitemImgIdAttr($value, $data)
    {
        if (isset($data['isset']) && $data['isset'] == 2) {
            if (isset($data['img_id']) && $data['img_id'] != 0) {
                if (strpos($data['img_id'], ',') === false) {
                    return intval($data['img_id']);
                } else {
                    $arr = explode(',', $data['img_id']);
                    return intval($arr[0]);
                }
            } else {
                return 0;
            }
        } else {
            if (isset($data['s_material_id']) && $data['s_material_id'] != 0) {
                if (strpos($data['s_material_id'], ',') === false) {
                    return intval($data['s_material_id']);
                } else {
                    $arr = explode(',', $data['s_material_id']);
                    return intval($arr[0]);
                }
            } else {
                return 0;
            }
        }
    }

    // 商品视频
    public function getvideoAttr($value, $data)
    {
        if (isset($data['video_id']) && $data['video_id'] != 0) {
            $arr = self::$MaterialModel->getFilePathByIDs($data['video_id']);
            return $arr;
        } else {
            return array();
        }
    }

    // 商品价格
    public function getsPriceAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            if (isset($data['store_product_id'])) {
                $price = self::$StoreProductSkuAttrModel->getPriceByGoodsID(array('s.store_product_id' => $data['store_product_id']));
            } else {
                $price = self::$StoreProductSkuAttrModel->getPriceByGoodsID(array('s.store_product_id' => $data['id']));
            }
            return $price;
        } else {
            if (isset($data['isset']) && $data['isset'] == 1) {
                if (isset($data['s_s_price'])) {
                    $data['s_s_price'] = $data['s_s_price'] ? $data['s_s_price'] : 0;
                    return '￥' . number_format(round($data['s_s_price'] / 100, 2), 2,".","");
                } else {
                    if (isset($data['price'])) {
                        $data['price'] = $data['price'] ? $data['price'] : 0;
                        return '￥' . number_format(round($data['price'] / 100, 2), 2,".","");
                    } else {
                        return '数据不全';
                    }
                }
            } else if (isset($data['isset']) && $data['isset'] == 2) {
                if (isset($data['price'])) {
                    $data['price'] = $data['price'] ? $data['price'] : 0;
                    return '￥' . number_format(round($data['price'] / 100, 2), 2,".","");
                } else {
                    return '数据不全';
                }
            } else {
                return '数据不全';
            }
        }
    }


    // 商品价格(合伙人 胡)
    public function getskuPriceAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            if (isset($data['store_product_id'])) {
                $price = self::$StoreProductSkuAttrModel->getPriceByGoodsIDs(array('s.store_product_id' => $data['store_product_id']));
            } else {
                $price = self::$StoreProductSkuAttrModel->getPriceByGoodsIDs(array('s.store_product_id' => $data['id']));
            }
            return $price;
        } else {
            if (isset($data['isset']) && $data['isset'] == 1) {
                if (isset($data['s_s_price'])) {
                    $data['s_s_price'] = $data['s_s_price'] ? $data['s_s_price'] : 0;
                    return number_format(round($data['s_s_price'] / 100, 2), 2,'.','');
                } else {
                    if (isset($data['price'])) {
                        $data['price'] = $data['price'] ? $data['price'] : 0;
                        return number_format(round($data['price'] / 100, 2), 2,'.','');
                    } else {
                        return '0';
                    }
                }
            } else if (isset($data['isset']) && $data['isset'] == 2) {
                if (isset($data['price'])) {
                    $data['price'] = $data['price'] ? $data['price'] : 0;
                    return number_format(round($data['price'] / 100, 2), 2,'.','');
                } else {
                    return '0';
                }
            } else {
                return '0';
            }
        }
    }

    protected $sClassAttrData = array();
    // 商品分类
    public function getsClassAttr($value, $data)
    {
        if (isset($data['product_classid']) && $data['product_classid']) {
            foreach ($this->sClassAttrData as $k=>$v){
                if($v['id'] == $data['product_classid']){
                    return $v['data'];
                }
            }
            $name = self::$ProductLabelClassModel->getNameByID($data['product_classid']);
            $this->sClassAttrData[] = array(
                'id'=>$data['product_classid'],
                'data'=>$name
            );
            return $name;
        }
        return '-';
    }

    protected $sLabelAttrData = array();
    // 商品标签
    public function getsLabelAttr($value, $data)
    {
        if (isset($data['product_labelids']) && $data['product_labelids']) {
            foreach ($this->sClassAttrData as $k=>$v){
                if($v['id'] == $data['product_labelids']){
                    return $v['data'];
                }
            }
            $name = self::$ProductLabelClassModel->getNameByIDs($data['product_labelids']);
            $this->sClassAttrData[] = array(
                'id'=>$data['product_labelids'],
                'data'=>$name
            );
            return $name;
        }
        return '-';
    }

    // 商品总库存
    public function gettotalnumAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            if (isset($data['store_product_id'])) {
                $totalnum = self::$StoreProductSkuAttrModel->sumField(array('store_product_id' => $data['store_product_id']), 'totalnum');
            } else {
                $totalnum = self::$StoreProductSkuAttrModel->sumField(array('store_product_id' => $data['id']), 'totalnum');
            }
            return $totalnum;
        } else {
            if ($data['totalnum'] <= 0 && $data['status'] == 1) {
                return strval($data['totalnum']);
            } else {
                return strval($data['totalnum']);
            }
        }
    }

    // 商品总销量
    public function gettotalsellAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            if (isset($data['store_product_id'])) {
                $totalsell = self::$StoreProductSkuAttrModel->sumField(array('store_product_id' => $data['store_product_id']), 'totalsell');
            } else {
                $totalsell = self::$StoreProductSkuAttrModel->sumField(array('store_product_id' => $data['id']), 'totalsell');
            }
            return $totalsell;
        } else {
            if ($data['totalsell'] <= 0 && $data['status'] == 1) {
                return strval($data['totalsell']);
            } else {
                return strval($data['totalsell']);
            }
        }
    }

    // 商品总销量
    public function getstatusAttr($value, $data)
    {
        if (isset($data['store_status'])) {
            if ($data['status'] == 2) {
                return '未上架';
            } else {
                $arr = array(1 => '上架中', 2 => '未上架');
                return isset($arr[$data['store_status']]) ? $arr[$data['store_status']] : '状态异常';
            }
        } else {
            $arr = array(1 => '上架中', 2 => '未上架');
            return isset($arr[$data['status']]) ? $arr[$data['status']] : '状态异常';
        }
    }

    // 商品编辑规格信息
    public function geteditskuinfoAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            $append = array(
                'skuarr',
                'skuval',
                'skutitlearr',
                'allproductnum',
                'sellproductnum',
                's_cost_price',
                's_price'
            );
            $map = array(
                's.product_id' => $data['id'],
                's.store_id' => $data['storeId']
            );
            self::$StoreProductSkuAttrModel = new StoreProductSkuAttrModel;
            $data = self::$StoreProductSkuAttrModel->getSkuData($map, $append);
            return $data;
        } else {
            return array();
        }
    }

    // 商品视频 详细信息
    public function getvideoarr2Attr($value, $data)
    {
        if (isset($data['video_id']) && $data['video_id'] != 0) {
            $arr = self::$MaterialModel->getPageList(array('id' => array('in', $data['video_id'])), 1, 10, 'id,urltype,url,cover as poster');
            return $arr;
        } else {
            return array();
        }
    }

    // 商品图片 详细信息
    public function getimgarr2Attr($value, $data)
    {
        if (isset($data['isset']) && $data['isset'] == 2) {
            if (isset($data['img_id']) && $data['img_id'] != 0) {
                $arr = self::$MaterialModel->getPageList(array('id' => array('in', $data['img_id'])), 1, 10, 'id,urltype,url');
                return $arr;
            } else {
                return array();
            }
        } else {
            if (isset($data['s_material_id']) && $data['s_material_id'] != 0) {
                $arr = self::$MaterialModel->getPageList(array('id' => array('in', $data['s_material_id'])), 1, 10, 'id,urltype,url');
                return $arr;
            } else {
                return array();
            }
        }
    }

    // 商品展示运费
    public function getsFreightAttr($value, $data)
    {
        if (isset($data['freight']) && $data['freight']) {
            return '￥ ' . number_format(round($data['freight'] / 100, 2), 2,".","");
        } else {
            return '￥ 0.00';
        }
    }

    // 商品展示成本价
    public function getsCostPriceAttr($value, $data)
    {
        if (isset($data['cost_price']) && $data['cost_price']) {
            return '￥ ' . number_format(round($data['cost_price'] / 100, 2), 2,".","");
        } else {
            return '￥ 0.00';
        }
    }

    /*
     * 处理产品名称
     * */
    public function getProductNameAttr($value, $data)
    {
        if (isset($data['isset']) && $data['isset'] == 1) {
            if (isset($data['store_product_name'])) {
                return $data['store_product_name'];
            }
        }
        if ($value) {
            return $value;
        } else {
            return $data['product_name'];
        }
    }

    /**
     * 获取规格数据
     * @param $value
     * @param $data
     * @return array
     */
    public function getskuinfoAttr($value, $data)
    {
        if (isset($data['issku']) && $data['issku'] == 1) {
            $append = array(
                'hide' => array(
                    'product_id',
                    'merchant_id',
                    'addtime',
                    'isset',
                    's_s_price',
                    's_s_cost_price'
                )
            );
            $map = array(
                's.product_id' => $data['id'],
                's.store_id' => $data['storeId']
            );
            $data = self::$StoreProductSkuAttrModel->getSkuInfo($map, $append);
            if ($data && count($data) > 0) {
                $title = self::$ProductSkuModel->getSkuPNameByIds($data[0]['sku_val_id']);
                $skuattr = array();
                foreach ($title as $k => $v) {
                    $skuattr[] = array(
                        'title' => $v,
                        'item' => []
                    );
                }
                $skuinfo = array();
                $imgurl = array();
                foreach ($data as $key => $val) {
                    $skuarr = explode(',', $val['sku_val_id']);
                    $skuval = explode(',', $val['sku']);
                    foreach ($skuattr as $k2 => $v2) {
                        $item = array('id' => $skuarr[$k2], 'text' => $skuval[$k2], 'status' => 0);
                        if (!in_array($item, $v2['item'])) {
                            $skuattr[$k2]['item'][] = $item;
                        }
                    }
                    $skuinfo[] = $val;
                    $img = strval($skuarr[0]);
                    if (!isset($imgurl[$img])) {
                        $imgurl[$img] = self::$MaterialModel->getFilePathByID($val['img']);
                    }
                }
                return array(
                    'skuattr' => $skuattr,
                    'skulist' => $skuinfo,
                    'imgurl' => $imgurl
                );
            }
            return array();
        } else {
            return array();
        }
    }

    public function getrealPriceAttr($value, $data)
    {
        if (isset($data['s_s_price']) && $data['s_s_price'] && isset($data['isset']) && $data['isset'] == 1) {
            return number_format($data['s_s_price'] / 100, 2, '.', '');
        } else {
            return number_format($data['price'] / 100, 2, '.', '');
        }
    }

    // 产品会员等级折扣
    public function getvipDiscountAttr($value, $data)
    {
        if (self::$uid && isset($data['store_product_id'])) {
            $level = (new MemberModel)->getFinds(array('id' => self::$uid),'level_id,is_level,growth_value,merchantid');
            if (!$level) {
                return 10;
            } else {
                if ($level['is_level'] == 1) {
                    $level_id = $level['level_id'];
                    $goodsdiscount = (new MemberlevelModel)->getFind(array('id' => $level_id),'id,goods_discount,goods_open');
                } else {
                    $map = array('merchant_id' => $level['merchantid'], 'growth_value' => array('ELT', $level['growth_value']));
                    $goodsdiscount = (new MemberlevelModel)->getFind($map,'id,goods_discount,goods_open','growth_value desc');
                }
                if ($goodsdiscount) {
                   if ($goodsdiscount['goods_open'] == 1) {
                        return $goodsdiscount['goods_discount'];
                    }
                }
                return 10;
            }
        }
        return 10;
    }
    // 产品会员信息
    public function getMemberInfoAttr($value, $data)
    {
        if (self::$uid && isset($data['store_product_id'])) {
            $level = (new MemberModel)->getFinds(array('id' => self::$uid),'level_id,is_level,growth_value,merchantid');
            if (!$level) {

            } else {
                if ($level['is_level'] == 1) {
                    $level_id = $level['level_id'];
                    $goodsdiscount = (new MemberlevelModel)->getFind(array('id' => $level_id),'id,goods_discount,goods_open');
                } else {
                    $map = array('merchant_id' => $level['merchantid'], 'growth_value' => array('ELT', $level['growth_value']));
                    $goodsdiscount = (new MemberlevelModel)->getFind($map,'id,goods_discount,goods_open','growth_value desc');
                }
                $level['level_id'] = $goodsdiscount['id'];
                $level['goods_discount'] = $goodsdiscount['goods_discount'];
                $level['goods_open'] = $goodsdiscount['goods_open'];
                return $level;
            }
        }
        return array(
            'level_id'=>0,
            'is_level'=>0,
            'growth_value'=>0,
            'merchantid'=>0,
            'goods_discount'=>10,
            'goods_open'=>2,
        );
    }


    // 申请仓库数据   $data['product_id'] $extraData['storeid']  $extraData['merchantid']
    public function getWarehouseGoodsInfoAttr($value, $data)
    {
        $extraData = self::$extraData;
        if (!$extraData || !isset($extraData['storeid']) || !isset($extraData['merchantid']) || !isset($data['headquarterStatus'])) {
            return array(
                'canApplyNum' => 0,
                'canApply' => '2',
                'refuseReason' => '系统错误',
                'canReturn' => 0,                   // 0,不能退货
                'refuseReturn' => '仓库无此商品'
            );
        }
        $storeid = $extraData['storeid'];
        $merchantid = $extraData['merchantid'];
        $map = array(
            'merchant_id' => $merchantid,
            'store_id' => $storeid,
            'product_id' => $data['product_id']
        );
        $field = array(
            'totalnum as canApplyNum',
            'status as canApply'
        );
        $newData = $this->where($map)->field($field)->find();
        if ($newData) {
            if (($data['headquarterStatus'] != 1 && $data['headquarterStatus'] != 6)) {
                if ($newData['canApply'] == 2) {
                    $newData['refuseReason'] = '总部下架且已停止申请';
                } else {
                    $newData['refuseReason'] = '仓库已停止申请';
                }
                $newData['canApply'] = 2; // 总部下架
            } else {
                if ($newData['canApply'] == 1) {
                    $newData['refuseReason'] = '允许申请';
                } else {
                    $newData['refuseReason'] = '仓库已停止申请';
                }
            }
            $newData['canReturn'] = $newData['canApply'];
        } else {
            $newData = array(
                'canApplyNum' => 0,
                'canApply' => '2',
                'refuseReason' => '仓库无此商品',
                'canReturn' => 0,                   // 0,不能退货
                'refuseReturn' => '仓库无此商品'
            );
        }
        return $newData;
    }
    public function getformAttr($v, $data){
        $ProductModel = new ProductModel;
        $map = array(
            'id'=>$data['product_id'],
        );
        $fields = array(
            'storeid',
        );
        $ProductData = $ProductModel->getFind($map,$fields);
        return $ProductData['storeid'];

    }
    public function getFreightInfoAttr($v, $data)
    {
        if (!isset($data['freight_id']) || $data['freight_id'] == 0) {
            return array(
                'id' => 0,
                'name' => '',
                'first_num'=>0,
                'first_fee'=>'0.00',
                'add_num'=>0,
                'add_fee'=>'0.00',
            );
        } else {
            if(is_object($data)){
                $res = $data->freightInfos;
            }else{
                $res = $this->freightInfos;
            }
            $res = $res ? $res : array(
                'id' => 0,
                'name' => '',
                'first_num'=>0,
                'first_fee'=>'0.00',
                'add_num'=>0,
                'add_fee'=>'0.00',
            );
            return $res;
        }
    }

    public function freightInfos()
    {
        return $this->hasOne('app\store\model\Productfreight', 'id', 'freight_id')->field('*');
    }


    public function updateProduct(array $where, array $update)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 添加商品
            $this->where($where)->update($update);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            Db::rollback();
        }
        return false;
    }
    /*
	 * 获取当前服务的会员价信息 要求
	 * $value 是当前服务id
	 * $data 数据中包含 商户id 信息
	 * android\controller\Service 方法有使用
	 * */
    public function getMemberPriceDataAttr($value,$data){
        try {
            $merchantId = self::$extraData['merchantid'];
            $goodsId = $data['product_id'];
            $map = array(
                'merchantid' => $merchantId,
                'storeid' => 0,
                'type' => 2,
                'goods_id' => $goodsId
            );
            $fields = array(
                'id', 'goods_id', 'price', 'sku_id','member_level_id'
            );
            $dataMap = array('map' => $map, 'field' => $fields);
            $MemberPriceModel = new MemberPriceModel;
            $memberPriceData = $MemberPriceModel->getAllData($dataMap);
        } catch (\Exception $e) {
            $memberPriceData = [];
        }
        return $memberPriceData;
    }
}