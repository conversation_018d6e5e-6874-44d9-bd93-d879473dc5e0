<div class="layui-fluid">
  <div
    id="expendituretable"
    v-cloak
    v-loading="ajaxloading"
    style="height: 100%"
  >
    <el-card class="box-card">
      <div class="flex items-center space-x-4">
        <template v-if="isFactory">
          <div>统计门店</div>
          <el-select
            v-model="storeid"
            filterable
            placeholder="请选择统计门店"
            @change="handleStoreChange"
            size="small"
          >
            <el-option :key="0" label="请选择门店" :value="0"> </el-option>
            <el-option
              v-for="item in storeListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <div class="flex items-center space-x-2">
          <div>统计日期</div>
          <el-date-picker
            v-model="dates"
            type="daterange"
            size="small"
            range-separator="至"
            clearable
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
          <el-button type="primary" size="small" @click="exportData">
            导出
          </el-button>
        </div>
      </div>

      <!-- <div class="space-y-2 mt-2" v-if="isFactory">
        <div class="flex space-x-2">
          <span>统计门店</span>
          <span class="font-bold">{{selectedStore?.storetag}}</span>
        </div>
        <div class="flex gap-2 flex-wrap items-baseline">
          <div
            v-for="item in storeList"
            :key="'storeList-' + item.id"
            class="border border-solid rounded-md py-1 px-2 cursor-pointer"
            :class="selectedStore?.id === item.id ? 'bg-primary text-white border-primary' : 'border-gray-300 bg-white'"
            @click="handleStoreClick(item)"
          >
            {{item.storetag}}
          </div>
        </div>
      </div> -->
      <el-button
        type="primary"
        size="small"
        @click="handleCreate"
        v-if="!isFactory"
        >新增记录
      </el-button>
      <div>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="date" label="日期"></el-table-column>
          <el-table-column prop="project" label="事由"></el-table-column>
          <el-table-column prop="handler" label="经手人"></el-table-column>
          <el-table-column prop="money" label="金额"></el-table-column>
          <el-table-column prop="remark" label="备注"> </el-table-column>
          <el-table-column prop="id" label="操作" v-if="!isFactory">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="handleModify(scope.row.id)"
              >
                修改
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="delLabelClass(scope.row.id)"
                >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div
          class="block"
          style="margin-top: 20px; overflow: hidden"
          v-if="total>0"
        >
          <el-pagination
            style="float: right"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 30, 40,100]"
            :page-size="limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </el-card>
    <el-dialog
      width="400px"
      :title="title"
      :visible.sync="isShowEditDialog"
      append-to-body
    >
      <el-form label-width="80px" size="small" v-loading="dialogLoading">
        <el-form-item label="日期">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="事由">
          <el-input
            maxlength="99"
            placeholder="请输入事由"
            v-model="form.project"
          ></el-input>
        </el-form-item>
        <el-form-item label="经手人">
          <el-select v-model="form.handler" placeholder="请选择经手人">
            <el-option
              v-for="item in staffList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="金额">
          <el-input
            type="number"
            placeholder="请输入金额"
            v-model="form.money"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            maxlength="300"
            placeholder="请输入备注"
            v-model="form.remark"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</div>
<script>
  layui.use("index", function () {
    var admin = layui.admin;
    var view = layui.view;
    var $ = layui.$;
    new Vue({
      el: "#expendituretable",
      data() {
        return {
          dates: [],
          title: "",
          form: {
            date: "",
            project: "",
            handler: "",
            money: "",
            remark: "",
            id: "",
          },
          page: 1,
          limit: 10,
          total: 0,
          tableData: [],
          ajaxloading: false,
          isShowEditDialog: false,
          staffList: [],
          dialogLoading: false,
          isEdit: false,
          isFactory: false,
          storeid: 0,
          storeList: [],
          selectedStore: {},
        };
      },
      computed: {
        storeListOptions() {
          return this.storeList.map((item) => ({
            value: item.id,
            label: item.storetag,
          }));
        },
      },
      methods: {
        formatEleMentPickDate(data) {
          return ff_util.formatEleMentPickDate(data);
        },
        handleCreate() {
          this.form = {
            date: "",
            project: "",
            handler: "",
            money: "",
            remark: "",
            id: "",
          };
          if (this.staffList.length === 0) {
            this.getAllStaff();
          }
          this.isEdit = false;
          this.title = "新增支出记录";
          this.isShowEditDialog = true;
        },
        handleModify(id) {
          this.isEdit = true;
          this.form = {
            date: "",
            project: "",
            handler: "",
            money: "",
            remark: "",
            id: "",
          };
          this.getDetail(id);
          this.title = "修改支出记录";
          this.isShowEditDialog = true;
        },
        handleCancel() {
          this.isShowEditDialog = false;
          this.form = {
            date: "",
            project: "",
            handler: "",
            money: "",
            remark: "",
            id: "",
          };
        },
        handleConfirm() {
          var vm = this;
          if (!vm.form.date) {
            vm.$message.error("请选择日期");
            return;
          }
          if (!vm.form.project) {
            vm.$message.error("请输入事由");
            return;
          }
          if (!vm.form.handler) {
            vm.$message.error("请输入经手人");
            return;
          }
          if (!vm.form.money || isNaN(Number(vm.form.money))) {
            vm.$message.error("请输入有效的金额");
            return;
          }
          let url = "";
          if (vm.isEdit) {
            url = "{:url('Financialstatements/editExpenditureTable')}";
          } else {
            url = "{:url('Financialstatements/addExpenditureTable')}";
          }
          $.ajax({
            url: url,
            data: vm.form,
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.$message.success(res.msg);
                vm.handleCancel();
                vm.getProcureList();
              } else {
                vm.$message.error(res.msg);
              }
            },
          });
        },
        getDetail(id) {
          var vm = this;
          vm.dialogLoading = true;
          if (this.staffList.length === 0) {
            this.getAllStaff();
          }

          $.ajax({
            url: "{:url('Financialstatements/getExpenditureTableDetail')}",
            data: {id: id},
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.form = res.data;
              } else {
                vm.$message.error(res.msg);
              }
            },
            complete: () => {
              vm.dialogLoading = false;
            },
          });
        },
        getAllStaff() {
          var vm = this;
          $.ajax({
            url: "{:url('/store/commission/getallstaff')}",
            type: "POST",
            success: function (res) {
              vm.staffList = res.data;
            },
          });
        },

        getProcureList() {
          var vm = this;
          vm.ajaxloading = true;
          const params = {
            storeid: vm.storeid,
            page: vm.page,
            limit: vm.limit,
          };
          // 处理日期范围
          if (vm.dates && vm.dates.length === 2) {
            params.start_date = vm.formatEleMentPickDate(vm.dates[0]);
            params.end_date = vm.formatEleMentPickDate(vm.dates[1]);
          }
          $.ajax({
            url: "{:url('Financialstatements/getExpenditureTableList')}",
            data: params,
            type: "POST",
            success: function (res) {
              vm.ajaxloading = false;
              if (res.code == 1) {
                vm.tableData = res.data.data;
                vm.total = res.data.total;
                vm.page = res.data.current_page;
              }
            },
          });
        },
        handleSizeChange(val) {
          this.page = 1;
          this.limit = val;
          this.getProcureList();
        },
        handleCurrentChange(val) {
          this.page = val;
          this.getProcureList();
        },
        delLabelClass(id) {
          var vm = this;
          vm.$confirm("确认要删除这条记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('Financialstatements/deleteExpenditureTable')}",
                data: {id: id},
                type: "POST",
                success: function (res) {
                  if (res.code == 1) {
                    vm.$message.success(res.msg);
                    vm.getProcureList();
                  } else {
                    vm.$message.error(res.msg);
                  }
                },
              });
            })
            .catch(() => {
              vm.$message.info("已取消删除");
            });
        },
        getParam(data) {
          let url = "";
          for (var k in data) {
            let value = data[k] !== undefined ? data[k] : "";
            url += `&${k}=${encodeURIComponent(value)}`;
          }
          return url ? url.substring(1) : "";
        },
        /**
         * 将url和参数拼接成完整地址
         * @param {string} url url地址
         * @param {Json} data json对象
         * @returns {string}
         */
        getUrl(url, data) {
          //看原始url地址中开头是否带?，然后拼接处理好的参数
          return (url +=
            (url.indexOf("?") < 0 ? "?" : "&") + this.getParam(data));
        },
        exportData() {
          var vm = this;
          // 检查是否选择了日期范围
          if (!this.dates || this.dates.length !== 2) {
            this.$message.warning("请先选择日期范围");
            return;
          }

          // 格式化日期参数
          var startDate = this.formatEleMentPickDate(this.dates[0]);
          var endDate = this.formatEleMentPickDate(this.dates[1]);

          // 构建下载URL
          const preUrl = "{:url('Financialstatements/exportExpenseReport')}";
          const data = {
            start: startDate,
            end: endDate,
            storeid: this.storeid,
          };
          const url = this.getUrl(preUrl, data);

          // 显示提示消息
          this.$message.warning(
            "数据较多时，将花费较多时间，请耐心等待，建议按时间段筛选条件分多次导出"
          );

          // 使用location.href触发文件下载
          location.href = url;
        },
        handleStoreClick(item) {
          if (this.selectedStore?.id === item.id) {
            this.selectedStore = {};
            this.storeid = 0;
          } else {
            this.selectedStore = item;
            this.storeid = item.id;
          }
          this.tableData = [];
          this.page = 1;
          this.getProcureList();
        },
        handleStoreChange(value) {
          this.storeid = value;
          this.tableData = [];
          this.page = 1;
          this.getProcureList();
        },
        getStoreList() {
          var vm = this;
          $.ajax({
            url: "{:url('/store/tab_store/getstorelist')}",
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.storeList = res.data;
              }
            },
          });
        },
      },
      watch: {
        dates() {
          this.page = 1;
          this.tableData = [];
          this.getProcureList();
        },
      },
      created: function () {
        this.storeid = layui.S_USER_TOKEN.storeid;
        this.isFactory = !this.storeid;
        if (this.isFactory) {
          this.getStoreList();
        } else {
          this.getProcureList();
        }
      },
    });
  });
</script>
<style></style>
