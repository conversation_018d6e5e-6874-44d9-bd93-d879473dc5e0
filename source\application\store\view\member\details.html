<!--[newmeiye]-->
<div class="layui-fluid">
  <div id="memberdetails" v-cloak>
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>会员详情</span>
      </div>
      <!--基本信息-->
      <div class="w-full flex space-x-6">
        <div class="flex flex-col items-center">
          <el-tooltip content="点击更换头像" effect="dark" placement="top">
            <el-image
              :src="pic"
              @click.native="uploadPic"
              alt=""
              class="pic cursor-pointer"
            ></el-image>
          </el-tooltip>
          <el-button size="mini" @click="doUploadPic" v-if="changePic"
            >使用头像</el-button
          >
        </div>
        <div class="flex-1">
          <div class="flex gap-3 items-center mb-2">
            <div class="text-xl font-bold">{{lst?.member_name}}</div>
            <div class="mt-1">{{lst?.remarks_name}}</div>
            <el-tag
              size="small"
              v-if="[1,2].includes(lst?.sex)"
              :type="
                lst?.sex == 1
                  ? 'primary'
                  : lst?.sex == 2
                  ? 'danger'
                  : 'info'
              "
            >
              {{lst?.sex == 1 ? '男' : lst?.sex == 2 ? '女' : '未知'}}
            </el-tag>
            <el-button @click="editMember(id)" type="text" size="small"
              >编辑</el-button
            >
            <div style="margin-left: auto">
              <el-popover
                @after-enter="changePass"
                placement="right"
                width="320"
                trigger="click"
              >
                <div>
                  <p style="text-align: center; margin-top: 16px">
                    扫一扫，在手机上修改密码
                  </p>
                  <div style="text-align: center">
                    <div
                      style="width: 240px; height: 240px; margin: 10px auto"
                      ref="changePass"
                    ></div>
                  </div>
                </div>
                <el-button
                  size="small"
                  slot="reference"
                  style="margin: 0px 10px"
                  >修改密码</el-button
                >
              </el-popover>
            </div>
          </div>
          <div class="flex space-x-12">
            <div>
              <div class="f_vip_detail_title">
                <div>是否会员：</div>
                <el-tag
                  size="small"
                  :type="lst?.is_vip == 1 ? 'warning' : 'info'"
                >
                  {{lst?.is_vip == 1 ? '会员' : '非会员'}}
                </el-tag>
              </div>
              <div class="f_vip_detail_title">
                <div>会员职称：</div>
                <div>{{lst?.jobName}}</div>
              </div>
            </div>
            <div>
              <div class="f_vip_detail_title">
                <div>会员编号：</div>
                <div>{{lst?.member_number}}</div>
              </div>
              <div class="f_vip_detail_title">
                <div>账号/手机：</div>
                <div>{{lst?.phone}}</div>
              </div>
            </div>
            <div>
              <div class="f_vip_detail_title">
                <div>创建日期：</div>
                <div>{{lst?.addtime}}</div>
              </div>
              <div class="f_vip_detail_title">
                <div>新客来源：</div>
                <div>{{lst?.sourceName}}</div>
              </div>
            </div>
            <div class="flex-1">
              <div class="f_vip_detail_title">
                <div>归属门店：</div>
                <div>{{lst?.store_id}}</div>
              </div>
              <div class="f_vip_detail_title">
                <div>新客归属：</div>
                <div>{{lst?.adviser}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="my-4 px-6 py-2 bg-gray-50 rounded flex items-center">
        <div class="vip_model1_info5_font10">
          <div class="vip_model1_info5_font11">累计消费总额</div>
          <div class="vip_model1_info5_font12">{{lst?.total}}</div>
        </div>
        <div class="vip_model1_info5_font10">
          <div class="vip_model1_info5_font11">消费次数</div>
          <div class="vip_model1_info5_font12">{{lst?.count}}</div>
        </div>
        <div
          class="vip_model1_info5_font10 cursor-pointer"
          @click="cardDetails(lst.id)"
        >
          <div class="vip_model1_info5_font11">卡项</div>
          <div class="vip_model1_info5_font12 text-primary">
            {{lst?.card_num}}
          </div>
        </div>
        <div
          class="vip_model1_info5_font10 cursor-pointer"
          @click="equityDetails(lst.id)"
        >
          <div class="vip_model1_info5_font11">权益</div>
          <div class="vip_model1_info5_font12 text-primary">
            {{lst?.equity_num}}
          </div>
        </div>
      </div>

      <!--消费-->
      <!-- <el-row :gutter="20" class="xiangqing" style="margin-bottom: 36px">
        <el-col :span="4" style="margin-left: 30px">
          <p style="padding-bottom: 8px">累计消费总额</p>
          <span style="font-size: 20px">{{lst.total}}</span>
        </el-col>
        <el-col :span="4" style="margin-left: 30px">
          <p style="padding-bottom: 8px">有效交易次数</p>
          <span style="font-size: 20px">{{lst.count}}</span>
        </el-col>
        <el-col :span="4" style="margin-left: 30px">
          <p style="padding-bottom: 8px">欠款</p>
          <span
            style="font-size: 20px; color: #e74c75; cursor: pointer"
            @click="arrearsDetails(lst.id)"
            >{{lst.debt}}</span
          >
        </el-col>
      </el-row> -->

      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="会员信息" name="1">
          <!-- <el-row :gutter="20">
            <el-col :span="24">
              <div style="padding: 16px">
                <div class="clearfix tabtop">
                  <span>标签</span>
                  <el-button
                    style="float: right; padding: 3px 0"
                    type="text"
                    @click="Tab()"
                    >编辑标签</el-button
                  >
                </div>
                <div style="padding: 10px" v-if="tab_id==''">
                  <el-button type="text" @click="Tab()">添加标签</el-button>
                </div>
                <div style="padding: 10px" v-else>
                  <el-tag
                    v-for="(tag,index) in tab_id"
                    :key="index"
                    style="margin: 4px; cursor: pointer; min-width: 220px"
                  >
                    {{tag.tag_name}}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row> -->
          <el-row :gutter="20">
            <el-col :span="12">
              <div style="padding: 16px">
                <div class="clearfix tabtop">
                  <span>基本信息</span>
                  <el-button
                    style="float: right; padding: 3px 0"
                    type="text"
                    @click="Edit(id)"
                    >编辑</el-button
                  >
                </div>
                <div style="padding: 10px">
                  <div style="padding: 6px">
                    <span class="basic">联系电话：</span>
                    <span>{{lst?.phone}}</span>
                  </div>
                  <div style="padding: 6px">
                    <span class="basic">微信号：</span>
                    <span>{{lst?.wechat_number }}</span>
                  </div>
                  <div style="padding: 6px">
                    <span class="basic">生日：</span>
                    <span>{{lst?.birthday}}</span>
                  </div>
                  <div style="padding: 6px">
                    <span class="basic">详细地址：</span>
                    <span>{{lst?.address}}</span>
                  </div>
                  <div style="padding: 6px">
                    <span class="basic">备注：</span>
                    <span>{{lst?.remarks}}</span>
                  </div>
                </div>
              </div>
            </el-col>

            <!-- <el-col :span="12">
              <div style="padding: 16px">
                <div class="clearfix tabtop">
                  <span>基本档案</span>
                </div>
                <div style="padding: 10px">
                  <div
                    style="padding: 6px"
                    v-for="(item,index) in ArchivesInfo"
                  >
                    <span class="basic">{{ item.field_name }}：</span>
                    <span
                      >{{item.memberInfo.keywords ? item.memberInfo.keywords :
                      '--'}}</span
                    >
                  </div>
                </div>
              </div>
            </el-col> -->
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="交易记录" name="2">
          <template>
            <div class="title">
              <div style="width: 30%">商品</div>
              <div class="Cells">单价</div>
              <div class="Cells">数量</div>
              <div class="Cells">{$storeCraftsmanName}/销售</div>
              <div class="Cells">客户</div>
              <div class="Cells">金额</div>
              <div class="Cells">下单门店</div>
              <div class="Cells">状态</div>
            </div>
            <el-card
              style="margin-bottom: 20px"
              shadow="hover"
              v-if="orderList != ''"
              v-for="(item,index) in orderList"
            >
              <div slot="header" class="clearfix">
                <span>{{item.order_time}}</span>
                <span
                  class="o-tag-indigo text-sm px-2 py-0.5 rounded w-fit ml-2"
                  v-if="item.type == 1 || item.type == 2"
                  >品项</span
                >
                <span
                  class="o-tag-fuchsia text-sm px-2 py-0.5 rounded w-fit ml-2"
                  v-else-if="item.type == 3"
                  >售卡</span
                >
                <span
                  class="o-tag-gray text-sm px-2 py-0.5 rounded w-fit ml-2"
                  v-else-if="item.type == 4"
                  >充值</span
                >
                <span class="float-right"
                  ><span class="text-gray">订单号：</span>
                  {{item.order_number}}</span
                >
              </div>
              <div>
                <div class="order_data">
                  <div style="width: 60%">
                    <div
                      style="display: flex; height: 80px"
                      v-for="(key,indexs) in item.orderInfo"
                    >
                      <div class="details">
                        <div class="details_img">
                          <!--<img :src="key.imgUrl"-->
                          <!--style="width: 100%;">-->
                          <el-image
                            :src="key.imgUrl"
                            style="width: 100%; height: 100%"
                          >
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                        </div>
                        <div class="details_text">
                          <p>{{key.name}}</p>
                          <p class="text-gray">{{key.sku_name}}</p>
                        </div>
                      </div>
                      <div class="" style="width: 16.7%; text-align: center">
                        <label>￥</label>
                        <span>{{key.price}}</span>
                      </div>
                      <div class="" style="width: 16.7%; text-align: center">
                        <label>×</label>
                        <span>{{key.num}}</span>
                      </div>
                      <div
                        class=""
                        style="
                          width: 16.7%;
                          text-align: center;
                          border-right: 1px solid #eee;
                        "
                      >
                        <div>
                          <p>{{key.Craftsman}}</p>
                        </div>
                        <div>
                          <p>{{item.cashier}}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="Cells content"
                    style="border-right: 1px solid #eee"
                  >
                    <div v-if="item.vip != ''">
                      <p>{{item.vip.member_name}}</p>
                      <p>{{item.vip.phone}}</p>
                      <!-- <p>{{item.vip.Grade}}</p> -->
                    </div>
                    <div v-else>暂无信息</div>
                  </div>
                  <div
                    class="Cells content"
                    style="border-right: 1px solid #eee"
                  >
                    <p>
                      <label>￥</label>
                      <span>{{item.receivable}}</span>
                    </p>
                  </div>
                  <div
                    class="Cells content"
                    style="border-right: 1px solid #eee"
                  >
                    {{item.storeTag}}
                  </div>
                  <div class="Cells content">
                    <p>{{item.order_state}}</p>
                    <p>
                      <el-button
                        type="text"
                        size="small"
                        @click="SeeOrder(item.id)"
                      >
                        订单详情
                      </el-button>
                    </p>
                    <p v-if="item.is_refund==1">退款成功</p>
                  </div>
                </div>
              </div>
            </el-card>
            <div class="zan" v-if="orderList == ''">暂无数据</div>
            <el-pagination
              style="margin-top: 10px; float: right"
              background
              @size-change="size_change"
              @current-change="current_change"
              :pager-count="5"
              :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
              :page-size="10"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </template>
        </el-tab-pane>
        <!-- <el-tab-pane label="服务日志" name="3">
          <template>
            <table style="border: 1px #e5e5e5 solid" width="100%">
              <tr style="width: 100%" v-for="item in servicelog">
                <td style="padding: 10px">
                  <div
                    style="
                      height: 68px;
                      width: 100%;
                      border-bottom: 1px rgb(204, 204, 204) dashed;
                      padding-top: 12px;
                    "
                  >
                    <div style="width: 46px; height: 46px; float: left">
                      <el-image
                        :src="item.staffInfo.avatar"
                        alt=""
                        style="border-radius: 50%; width: 46px; height: 46px"
                      >
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </div>
                    <div style="float: left; margin-left: 20px">
                      <p
                        style="font-weight: bold; font-size: 14px; color: #666"
                      >
                        {{item.staffInfo.nickname}}
                        <el-tag size="mini"
                          >{{item.staffInfo.groupInfo.groupName}}</el-tag
                        >
                      </p>
                      <p>{{item.staffInfo.realname}}</p>
                    </div>
                    <div style="float: right; line-height: 46px">
                      {{item.addtime}}
                    </div>
                  </div>
                  <div style="padding: 10px">
                    <p style="padding: 20px 0">{{item.content}}</p>
                    <div style="min-height: 80px" v-if="item.imgArr!=''">
                      <div
                        v-for="(img,index) in item.imgArr"
                        style="float: left; margin: 0 5px"
                      >
                        <el-image
                          :src="img.file_path"
                          alt=""
                          style="width: 80px; height: 80px; margin: 0 5px"
                          :preview-src-list="[img.file_path]"
                        >
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                      </div>
                    </div>
                  </div>
                  <div
                    style="
                      background: #f4f4f4;
                      height: 40px;
                      line-height: 40px;
                      margin: 0 10px;
                      padding-left: 10px;
                      font-weight: bold;
                    "
                  >
                    关联服务：
                  </div>

                  <div style="padding: 10px">
                    <table style="border: 1px #e5e5e5 solid; width: 100%">
                      <tr
                        style="
                          height: 40px;
                          border-bottom: 1px #e5e5e5 solid;
                          background-color: #f8f8f8;
                        "
                      >
                        <td style="padding-left: 5px">商品名称</td>
                        <td>单价</td>
                        <td>数量</td>
                        <td>优惠权益</td>
                        <td>小计</td>
                      </tr>
                      <tr
                        style="height: 74px; border-bottom: 1px #e5e5e5 solid"
                        v-for="(key,indexs) in item.orderInfo"
                      >
                        <td style="padding-left: 5px" width="20%">
                          <div style="height: 40px; width: 40px; float: left">
                            <el-image :src="key.imgUrl" style="width: 100%">
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                          </div>
                          <div style="float: left; margin-left: 20px">
                            <p style="color: #3e63dd">{{key.name}}</p>
                            <p style="color: #999">{{key.sku_name}}</p>
                          </div>
                        </td>
                        <td>
                          <div class="">
                            <span>￥{{key.price}}</span>
                          </div>
                        </td>
                        <td>
                          <div
                            class=""
                            style="width: 16.7%; text-align: center"
                          >
                            <label>×</label>
                            <span>{{key.num}}</span>
                          </div>
                        </td>
                        <td>
                          <div v-if="key.equity_type == 1">无权益</div>
                          <div v-else-if="key.equity_type == 2">
                            <p>-￥{{key.reduceprice}}</p>
                            <p style="color: #a5a5a5; font-size: 12px">
                              折扣优惠
                            </p>
                          </div>
                          <div v-else-if="item.equity_type == 3">
                            <p>￥{{key.reduceprice}}</p>
                            <p style="color: #a5a5a5; font-size: 12px">
                              会员卡抵扣1次
                            </p>
                          </div>
                          <div v-else-if="key.equity_type == 4">
                            <p>￥{{key.reduceprice}}</p>
                            <p style="color: #a5a5a5; font-size: 12px">
                              手动改价
                            </p>
                          </div>
                        </td>
                        <td>
                          <div>
                            <span>￥</span>
                            <span>{{key.Subtotal}}</span>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
              </tr>
            </table>
            <div style="text-align: center" v-if="servicelog == ''">
              暂无数据
            </div>
            <el-pagination
              style="margin-top: 10px; float: right"
              background
              @size-change="log_size_change"
              @current-change="log_current_change"
              :pager-count="5"
              :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
              :page-size="10"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totals"
            >
            </el-pagination>
          </template>
        </el-tab-pane> -->
        <el-tab-pane label="电子病历" name="4">
          <template>
            <el-button type="primary" size="small" @click="createMedicalRecord"
              >新增病历
            </el-button>
            <el-table :data="medicalRecord_tableData" style="width: 100%">
              <el-table-column prop="create_time" label="录入时间">
              </el-table-column>
              <el-table-column prop="medicalRecord_chiefComplaint" label="主诉">
                <template slot-scope="scope">
                  <div class="truncate">{{scope.row.main_complaint}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="medicalRecord_historyOfPresentIllness"
                label="现病史"
              >
                <template slot-scope="scope">
                  <div class="truncate">{{scope.row.present_illness}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="medicalRecord_createPersonName"
                label="填写人"
              >
                <template slot-scope="scope">
                  <div class="truncate">{{scope.row.created_name}}</div>
                </template>
              </el-table-column>
              <el-table-column prop="medicalRecord_remarks" label="备注">
                <template slot-scope="scope">
                  <div class="truncate">{{scope.row.notes}}</div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="操作">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="mini"
                    @click="viewMedicalRecordDetail(scope.row.id)"
                  >
                    查看
                  </el-button>
                  <!-- <el-button
                    type="text"
                    size="mini"
                    @click="handleMedicalRecordModify(scope.row.id)"
                  >
                    修改
                  </el-button> -->
                  <el-button
                    type="text"
                    size="mini"
                    @click="delMedicalRecord(scope.row.id)"
                    >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div
              class="block"
              style="margin-top: 20px; overflow: hidden"
              v-if="total>0"
            >
              <el-pagination
                style="float: right"
                background
                @size-change="medicalRecord_sizeChange"
                @current-change="medicalRecord_currentChange"
                :current-page="medicalRecord_page"
                :page-sizes="[10, 20, 30, 40,100]"
                :page-size="medicalRecord_limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="medicalRecord_total"
              >
              </el-pagination>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="体检信息" name="5">
          <template>
            <div class="grid grid-cols-3 gap-4 p-4">
              <div
                v-for="item in examinationInfo_data"
                :key="item.key"
                class="px-4 py-2 rounded-lg bg-white border border-solid border-gray-300 cursor-pointer hover:shadow-md"
                @click="showExaminationInfoDetail(item)"
              >
                <div class="flex space-x-4 items-baseline">
                  <div class="text-xl font-bold">{{ item.title }}</div>
                  <div v-if="item.danger" class="text-red-500">
                    {{ item.danger }}
                  </div>
                </div>
                <div class="flex items-baseline space-x-1 mt-2">
                  <div class="text-lg font-bold">{{ item.value }}</div>
                  <div class="text-text_color_secondary">{{ item.unit }}</div>
                </div>
                <div class="flex space-x-2 justify-between">
                  <div class="text-text_color_secondary">
                    更新时间：{{ item.date }}
                  </div>
                  <i class="layui-icon layui-icon-right"></i>
                </div>
              </div>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="设备绑定" name="6">
          <template>
            <el-table :data="deviceBinding_tableData" style="width: 100%">
              <el-table-column
                prop="device_id"
                label="设备编号"
              ></el-table-column>
              <el-table-column
                prop="device_name"
                label="设备名称"
              ></el-table-column>
              <el-table-column prop="device_type" label="设备类型">
                <template slot-scope="scope">
                  <div
                    class="o-tag"
                    :class="getDeviceTypeColor(scope.row.device_type)"
                  >
                    {{getDeviceTypeName(scope.row.device_type)}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="bind_time"
                label="绑定时间"
              ></el-table-column>

              <!-- <el-table-column prop="status" label="操作">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="mini"
                    @click="viewMoreBinding(scope.row.id)"
                    >详情</el-button
                  >
                </template>
              </el-table-column> -->
            </el-table>
            <div
              class="block"
              style="margin-top: 20px; overflow: hidden"
              v-if="deviceBinding_total>0"
            >
              <el-pagination
                style="float: right"
                background
                @size-change="deviceBinding_sizeChange"
                @current-change="deviceBinding_currentChange"
                :current-page="deviceBinding_page"
                :page-sizes="[10, 20, 30, 40,100]"
                :page-size="deviceBinding_limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="deviceBinding_total"
              >
              </el-pagination>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        title="修改信息"
        :visible.sync="dialogTablelabel"
        :modal-append-to-body="false"
        width="30%"
      >
        <div class="layui-form-item">
          <div class="layui-inline">
            <el-form ref="form" :rules="rules" :model="form" label-width="80px">
              <el-input type="hidden" v-model="form.id"></el-input>
              <el-form-item label="会员昵称" prop="member_name">
                <el-input
                  maxlength="10"
                  placeholder="请输入会员昵称"
                  v-model="form.member_name"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注名">
                <el-input
                  maxlength="10"
                  placeholder="建议输入客户真实姓名"
                  v-model="form.remarks_name"
                ></el-input>
              </el-form-item>
              <el-form-item label="性别：">
                <el-radio-group v-model="form.sex" size="small">
                  <el-radio :label="1">男</el-radio>
                  <el-radio :label="2">女</el-radio>
                  <el-radio :label="3">未知</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="新客来源" prop="member_source">
                <el-select
                  v-model="form.member_source"
                  placeholder="请选择来源"
                >
                  <el-option
                    v-for="item in source"
                    :key="item.id"
                    :label="item.source_name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="会员等级" prop="growth">
                <el-select v-model="form.growth" placeholder="请选择等级">
                  <el-option
                    v-for="item in member_level"
                    :key="item.id"
                    :label="item.level_name"
                    :value="item.growth_value"
                  >
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="会员职称" prop="jobName">
                <el-select v-model="form.jobName" placeholder="请选择职称">
                  <el-option
                    v-for="item in jobNames"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="归属门店" v-if="storeid == 0">
                <el-select v-model="form.store_id" placeholder="请选择门店">
                  <el-option
                    v-for="item in storelist"
                    :key="item.id"
                    :label="item.storetag"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button size="small" @click="dialogTablelabel = false"
                  >取消</el-button
                >
                <el-button type="primary" size="small" @click="onSubmit('form')"
                  >确定</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        title="修改基本信息"
        :visible.sync="dialogTable"
        :modal-append-to-body="false"
        width="30%"
      >
        <div class="layui-form-item">
          <div class="layui-inline">
            <el-form ref="form" :rules="rules" :model="form" label-width="80px">
              <el-input type="hidden" v-model="form.id"></el-input>
              <!--<el-form-item label="联系电话" prop="phone">-->
              <!--<el-input placeholder="请输入联系电话" v-model="form.phone"></el-input>-->
              <!--</el-form-item>-->
              <el-form-item label="微信号">
                <el-input
                  maxlength="30"
                  placeholder="请输入微信号"
                  v-model="form.wechat_number"
                ></el-input>
              </el-form-item>
              <el-form-item label="生日" prop="birthday">
                <el-date-picker
                  type="date"
                  placeholder="选择日期"
                  v-model="form.birthday"
                  :picker-options="pickerOptions"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="详细地址">
                <el-input
                  maxlength="11"
                  placeholder="请输入详细地址"
                  v-model.number="form.address"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  type="textarea"
                  placeholder="备注不超过200个字"
                  v-model="form.remarks"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" @click="dialogTable = false"
                  >取消</el-button
                >
                <el-button type="primary" size="small" @click="onSub('form')"
                  >确定</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-dialog>
      <el-dialog title="添加标签" :visible.sync="dialogTab" append-to-body>
        <div class="layui-form-item">
          <div class="layui-inline" style="width: 100%">
            <div style="">
              <div>
                <template v-for="item in memberTabData">
                  <el-tag
                    size="medium"
                    type="info"
                    style="margin: 4px; min-width: 220px"
                    >{{ item.tag_name }}</el-tag
                  >
                </template>
              </div>
            </div>
            <p style="height: 36px; line-height: 36px">自定义标签</p>
            <div>
              <el-form :ref="forms" :model="forms" label-width="80px">
                <el-form-item label-width="0">
                  <el-input
                    maxlength="20"
                    show-word-limit
                    v-model="forms.tag_name"
                    size="small"
                    style="width: 150px"
                  ></el-input>
                  <el-button @click.prevent="onSubmittab" size="small"
                    >确定</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
            <p style="height: 36px; line-height: 36px">选择标签</p>
            <div
              style="
                border: 1px rgba(166, 169, 168, 0.3) solid;
                padding: 10px 10px 10px 10px;
                border-radius: 5px;
              "
            >
              <el-row type="flex" style="flex-wrap: wrap">
                <template v-for="(item,index) in tabData">
                  <div style="margin: 4px">
                    <i @click.stop="editTag(item)" class="el-icon-edit"></i>
                    <el-checkbox
                      @change="tabCheck(item)"
                      :checked="TabEffect(item.id)"
                      border
                      style="min-width: 220px; margin: 4px"
                      size="mini"
                    >
                      <span>{{ item.tag_name }}</span>
                    </el-checkbox>
                  </div>
                </template>
              </el-row>
            </div>
            <div style="float: right; margin-top: 20px">
              <el-button type="primary" size="small" @click="dialogTab = false"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="onSubtab(dynamicTags)"
                >确定</el-button
              >
            </div>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        width="900px"
        :visible.sync="isShowMedicalRecordDialog"
        :close-on-click-modal="false"
        append-to-body
        @click.native="handleDialogClick"
      >
        <div slot="title">
          <div class="w-full text-2xl font-bold text-center text-black">
            {{medicalRecord_title}}
          </div>
        </div>
        <div class="relative" ref="medicalRecord_dialog_inner_box">
          <div class="px-4">
            <div class="flex space-x-6">
              <el-image :src="pic" alt="" class="pic"></el-image>
              <div class="flex-1">
                <div class="flex gap-3 items-center mb-2">
                  <div class="text-2xl text-black font-bold">
                    {{lst?.member_name}}
                  </div>
                  <div class="mt-1">{{lst?.remarks_name}}</div>
                </div>
                <div class="flex space-x-12">
                  <div>
                    <div class="f_vip_detail_title">
                      <div>性别：</div>
                      <div>
                        {{lst?.sex == 1 ? '男' : lst?.sex == 2 ? '女' : '未知'}}
                      </div>
                    </div>
                    <div class="f_vip_detail_title">
                      <div>联系电话：</div>
                      <div>{{lst?.phone}}</div>
                    </div>
                  </div>
                  <div>
                    <div class="f_vip_detail_title">
                      <div>出生年月：</div>
                      <div>{{lst?.birthday}}</div>
                    </div>
                    <div class="f_vip_detail_title">
                      <div>年龄：</div>
                      <div>{{getAge(lst?.birthday)}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <table
              class="f-info-table my-4"
              v-if="medicalRecord_title!='新增病历'"
            >
              <tr>
                <td class="f-info-label">创建日期：</td>
                <td>{{medicalRecord_form?.medicalRecord_createTime}}</td>
                <td class="f-info-label">更新日期：</td>
                <td>{{medicalRecord_form?.medicalRecord_updateTime}}</td>
              </tr>
            </table>
          </div>

          <el-form
            ref="medicalRecord_form"
            :model="medicalRecord_form"
            class="o-view-disabled f-medicalRecord-form"
            :validate-on-rule-change="false"
            :show-message="isEditorMedicalRecord"
            :rules="isEditorMedicalRecord ? medicalRecord_rules : {}"
          >
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">主诉</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item class="px-4" prop="medicalRecord_chiefComplaint">
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_chiefComplaint"
                @focus="handleInputFocus('medicalRecord_chiefComplaint')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_chiefComplaint"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_chiefComplaint_height}"
            ></div>
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">现病史</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item
              class="px-4"
              prop="medicalRecord_historyOfPresentIllness"
            >
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_historyOfPresentIllness"
                @focus="handleInputFocus('medicalRecord_historyOfPresentIllness')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_historyOfPresentIllness"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_historyOfPresentIllness_height}"
            ></div>
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">即往史</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item class="px-4" prop="medicalRecord_goingToHistory">
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_goingToHistory"
                @focus="handleInputFocus('medicalRecord_goingToHistory')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_goingToHistory"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_goingToHistory_height}"
            ></div>
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">个人史</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item class="px-4" prop="medicalRecord_personalHistory">
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_personalHistory"
                @focus="handleInputFocus('medicalRecord_personalHistory')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_personalHistory"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_personalHistory_height}"
            ></div>
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">家族史</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item class="px-4" prop="medicalRecord_familyHistory">
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_familyHistory"
                @focus="handleInputFocus('medicalRecord_familyHistory')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_familyHistory"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_familyHistory_height}"
            ></div>
            <div class="flex items-baseline space-x-4 px-4">
              <div class="mb-2 font-bold">备注</div>
              <i
                v-if="!isEditorMedicalRecord"
                class="el-icon-edit text-lg text-primary cursor-pointer hover:text-primary/80"
                @click="handleEditMedicalRecord"
              ></i>
            </div>
            <el-form-item class="px-4" prop="medicalRecord_remarks">
              <el-input
                :disabled="!isEditorMedicalRecord"
                class="o-disable-white-bg"
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2}"
                v-model="medicalRecord_form.medicalRecord_remarks"
                @focus="handleInputFocus('medicalRecord_remarks')"
                @input="refreshPosition"
              ></el-input>
            </el-form-item>
            <div
              ref="medicalRecord_remarks"
              v-if="isEditorMedicalRecord"
              :style="{height: medicalRecord_remarks_height}"
            ></div>
          </el-form>
          <div
            class="absolute left-0 w-full transition-all duration-300 ease-in-out"
            :style="{top: medicalRecord_templateBoxTop}"
            v-show="currentFocusedInput && isEditorMedicalRecord"
          >
            <transition name="medical-record-template">
              <medical-record-template-box
                :tag-list="tagList"
                @template-click="handleTemplateClick"
              ></medical-record-template-box>
            </transition>
          </div>

          <div class="pl-4">
            <div class="mb-4 font-bold">图片</div>
            <el-upload
              action="{:url('/store/Medicalrecord/uploadFile')}"
              list-type="picture-card"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemoveMedicalRecordImg"
              :file-list="fileList"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              :headers="{'X-Requested-With': 'XMLHttpRequest'}"
              :disabled="!isEditorMedicalRecord"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
          </div>
        </div>

        <div v-if="isEditorMedicalRecord" slot="footer" class="dialog-footer">
          <el-button @click="handleMedicalRecordCancel">取 消</el-button>
          <el-button type="primary" @click="handleMedicalRecordConfirm"
            >确 定</el-button
          >
        </div>
      </el-dialog>
      <el-dialog :visible.sync="isShowImgDialog" append-to-body>
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
      <el-dialog
        :title="examinationInfo_title"
        :visible.sync="isShowExaminationInfoDialog"
        append-to-body
      >
        <div v-loading="examinationInfoCharLoading">
          <div ref="chartBox" style="width: 100%; height: 300px"></div>
          <el-table :data="examinationInfo_chartData" style="width: 100%">
            <el-table-column prop="date" label="日期"></el-table-column>
            <el-table-column prop="value" :label="chartUnitLabel">
            </el-table-column>
          </el-table>
          <div
            class="block"
            style="margin-top: 20px; overflow: hidden"
            v-if="examinationInfo_total>0"
          >
            <el-pagination
              style="float: right"
              background
              @size-change="examinationInfo_sizeChange"
              @current-change="examinationInfo_currentChange"
              :current-page="examinationInfo_page"
              :page-sizes="[10, 20, 30, 40,100]"
              :page-size="examinationInfo_limit"
              layout="total, sizes, prev, pager, next, jumper"
              :total="examinationInfo_total"
            >
            </el-pagination>
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</div>
<script src="assets/common/js/Qrcode.js"></script>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/util/enum.js"
></script>
<script src="/assets/src/views/component/medicalRecordTemplateBox/index.js"></script>
<script>
  layui.use(["index"], function () {
    var $ = layui.$;
    // TODO 用户用下面这个layui.echarts有何区别
    //  D:\code\YXG-tsp-php\source\application\store\view\datacenter\index.html
    // var echarts = layui.echarts;
    new Vue({
      el: "#memberdetails",
      data() {
        return {
          id: {$id},
          storeid: {$storeid},
          lst: [],
          servicelog: [],
          pic: "",
          tab_id: [],
          tabData: [],
          dynamicTags: [],
          details: "",
          list: "",
          dialogTablelabel: false,
          dialogTab: false,
          dialogTable: false,
          visible: false,
          visible2: false,
          loading: false, //加载
          isEditorMedicalRecord: false,
          form: {
            id: "",
            member_name: "",
            remarks_name: "",
            sex: "",
            // phone: '',
            birthday: "",
            member_number: "",
            member_source: "",
            growth: "",
            growth_value: "",
            store_id: "",
            wechat_number: "",
            remarks: "",
            address: "",
            score: "",
            jobName: "",
          },
          fileList: [],
          jobNames: JSON.parse(`<?php echo $jobNames; ?>`),
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            },
          },
          rules: {
            member_name: [
              {required: true, message: "请输入会员昵称", trigger: "blur"},
              {
                min: 1,
                max: 30,
                message: "长度在 1 到 30 个字符",
                trigger: "blur",
              },
            ],
            member_source: [
              {required: true, message: "请选择会员来源", trigger: "blur"},
            ],
            growth: [
              {required: true, message: "请选择会员等级", trigger: "blur"},
            ],
            birthday: [
              {required: true, message: "请选择生日", trigger: "blur"},
            ],
          },

          forms: {
            tag_name: "",
          },
          storelist: {$storelist},
          member_level: {$member_level},
          source: {$member_source},
          activeName: "1",
          //表格
          tableData: [],
          promotionloading: true,
          page: 1,
          limit: 10,
          total: 0,
          totals: 0,
          orderList: [],
          memberTabData: [],
          ArchivesInfo: [], // 基本档案信息
          changePic: false,

          //------------------------------------------------------------
          // 病历系统
          isShowMedicalRecordDialog: false,
          isShowImgDialog: false,
          dialogImageUrl: "",
          medicalRecord_title: "",
          medicalRecord_tableData: [],
          medicalRecord_page: 1,
          medicalRecord_limit: 10,
          medicalRecord_total: 0,
          medicalRecord_totals: 0,
          medicalRecord_form: {
            id: "",
            medicalRecord_createTime: "",
            medicalRecord_chiefComplaint: "", // 主诉
            medicalRecord_historyOfPresentIllness: "", // 现病史
            medicalRecord_goingToHistory: "", // 即往史
            medicalRecord_personalHistory: "", // 个人史
            medicalRecord_familyHistory: "", // 家族史
            medicalRecord_createPersonName: "", // 填写人
            medicalRecord_remarks: "", // 备注
            medicalRecord_imgsPath: [],
            medicalRecord_imgsUrl: [],
            medicalRecord_updateTime: "",
            medicalRecord_createTime: "",
          },
          medicalRecord_rules: {
            medicalRecord_chiefComplaint: [
              {required: true, message: "请输入主诉", trigger: "submit"},
            ],
          },
          tagList: [],
          medicalRecord_templateBoxTop: "100px",
          medicalRecord_chiefComplaint_height: "0px",
          medicalRecord_historyOfPresentIllness_height: "0px",
          medicalRecord_goingToHistory_height: "0px",
          medicalRecord_personalHistory_height: "0px",
          medicalRecord_familyHistory_height: "0px",
          medicalRecord_remarks_height: "0px",
          currentFocusedInput: "",
          //------------------------------------------------------------
          // 体检信息
          echartsApp: null,
          examinationInfo_title: "",
          chartUnitLabel: "",
          isShowExaminationInfoDialog: false,
          examinationInfoCharLoading: false,
          examinationInfo_data: [],
          examinationInfo_chartData: [],
          examinationInfo_rules: [],
          examinationInfo_page: 1,
          examinationInfo_limit: 10,
          examinationInfo_total: 0,
          examinationInfo_totals: 0,
          examinationInfo_currentDate: {},
          //------------------------------------------------------------
          // 设备绑定
          deviceBinding_title: "",
          isShowDeviceBindingDialog: false,
          deviceBinding_tableData: [],
          deviceBinding_page: 1,
          deviceBinding_limit: 10,
          deviceBinding_total: 0,
          deviceBinding_totals: 0,
        };
      },
      methods: {
        getDeviceTypeName(device_type) {
          return window.MyEnums.getDeviceTypeName(device_type);
        },
        getDeviceTypeColor(device_type) {
          return window.MyEnums.getDeviceTypeColor(device_type);
        },
        getAge(birthday) {
          if (!birthday) return "";
          const today = new Date();
          const birthDate = new Date(birthday);
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (
            monthDiff < 0 ||
            (monthDiff === 0 && today.getDate() < birthDate.getDate())
          ) {
            age--;
          }
          return age + "岁";
        },

        changePass() {
          this.$nextTick(() => {
            const url =
              window.location.protocol +
              "//" +
              window.location.host +
              "/index.php?s=/wxhome/Wxbind/setMemberPassword";
            const text =
              url +
              "&memberId=" +
              this.id +
              "&merchantId=" +
              this.lst["merchantid"];
            this.$refs["changePass"].innerHTML = "";
            new QRCode(this.$refs["changePass"], {
              text: text,
              width: 240,
              height: 240,
              colorDark: "#000000",
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.H,
            });
          });
        },
        //查看大图
        seeBig() {
          layer.photos({
            photos: "#bigImg",
          });
        },

        onSubmit(form) {
          var that = this;
          that.$refs[form].validate((valid) => {
            if (valid) {
              var id = that.form.id;
              var member_name = that.form.member_name;
              var remarks_name = that.form.remarks_name;
              var member_number = that.form.member_number;
              var member_source = that.form.member_source;
              var jobName = that.form.jobName;
              var sex = that.form.sex;
              var growth = that.form.growth;
              var pattern = /^\d*$/;
              if (!pattern.test(growth)) {
                var growth_value = that.form.growth_value;
              } else {
                var growth_value = that.form.growth;
              }
              var store_id = that.form.store_id;
              if (store_id == "总部") {
                store_id = 0;
              }
              $.ajax({
                url: "{:url('edMember')}",
                data: {
                  id: id,
                  member_name: member_name,
                  remarks_name: remarks_name,
                  member_number: member_number,
                  member_source: member_source,
                  growth_value: growth_value,
                  jobName: jobName,
                  store_id: store_id,
                  sex: sex,
                },
                type: "post",
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    that.getData();
                    that.dialogTablelabel = false;
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            } else {
              return false;
            }
          });
        },
        //增加标签
        onSubmittab() {
          var that = this;
          var tag_name = that.forms.tag_name;
          if (tag_name == "") {
            return that.$message.error("请输入标签名称");
          }
          $.ajax({
            url: "{:url('adTab')}",
            data: {
              tag_name: tag_name,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.forms.tag_name = "";
                that.getTabList();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //添加标签提交
        onSubtab() {
          var vm = this;
          var dynamicTags = vm.memberTabData;
          if (dynamicTags == "") {
            this.$message({
              type: "info",
              message: "请选择标签",
            });
            return;
          }
          var id = vm.id;
          $.ajax({
            url: "{:url('editTab')}",
            data: {
              dynamicTags: dynamicTags,
              id: id,
            },
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
                vm.dialogTab = false;
                vm.getData();
              } else {
                vm.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //打开标签添加弹框
        Tab() {
          var vm = this;
          vm.memberTabData = JSON.parse(JSON.stringify(vm.tab_id), true);
          vm.getTabList();
          vm.dialogTab = true;
        },
        //会员标签
        TabEffect(id) {
          for (var i = 0; i < this.memberTabData.length; i++) {
            if (id == this.memberTabData[i]["id"]) {
              return true;
            }
          }
          return false;
        },
        tabCheck(item) {
          for (var i = 0; i < this.memberTabData.length; i++) {
            if (item.id == this.memberTabData[i]["id"]) {
              this.memberTabData.splice(i, 1);
              return;
            }
          }
          this.memberTabData.push(item);
        },
        //获得全部标签
        getTabList: function () {
          var vm = this;
          $.ajax({
            url: "{:url('getTabList')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 1) {
                vm.tabData = res.data;
              }
            },
          });
        },
        //订单跳转详情页
        SeeOrder(id) {
          window.open("#/Order/seorder/id=" + id);
        },

        //预约到店
        storeService(name, phone) {
          this.visible2 = false;
          location.hash =
            "/booker/index/add/tan/shoper=" + name + "/phone=" + phone;
        },
        //预约上门
        doorService(name, phone) {
          this.visible2 = false;
          location.hash =
            "/Booker/index/create/tan/shoper=" + name + "/phone=" + phone;
        },

        //余额明细
        balanceDetails(id) {
          location.hash = "/Member/bal/balance/" + id;
        },
        //成长值明细
        gropDetails(id) {
          location.hash = "/Member/grop/grop/" + id;
        },
        //会员卡项明细
        cardDetails(id) {
          location.hash = "/Member/card/card/" + id;
        },
        //会员权益明细
        equityDetails(id) {
          location.hash = "/Member/equity/equity/" + id;
        },
        //会员优惠券
        couponDetails(id) {
          location.hash = "/Member/coupon/coupon/" + id;
        },
        //会员积分
        scoreDetails(id) {
          location.hash = "/Member/score/score/" + id;
        },
        //会员欠款详情
        arrearsDetails(id) {
          location.hash = "/Member/arrears/arrears/" + id;
        },
        den(tag) {
          var vm = this;
          var id = tag.id;
          var name = tag.tag_name;

          vm.dynamicTags.push({
            id: id,
            tag_name: name,
          });
        },
        handleClose(tag) {
          this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
        },
        //获取会员等级
        getMemberlevelList: function () {
          var vm = this;
          $.ajax({
            url: "{:url('getMemberlevelList')}",
            data: {},
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.tableData = res.data;
              }
            },
          });
        },
        // tab切换
        handleClick() {
          var vm = this;
          switch (vm.activeName) {
            case "2":
              vm.getOrderList(); //交易记录
              break;
            case "3":
              vm.getServiceLog(); //服务日志
              break;
            case "4":
              vm.getMedicalRecordList(); // 病历
              break;
            case "5":
              vm.getExaminationInfoData(); // 体检信息
              break;
            case "6":
              vm.getDeviceBindingData(); // 设备绑定
              break;
          }
        },

        //修改会员
        editMember(id) {
          var that = this;
          $.ajax({
            url: "{:url('getMemberFind')}",
            data: {
              id: id,
            },
            type: "post",
            success: function (res) {
              that.form = res.data;
            },
          });
          that.dialogTablelabel = true;
        },
        //修改基本信息
        Edit(id) {
          var that = this;
          $.ajax({
            url: "{:url('getMemberFind')}",
            data: {
              id: id,
            },
            type: "post",
            success: function (res) {
              that.form = res.data;
            },
          });
          that.dialogTable = true;
        },
        onSub(form) {
          var that = this;
          that.$refs[form].validate((valid) => {
            if (valid) {
              //国际时间转Y-m-d格式
              var d = new Date(that.form.birthday);
              var birthday =
                d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate();
              var id = that.form.id;
              var wechat_number = that.form.wechat_number;
              var address = that.form.address;
              var remarks = that.form.remarks;

              $.ajax({
                url: "{:url('editMem')}",
                data: {
                  id: id,
                  wechat_number: wechat_number,
                  address: address,
                  birthday: birthday,
                  remarks: remarks,
                },
                type: "post",
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    that.getData();
                    that.dialogTable = false;
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            } else {
              return false;
            }
          });
        },
        getData() {
          var vm = this;
          vm.loading = true;
          var id = vm.id;
          if (id != "") {
            $.ajax({
              url: "{:url('getDetFind')}",
              data: {id: id},
              type: "post",
              success: function (res) {
                vm.lst = res.data;
                vm.pic = res.data?.pic
                  ? res.data?.pic
                  : "assets/img/touxoang.png";
                vm.tab_id = res.data?.tabInfo;
                vm.ArchivesInfo = res.data?.archivesInfo;
                vm.loading = false;
              },
            });
          }
        },
        //服务日志
        getServiceLog() {
          var vm = this;
          vm.loading = true;
          var id = vm.id;
          if (id != "") {
            $.ajax({
              url: "{:url('getServiceLog')}",
              data: {
                id: id,
                page: vm.page,
                limit: vm.limit,
              },
              type: "post",
              success: function (res) {
                if (res.code == 1) {
                  vm.servicelog = res.data;
                  vm.totals = res.count;
                  vm.loading = false;
                } else {
                  vm.servicelog = [];
                  vm.totals = 0;
                }
              },
            });
          }
        },

        //获取订单信息
        getOrderList: function () {
          var that = this;
          that.loading = true;
          var vip_id = that.id;
          $.ajax({
            url: "{:url('getOrderList')}",
            type: "post",
            data: {
              page: that.page,
              limit: that.limit,
              vip_id: vip_id,
            },
            success: function (res) {
              if (res.code == 0) {
                that.orderList = res.data;
                that.total = res.count;
              } else {
                that.orderList = [];
                that.total = 0;
              }
              that.loading = false;
            },
          });
        },

        //增减积分
        editScore(id) {
          var vm = this;
          var score = vm.form.score;
          var pre = /^[\-|0-9][0-9]{1,}$/;
          if (score == "") {
            vm.$message({
              type: "info",
              message: "请输入需要增减的积分",
            });
            return;
          }
          $.ajax({
            url: "{:url('editScore')}",
            data: {
              id: id,
              score: score,
            },
            type: "post",
            success: function (res) {
              if (res.status == 1) {
                vm.$message({
                  type: "success",
                  message: res.msg,
                });
                vm.visible = false;
                vm.form.score = "";
                vm.getData();
              } else {
                vm.$message({
                  type: "info",
                  message: res.msg,
                });
                vm.visible = false;
                vm.form.score = "";
                vm.getData();
              }
            },
          });
        },

        size_change(val) {
          this.limit = val;
          this.page = 1;
          this.getOrderList();
        },
        /* 切换页数 */
        current_change(val) {
          this.page = val;
          this.getOrderList();
        },

        log_size_change(val) {
          this.limit = val;
          this.page = 1;
          this.getServiceLog();
        },
        /* 切换页数 */
        log_current_change(val) {
          this.page = val;
          this.getServiceLog();
        },
        uploadPic() {
          const that = this;
          top.layui.vueCommon.chooseImg(1, function (arr) {
            that.pic = arr[0]["file_path"];
            that.changePic = true;
          });
        },
        doUploadPic() {
          if (!this.changePic) {
            return false;
          }
          const that = this;
          that.loading = true;
          $.ajax({
            url: "{:url('doUploadPic')}",
            type: "post",
            data: {
              pic: that.pic,
              vip_id: that.id,
            },
            success: function (res) {
              if (res.code == 1) {
                that.$message.success("头像使用成功");
                that.changePic = false;
              } else {
                that.$message.error("头像使用失败");
              }
              that.loading = false;
            },
          });
        },
        editTag(item) {
          // console.log(item);
          let that = this;
          this.$prompt("请输入标签", "修改标签", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            inputPlaceholder: item.tag_name,
            inputValidator: (value) => {
              if (value.length == 0) {
                return "标签不能为空";
              } else if (value.length > 20) {
                return "标签不能超过20个字符";
              }
              return true;
            },
          })
            .then(({value}) => {
              $.ajax({
                url: "{:url('editTabitem')}",
                data: {
                  id: item.id,
                  tag_name: value,
                },
                type: "post",
                success: function (res) {
                  if (res.code == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                    });
                    item.tag_name = value;
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {});
        },
        //--------------------------------------------------
        // TODO 病历
        getMedicalRecordList() {
          var vm = this;
          var id = vm.id;
          if (id != "") {
            $.ajax({
              url: "{:url('getMedicalRecordList')}",
              data: {
                id: id,
                page: vm.medicalRecord_page,
                limit: vm.medicalRecord_limit,
              },
              type: "post",
              success: function (res) {
                if (res.code == 1) {
                  // 处理返回的数据，确保attachment字段被正确解析
                  const processedData = res.data.map((item) => {
                    if (item.attachment) {
                      try {
                        // 使用辅助方法解码HTML实体
                        item.attachment = vm.decodeHtmlEntities(
                          item.attachment
                        );
                      } catch (e) {
                        console.error("解析attachment失败:", e);
                      }
                    }
                    return item;
                  });

                  vm.medicalRecord_tableData = processedData;
                  vm.medicalRecord_totals = res.count;
                } else {
                  vm.medicalRecord_tableData = [];
                  vm.medicalRecord_totals = 0;
                }
              },
            });
          }
        },

        // 辅助方法：解码HTML实体
        decodeHtmlEntities(str) {
          if (typeof str !== "string") return str;

          return str
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&lt;/g, "<")
            .replace(/&gt;/g, ">")
            .replace(/&amp;/g, "&");
        },
        getMedicalRecordDetail(id) {
          var vm = this;
          // 从表格数据中查找对应id的记录
          const record = vm.medicalRecord_tableData.find(
            (item) => item.id == id
          );

          if (record) {
            vm.medicalRecord_form.id = record.id;
            vm.medicalRecord_form.medicalRecord_chiefComplaint =
              record.main_complaint;
            vm.medicalRecord_form.medicalRecord_historyOfPresentIllness =
              record.present_illness;
            vm.medicalRecord_form.medicalRecord_goingToHistory =
              record.past_history;
            vm.medicalRecord_form.medicalRecord_personalHistory =
              record.personal_history;
            vm.medicalRecord_form.medicalRecord_familyHistory =
              record.family_history;
            vm.medicalRecord_form.medicalRecord_createPersonName =
              record.created_name;
            vm.medicalRecord_form.medicalRecord_remarks = record.notes;
            vm.medicalRecord_form.medicalRecord_createTime = record.create_time;
            vm.medicalRecord_form.medicalRecord_updateTime = record.update_time;

            // 处理图片数据 - 解析JSON字符串
            let images = [];
            try {
              if (record.attachment) {
                let attachmentStr = record.attachment;

                // 使用辅助方法解码HTML实体
                if (typeof attachmentStr === "string") {
                  attachmentStr = this.decodeHtmlEntities(attachmentStr);
                  images = JSON.parse(attachmentStr);
                } else if (Array.isArray(record.attachment)) {
                  images = record.attachment;
                }
              }
            } catch (e) {
              console.error("解析图片数据失败:", e);
              images = [];
            }

            vm.medicalRecord_form.medicalRecord_imgsUrl = images;

            // 处理fileList用于el-upload展示
            vm.fileList = images.map((url, index) => {
              return {
                name: `图片${index + 1}`,
                url: url,
              };
            });
          } else {
            vm.$message({
              type: "info",
              message: "未找到对应的病历记录",
            });
          }
        },
        createMedicalRecord() {
          if (this.tagList.length == 0) {
            this.getTagList();
          }
          this.currentFocusedInput = "";
          this.resetMedicalRecordBoxHeight();
          this.medicalRecord_title = "新增病历";
          this.isShowMedicalRecordDialog = true;
          this.isEditorMedicalRecord = true;
          // 重置表单数据
          this.clearMedicalRecordForm();

          // 确保图片数组被正确初始化为空数组
          this.medicalRecord_form.medicalRecord_imgsUrl = [];
          this.fileList = [];
        },
        viewMedicalRecordDetail(id) {
          if (this.tagList.length == 0) {
            this.getTagList();
          }
          this.isEditorMedicalRecord = false;
          this.getMedicalRecordDetail(id);
          // TODO 查看disable
          this.medicalRecord_title = "查看病历";
          this.isShowMedicalRecordDialog = true;
        },
        handleMedicalRecordModify(id) {
          if (this.tagList.length == 0) {
            this.getTagList();
          }
          this.getMedicalRecordDetail(id);
          this.medicalRecord_title = "修改病历";
          this.isShowMedicalRecordDialog = true;
          this.isEditorMedicalRecord = true;
        },
        handlePictureCardPreview(file) {
          this.dialogImageUrl = file.url;
          this.isShowImgDialog = true;
        },
        handleRemoveMedicalRecordImg(file, fileList) {
          this.fileList = fileList;
          // 从form.medicalRecord_imgsUrl数组中移除被删除的图片
          if (
            Array.isArray(this.medicalRecord_form.medicalRecord_imgsUrl) &&
            file.url
          ) {
            // 获取要删除的图片URL
            const imageUrl = file.url;
            // 查找图片URL在数组中的位置
            const index =
              this.medicalRecord_form.medicalRecord_imgsUrl.findIndex(
                (url) =>
                  url === imageUrl || url.endsWith(imageUrl.split("/").pop())
              );
            if (index !== -1) {
              this.medicalRecord_form.medicalRecord_imgsUrl.splice(index, 1);
            }
          }
        },
        handleUploadSuccess(response, file, fileList) {
          if (response.code === 1) {
            this.fileList = fileList;
            // 将新上传的图片URL添加到图片数组中
            if (!Array.isArray(this.medicalRecord_form.medicalRecord_imgsUrl)) {
              this.medicalRecord_form.medicalRecord_imgsUrl = [];
            }
            // 根据实际返回的数据结构获取图片URL
            const imageUrl = response.data.file_path;
            if (imageUrl && imageUrl.trim() !== "") {
              this.medicalRecord_form.medicalRecord_imgsUrl.push(imageUrl);
              this.$message.success("图片上传成功");
            } else {
              this.$message.warning("图片URL无效，请重新上传");
            }
          } else {
            this.$message.error(response.msg || "上传失败");
            // 从fileList中移除上传失败的文件
            const index = fileList.indexOf(file);
            if (index !== -1) {
              fileList.splice(index, 1);
            }
          }
        },
        beforeUpload(file) {
          const isImage = file.type.startsWith("image/");
          const isLt2M = file.size / 1024 / 1024 < 2;

          if (!isImage) {
            this.$message.error("只能上传图片文件!");
            return false;
          }
          if (!isLt2M) {
            this.$message.error("图片大小不能超过 2MB!");
            return false;
          }
          return true;
        },
        clearMedicalRecordForm() {
          this.medicalRecord_form = {
            id: "",
            medicalRecord_chiefComplaint: "",
            medicalRecord_historyOfPresentIllness: "",
            medicalRecord_goingToHistory: "",
            medicalRecord_personalHistory: "",
            medicalRecord_familyHistory: "",
            medicalRecord_createPersonName: "",
            medicalRecord_remarks: "",
            medicalRecord_imgsPath: [],
            medicalRecord_imgsUrl: [],
            medicalRecord_createTime: "",
            medicalRecord_updateTime: "",
          };
          // 清除文件列表
          this.fileList = [];
        },
        handleEditMedicalRecord() {
          this.isEditorMedicalRecord = true;
          this.medicalRecord_title = "修改病历";
        },
        handleMedicalRecordCancel() {
          this.isShowMedicalRecordDialog = false;
          // this.isEditorMedicalRecord = false;
        },
        handleMedicalRecordConfirm() {
          var that = this;
          if (!that.medicalRecord_form.medicalRecord_chiefComplaint) {
            that.$message({
              type: "warning",
              message: "请输入主诉",
            });
            return;
          }
          var main_complaint =
            that.medicalRecord_form.medicalRecord_chiefComplaint;
          var present_illness =
            that.medicalRecord_form.medicalRecord_historyOfPresentIllness;
          var past_history =
            that.medicalRecord_form.medicalRecord_goingToHistory;
          var personal_history =
            that.medicalRecord_form.medicalRecord_personalHistory;
          var family_history =
            that.medicalRecord_form.medicalRecord_familyHistory;
          var notes = that.medicalRecord_form.medicalRecord_remarks;
          var id = that.id;
          var record_id = that.medicalRecord_form.id;

          // 确保图片数组不为null且有效
          let imageUrls = that.medicalRecord_form.medicalRecord_imgsUrl || [];
          // 过滤掉null和空值
          imageUrls = imageUrls.filter((url) => url && url.trim() !== "");

          // 构建请求数据
          var data = {
            user_id: id,
            main_complaint: main_complaint,
            present_illness: present_illness,
            past_history: past_history,
            personal_history: personal_history,
            family_history: family_history,
            notes: notes,
            // 图片数据，使用attachment字段并JSON字符串化，确保不传入[null]
            attachment: JSON.stringify(imageUrls.length > 0 ? imageUrls : []),
          };

          // 判断是新增还是修改
          var url = record_id
            ? "/store/Medicalrecord/editrec"
            : "/store/Medicalrecord/addrec";
          if (record_id) {
            data.merchantid = record_id;
          }

          $.ajax({
            url: url,
            data: JSON.stringify(data),
            type: "post",
            headers: {
              "Content-Type": "application/json",
            },
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.getMedicalRecordList();
                that.isShowMedicalRecordDialog = false;
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        medicalRecord_sizeChange(pageLimit) {
          this.medicalRecord_page = 1;
          this.medicalRecord_limit = pageLimit;
          this.getMedicalRecordList();
        },
        medicalRecord_currentChange(pageSize) {
          this.medicalRecord_page = pageSize;
          this.getMedicalRecordList();
        },
        // 删除病历记录
        delMedicalRecord(id) {
          var vm = this;
          this.$confirm("确定要删除该病历记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('/store/Medicalrecord/delrec')}",
                data: {
                  id: id,
                },
                type: "post",
                success: function (res) {
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getMedicalRecordList();
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        getTagList() {
          const vm = this;
          $.ajax({
            url: "{:url('/store/Medicalrecord/taglist')}",
            type: "POST",
            data: {
              page: 1,
              limit: 1000,
            },
            success: function (res) {
              vm.tagList = res.data;
            },
            error: function (res) {
              vm.$message.error(res.msg);
            },
          });
        },
        //--------------------------------------------------
        // TODO 体检
        getExaminationInfoData() {
          var vm = this;
          $.ajax({
            url: "{:url('Member/getExaminationInfoData')}",
            data: {
              uid: vm.id,
            },
            type: "post",
            success: function (res) {
              vm.examinationInfo_data = res.data;
            },
          });
        },
        async getExaminationInfoChartData(exType) {
          var vm = this;
          vm.examinationInfo_chartData = [];
          await $.ajax({
            url: "{:url('Member/getExaminationInfoChartData')}",
            type: "post",
            data: {
              type: exType,
            },
            success: function (res) {
              vm.examinationInfo_chartData = res.data;
              console.log("getExaminationInfoChartData ok");
            },
          });
          vm.chartUnitLabel = `记录值（${vm.examinationInfo_currentDate.unit}）`;
        },
        drawExaminationInfoChart() {
          var vm = this;
          let defaultOption = {
            color: ["#80FFA5", "#00DDFF", "#37A2FF", "#FF0087", "#FFBF00"],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
              },
            ],
            yAxis: [
              {
                type: "value",
              },
            ],
            series: [
              {
                name: "",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "rgb(128, 255, 165)",
                    },
                    {
                      offset: 1,
                      color: "rgb(1, 191, 236)",
                    },
                  ]),
                },
                emphasis: {
                  focus: "series",
                },
                data: [],
                tooltip: {
                  valueFormatter: function (value) {
                    return value + " " + vm.examinationInfo_currentDate.unit;
                  },
                },
              },
            ],
          };

          if (vm.examinationInfo_currentDate.type == "bloodPressure") {
            defaultOption.legend = {
              data: ["收缩压", "舒张压"],
            };
            const arr = vm.examinationInfo_chartData.map((item) =>
              item.value.split("/")
            );
            defaultOption.series[0].name = "收缩压";
            defaultOption.series[0].data = arr.map((item) => item[0]);
            defaultOption.series[0]["tooltip"] = {
              valueFormatter: function (value) {
                return value + " mmHg";
              },
            };
            defaultOption.series.push({
              name: "舒张压",
              type: "line",
              stack: "Total",
              smooth: true,
              lineStyle: {
                width: 0,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgb(0, 221, 255)",
                  },
                  {
                    offset: 1,
                    color: "rgb(77, 119, 255)",
                  },
                ]),
              },
              tooltip: {
                valueFormatter: function (value) {
                  return value + " mmHg";
                },
              },
              emphasis: {
                focus: "series",
              },
              data: arr.map((item) => item[1]),
            });
          } else {
            defaultOption.legend = {
              data: [vm.examinationInfo_currentDate.title],
            };
            defaultOption.series = [
              {
                name: vm.examinationInfo_currentDate.title,
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "rgb(128, 255, 165)",
                    },
                    {
                      offset: 1,
                      color: "rgb(1, 191, 236)",
                    },
                  ]),
                },
                emphasis: {
                  focus: "series",
                },
                data: vm.examinationInfo_chartData.map((item) =>
                  Number(item.value)
                ),
                tooltip: {
                  valueFormatter: function (value) {
                    return value + " " + vm.examinationInfo_currentDate.unit;
                  },
                },
              },
            ];
          }

          defaultOption.xAxis[0].data = vm.examinationInfo_chartData.map(
            (item) => item.date
          );
          vm.echartsApp.setOption(defaultOption, true);
        },
        async showExaminationInfoDetail(data) {
          var vm = this;
          vm.examinationInfo_currentDate = data;
          vm.examinationInfo_title = data.title;
          vm.isShowExaminationInfoDialog = true;
          vm.examinationInfoCharLoading = true;
          try {
            await vm.getExaminationInfoChartData(data.type);
          } catch {}
          vm.examinationInfoCharLoading = false;

          // 使用 $nextTick 确保 DOM 已更新
          vm.$nextTick(() => {
            // 给一个短暂延时确保对话框完全展开
            setTimeout(() => {
              if (vm.$refs.chartBox) {
                console.log("setTimeout ok");
                vm.echartsApp = echarts.init(vm.$refs.chartBox);
                vm.drawExaminationInfoChart();

                // 监听窗口大小变化，重绘图表
                // window.addEventListener("resize", function () {
                //   echartsApp.resize();
                // });

                // setTimeout(() => {
                //   vm.examinationInfo_page = 2;
                //   this.getExaminationInfoChartData();
                //   this.drawExaminationInfoChart();
                // }, 2000);
              }
            }, 200);
          });
        },
        examinationInfo_sizeChange(pageLimit) {
          this.examinationInfo_page = 1;
          this.examinationInfo_limit = pageLimit;
          this.getExaminationInfoChartData();
          this.drawExaminationInfoChart();
        },
        examinationInfo_currentChange(pageSize) {
          this.examinationInfo_page = pageSize;
          this.getExaminationInfoChartData();
          this.drawExaminationInfoChart();
        },
        //------------------------------------------------------------
        // 设备绑定
        getDeviceBindingData() {
          var vm = this;
          $.ajax({
            url: "{:url('member/getDeviceList')}",
            type: "post",
            data: {
              uid: vm.id,
              page: vm.deviceBinding_page,
              limit: vm.deviceBinding_limit,
            },
            success: function (res) {
              vm.deviceBinding_tableData = res.data;
              vm.deviceBinding_total = res.count;
            },
          });
        },
        viewMoreBinding(id) {
          var vm = this;
          vm.isShowDeviceBindingDialog = true;
        },
        deviceBinding_sizeChange(pageLimit) {
          this.deviceBinding_page = 1;
          this.deviceBinding_limit = pageLimit;
          this.getDeviceBindingData();
        },
        deviceBinding_currentChange(pageSize) {
          this.deviceBinding_page = pageSize;
          this.getDeviceBindingData();
        },

        resetMedicalRecordBoxHeight() {
          this.medicalRecord_chiefComplaint_height = "0px";
          this.medicalRecord_historyOfPresentIllness_height = "0px";
          this.medicalRecord_goingToHistory_height = "0px";
          this.medicalRecord_personalHistory_height = "0px";
          this.medicalRecord_familyHistory_height = "0px";
          this.medicalRecord_remarks_height = "0px";
        },

        //刷新位置
        refreshPosition() {
          if (!this.isEditorMedicalRecord) return;
          // 重置所有高度
          this.resetMedicalRecordBoxHeight();

          // 设置当前输入框的高度
          this[this.currentFocusedInput + "_height"] = "430px";

          // 使用 nextTick 确保 DOM 已更新
          this.$nextTick(() => {
            // 使用延迟来实现更好的视觉效果
            setTimeout(() => {
              if (this.$refs[this.currentFocusedInput]) {
                const rect =
                  this.$refs[this.currentFocusedInput].getBoundingClientRect();
                const dialogTop =
                  this.$refs.medicalRecord_dialog_inner_box.getBoundingClientRect()
                    .top;
                // 添加一个小延迟效果，使动画更加明显
                setTimeout(() => {
                  this.medicalRecord_templateBoxTop =
                    rect.top - dialogTop + "px";
                }, 50);
              }
            }, 0);
          });
        },

        handleInputFocus(inputName) {
          if (!this.isEditorMedicalRecord) return;

          // 更新当前焦点输入框
          this.currentFocusedInput = inputName;
          this.refreshPosition();
        },

        handleInputBlur(inputName) {
          if (!this.isEditorMedicalRecord) return;

          // 延迟一下重置高度,以便能看到动画效果
          setTimeout(() => {
            if (this.currentFocusedInput === inputName) {
              this[inputName + "_height"] = "0px";
              this.currentFocusedInput = "";
            }
          }, 200);
        },
        handleDialogClick(e) {
          // 如果点击的是对话框本身(而不是其子元素)
          if (e.target === e.currentTarget) {
            this.currentFocusedInput = "";
            // 重置所有高度
            this.resetMedicalRecordBoxHeight();
          }
        },
        handleTemplateClick(template) {
          this.medicalRecord_form[this.currentFocusedInput] =
            this.medicalRecord_form[this.currentFocusedInput] + template;
          this.refreshPosition();
        },
      },
      watch: {
        isShowMedicalRecordDialog: function (newVal) {
          if (!newVal) {
            this.clearMedicalRecordForm();
            // 重置表单验证规则
            if (this.$refs.medicalRecord_form) {
              this.$refs.medicalRecord_form.clearValidate();
            }
            // 重置编辑状态
            this.isEditorMedicalRecord = false;
          }
        },
      },
      created: function () {
        var vm = this;
        vm.getData();
      },
    });
  });
</script>
<style>
  .lf {
    float: left;
  }
  .basic {
    color: #999999;
  }
  .pic {
    width: 82px;
    height: 82px;
    border-radius: 50%;
    border: 1px #e5e5e5 solid;
  }
  .xiangqing {
    background: #f8f8f8;
    padding: 16px;
  }
  .tabtop {
    height: 30px;
    border-bottom: 1px #c4c8d0 solid;
  }
  .time {
    display: flex;
    align-items: center;
    height: 50px;
  }

  .Dtime {
    display: flex;
    align-items: center;
    margin-right: 20px;
  }

  .search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_block {
    margin-right: 16px;
    margin-bottom: 10px;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    margin-bottom: 20px;
  }

  .order_data {
    display: flex;
    justify-content: space-between;
  }

  .details {
    width: 50%;
    display: flex;
    font-size: 14px;
  }

  .details_img {
    width: 60px;
    margin-right: 10px;
  }

  .details_text {
    font-size: 14px;
  }

  .Cells {
    width: 10%;
    text-align: center;
  }

  .content {
    /*height: 80px;*/
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }

  .buttoms {
    width: 100%;
    height: 50px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
  }

  .zan {
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }

  .f_vip_detail_title {
    display: flex;
  }

  .f_vip_detail_title > div {
    padding: 5px 0;
  }

  .f_vip_detail_title > div:nth-child(1) {
    flex-shrink: 0;
    width: 76px;
  }

  .vip_model1_info5_font10 {
    text-align: center;
    width: 185px;
    padding: 16px 4px;
    transition-property: color, background-color, border-color,
      text-decoration-color, fill, stroke, opacity, box-shadow, transform,
      filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .vip_model1_info5_font10:hover {
    background: #fff;
  }

  .vip_model1_info5_font11 {
    padding: 5px 0px;
    font-size: 14px;
    font-weight: 400;
    color: rgb(94, 94, 94);
  }

  .vip_model1_info5_font12 {
    padding: 5px 0px;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
  }

  .f-input-box-1123 {
    min-height: 60px;
    border: 1px solid rgb(141 141 141);
    border-radius: 3px;
    padding: 10px;
    margin-bottom: 20px;
  }

  .f-input-add-btn-bg {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(249 250 251);
  }

  .f-info-table {
    width: 100%;
    border-collapse: collapse;
  }
  .f-info-table td {
    border: 1px solid #959595;
    padding: 0.5rem;
  }
  .f-info-label {
    white-space: nowrap;
    font-weight: bold;
    width: 100px;
  }

  .o-view-disabled.f-medicalRecord-form {
    .el-textarea.is-disabled .el-textarea__inner {
      border: 1px solid #959595;
    }
  }

  /* 新增动画效果 */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .duration-300 {
    transition-duration: 300ms;
  }

  .ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 模板框出现动画 */
  .medical-record-template-enter-active,
  .medical-record-template-leave-active {
    transition: opacity 0.3s, transform 0.3s;
  }

  .medical-record-template-enter,
  .medical-record-template-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }
</style>
