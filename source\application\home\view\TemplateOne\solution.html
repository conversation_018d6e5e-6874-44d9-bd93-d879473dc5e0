<!--[meiye_08_07]-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="keywords" content="{$data['keywords']}"/>
    <meta name="Description" content="{$data['description']}"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{$data['webtitle']}</title>
    <link rel="stylesheet" href="/assets/home/<USER>/css-comment.css">
    <link rel="stylesheet" href="/assets/home/<USER>/bootstrap.css">
    <link rel="stylesheet" href="/assets/home/<USER>/swiper.min.css">
    <link rel="stylesheet" href="/assets/home/<USER>/solution.css">
    <link rel="stylesheet" href="/assets/home/<USER>/headerFooter.css">
    <link rel="shortcut icon" type="image/x-icon" href="{$site['web_ico']}">
    <style>
        .nav > li > a:hover, .nav > li > a:focus {
            background: #fff;
            color: #000;
        }

        .sel {
            border-bottom: 2px solid #B35DCC;
        }
    </style>
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            hm.src = "{$data['sq_script_code']}";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>

<body>
<div >
    {include file="/TemplateOne/header"}
    <div id="app">
        <!-- banner -->
        <div>
            <img src="/assets/home/<USER>/solution-imgs/solutionBanner.png" alt="解决方案" class="img-responsive">
        </div>
        <!-- 行业规模 -->
        <div class="industryScale">
            <div class="container">
                <div class="row hidden-xs">
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <img src="/assets/home/<USER>/solution-imgs/scale.png" alt="" class='img-responsive'>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 scalePl">
                        <p class="solutiont1">行业规模</p>
                        <p class="solutiont2">在我国整个美业 <br>和皮肤管理相关的门店大约在17万家 <br> 皮肤管理行业从业人数约有100万人<br>
                            每万城镇居民平均拥有皮肤管理店4家左右<br>平均每人在皮肤管理上消费100元以上</p>
                    </div>
                </div>
                <!-- 移动端 -->
                <div class="row text-center hidden-lg hidden-md hidden-sm">
                    <div class="col-xs-12">
                        <p class="solutiont1">行业规模</p>
                        <p class="solutiont2">
                            在我国整个美业 和皮肤管理相关的门店大约在17万家 皮肤管理行业从业人数约有100万人 每万城镇居民平均拥有皮肤管理店4家左右 平均每人在皮肤管理上消费100元以上
                        </p>
                        <div>
                            <img src="/assets/home/<USER>/solution-imgs/scale.png" alt="" class='img-responsive'>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 行业痛点 -->
        <div class="industryPoint">
            <div class="container">
                <div class="row hidden-xs">
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <p class="solutiont1">行业痛点</p>
                        <p class="solutiont2">营销渠道单一，严重影响拓客<br>互联网程度低 <br> 行业信息化程度低、纸质口头的管理方式为主</p>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <img src="/assets/home/<USER>/solution-imgs/point.png" alt="" class='img-responsive'>
                    </div>
                </div>
                <!-- 移动端 -->
                <div class="row text-center hidden-lg hidden-md hidden-sm">
                    <div class="col-xs-12">
                        <p class="solutiont1">行业痛点</p>
                        <p class="solutiont2">
                            营销渠道单一，严重影响拓客 互联网程度低 行业信息化程度低、纸质口头的管理方式为主
                        </p>
                        <div>
                            <img src="/assets/home/<USER>/solution-imgs/point.png" alt="" class='img-responsive'>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 解决方案 -->
        <div class="solution-box">
            <div class="container text-center">
                <p class="solution-title">解决方案</p>
                <div class="row ">
                    <div class="col-lg-3 col-md-3 col-sm-3 hidden-xs" v-for="(item,index) in solutionList">
                        <div class="solution-list center-block">
                            <img :src="item.imgsrc" alt="">
                            <div class="solution-list-txt">{{item.txt}}</div>
                        </div>
                    </div>
                    <!-- 移动端 -->
                    <div class="col-xs-6 hidden-lg hidden-md hidden-sm" v-for="(item,index) in solutionList">
                        <div class="solution-list center-block">
                            <img :src="item.imgsrc" alt="">
                            <div class="solution-list-txt">{{item.txt}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 获客经营解决方案 -->
        <div class="container text-center hidden-xs">
            <p class="solutiont1 getGuest-title ">获客经营解决方案</p>
            <div class="row getGuest-box">
                <ul class="center-block" style="overflow-x: hidden">
                    <li class="li1 getGuestBg1" style="width: 450px;">
                        <div class="before-txt getGuestBg2">
                            <img src="/assets/home/<USER>/solution-imgs/li-icon1.png" alt="线上网店" class="before-img">
                            <p class="getGuesttxt1">线上网店</p>
                        </div>
                        <div class="after-txt">
                            <p class="after-p1">线上网店</p>
                            <p class="after-p2">
                                基于移动端和PC端的网店展示， 及精美的服务项目展示。消费者 可通过移动端访问店铺，随时分 享和传播
                            </p>
                        </div>
                    </li>
                    <li class="li2 getGuestBg1">
                        <div class="before-txt getGuestBg3">
                            <img src="/assets/home/<USER>/solution-imgs/li-icon2.png" alt="多网点支持" class="before-img">
                            <p class="getGuesttxt1">多网点支持</p>
                        </div>
                        <div class="after-txt">
                            <p class="after-p1">多网点支持</p>
                            <p class="after-p2">
                                采用LBS定位，消费者在使用小程序时，会根据消费者地理位置，推荐附近门店，还会根据消费者的消费习惯，推荐相关门店。 随时获客
                            </p>
                        </div>

                    </li>
                    <li class="li3 getGuestBg1">
                        <div class="before-txt getGuestBg2">
                            <img src="/assets/home/<USER>/solution-imgs/li-icon3.png" alt="随时获客" class="before-img">
                            <p class="getGuesttxt1">随时获客</p>
                        </div>
                        <div class="after-txt">
                            <p class="after-p1">随时获客</p>
                            <p class="after-p2">
                                消费者可在线24小时预约服务，自选技师、项目和时间。门店通过预约中心，合理安排新增预约和排班设置。 贴心完善的开单收银
                            </p>
                        </div>
                    </li>
                    <li class="li4 getGuestBg1">
                        <div class="before-txt getGuestBg3">
                            <img src="/assets/home/<USER>/solution-imgs/li-icon4.png" alt="贴心完善的开单收银" class="before-img">
                            <p class="getGuesttxt1"> 贴心完善的开单收银</p>
                        </div>
                        <div class="after-txt">
                            <p class="after-p1">贴心完善的开单收银</p>
                            <p class="after-p2">
                                美业系统与开单系统无缝结合的收银台，支持微信、支付宝、储值卡、等多种支付方式，从而达到快速收银，杜绝假币和沉淀用户数据的好处。
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- 会员解决方案 -->
        <div class="member-box">
            <div class='container text-center'>
                <p class="member-title">会员解决方案</p>
                <p class="member-subtitle">美业会员系统，留客更轻松</p>
                <div class="row hidden-xs" style="margin-top: 45px;">
                    <div class="col-lg-6 col-md-6 col-sm-6" class="memeber-imgbox">
                        <img src="/assets/home/<USER>/solution-imgs/memberpic1.png" alt="" class="img-responsive">
                        <img src="/assets/home/<USER>/solution-imgs/memberpic2.png" alt=""
                             class="img-responsive memeber-img2">
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <p class="member-subtitle member-txt"> 线上储值卡、次卡、折扣卡，办卡容易成本低，提高顾客 <br> 粘性和消费频率，顾客 微信访问店铺，随时分享和传播。<br>
                            每一笔消费都有短信微信通知，安心周到，顾客更信任。<br> 门店根据美业系统透过会员的数据沉淀，轻松掌握消费者的消费习惯和喜好技师</p>
                    </div>
                </div>
                <!-- 移动端 -->
                <div class='row hidden-lg hidden-md hidden-sm'>
                    <div class="col-xs-12">
                        <img src="/assets/home/<USER>/solution-imgs/memberpic1.png" alt="" class="img-responsive">
                        <p class="member-phone-p">线上储值卡、次卡、折扣卡，办卡容易成本低，提高顾客粘性和消费频率，顾客
                            微信访问店铺，随时分享和传播。门店根据美业系统透过会员的数据沉淀，轻松掌握消费者的消费习惯和喜好技师</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 店务管理解决方案 -->
        <div class="container text-center" style="padding: 3% 0;">
            <p class="member-title">店务管理解决方案</p>
            <div class="row hidden-xs" style="margin: 45px 0;">
                <div class="col-lg-3" v-for="(item,index) in shopList">
                    <img :src="item.imgsrc" alt="" class="img-responsive center-block shopimg">
                    <p class="after-p1">{{item.txt1}}</p>
                    <p class="after-p2 shopmanage-t2 center-block">{{item.txt2}}</p>
                </div>
            </div>
            <!-- 移动端 -->
            <div class="row hidden-lg hidden-md hidden-sm">
                <div class="col-xs-6" v-for="(item,index) in shopList">
                    <img :src="item.imgsrc" alt="" class="img-responsive center-block shopimg">
                    <p class="solutiont2" style="margin-top: 15px;">{{item.txt1}}</p>
                </div>
            </div>
        </div>
        <!-- 售后服务解决方案 pc&phone -->
        <div class='member-box text-center'>
            <p class="member-title" style="margin-bottom: 3%;">售后服务解决方案</p>
            <img src="/assets/home/<USER>/solution-imgs/saleAfter.png" alt="售后服务解决方案" class="img-responsive  center-block">
        </div>
        <!-- 我们的目标 -->
        <div class="ourAim">
            <div class="container text-center">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-lg-offset-3 col-md-offset-3 col-sm-offset-3 hidden-xs">
                        <p class="ourt1">我们的目标是为您打造属于自己的美业品牌</p>
                        <p class="ourt2">提供有效的行业解决方案</p>
                        <a href="{:url('Index/index')}#trial" class="caseshowbtn pull-left">免费试用</a>
                        <a href="{$data['sq_independent_link']}" target="_blank" class="caseshowbtn pull-right">立即咨询</a>
                    </div>
                    <!-- 移动端 -->
                    <div class="col-xs-12  hidden-sm hidden-md hidden-lg">
                        <p class="ourt1phone">我们的目标是为您打造属于自己的美业品牌</p>
                        <p class="ourt2phone">提供有效的行业解决方案</p>
                        <div><a href="{:url('Index/index')}#trial" class="caseshowbtn2">免费试用</a></div>
                        <div><a href="{$data['sq_independent_link']}" target="_blank" class="caseshowbtn2">立即咨询</a></div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    {include file="/TemplateOne/footer"}
</div>
<script src="/assets/home/<USER>/vue2.5.16.js"></script>
<script src="/assets/home/<USER>/jquery-3.2.1.min.js"></script>
<script src="/assets/home/<USER>/bootstrap.js"></script>
<script src="/assets/home/<USER>/swiper.min.js"></script>
<script src="assets/home/<USER>/header.js"></script>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            solutionList: [{
                imgsrc: '/assets/home/<USER>/solution-imgs/solutionicon1.png',
                txt: '获客经营解决方案'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/solutionicon2.png',
                txt: '会员解决方案'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/solutionicon3.png',
                txt: '店务管理解决方案'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/solutionicon4.png',
                txt: '售后服务解决方案'
            }],
            shopList: [{
                imgsrc: '/assets/home/<USER>/solution-imgs/shopicon1.png',
                txt1: '业绩分析',
                txt2: '根据门店当月业绩生成 可视化图表，简洁清晰'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/shopicon2.png',
                txt1: '核算工资',
                txt2: '员工当月工资核算，自 动核算提成+底薪'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/shopicon3.png',
                txt1: '员工排名',
                txt2: '统计当月员工绩效并按 生成列表，可手动点选 各种排名'
            }, {
                imgsrc: '/assets/home/<USER>/solution-imgs/shopicon4.png',
                txt1: '发布活动',
                txt2: '通过后台自定义发布门 店的优惠活动，也可为 分店统一设置'
            }]
        }
    });
    $(document).ready(function () {
        $(".getGuest-box ul li").hover(function () {
            $(this).stop(true).animate({
                width: "450px"
            }, 500).siblings().stop(true).animate({
                width: '228px'
            }, 500);
        });
    });
</script>
</body>

</html>