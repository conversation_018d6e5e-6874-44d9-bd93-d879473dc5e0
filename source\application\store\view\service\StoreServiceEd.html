<!--[meiye_08_17]-->
<div class="layui-fluid">
  <div id="eletest" v-cloak>
    <el-card>
      <el-form
        ref="form"
        :model="form"
        label-width="90px"
        style="width: 70%"
        size="small"
      >
        <el-form-item label="名称：">
          <el-col :span="12">
            <el-input
              placeholder="请输入名称"
              v-model="form.service_name"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="图片：" v-if="form.imgUrl">
          <div style="display: flex; flex-wrap: wrap">
            <div
              v-if="form.imgUrl"
              id="Simg"
              class="Simg"
              v-for="(item,index) in form.imgUrl"
            >
              <div class="dlt" @click="delImg(index)">
                <i class="el-icon-circle-close-outline"></i>
              </div>
              <el-image
                :preview-src-list="[item.file_path]"
                :src="item.file_path"
                fit="contain"
                style="width: 102px; height: 102px"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="img_bor" @click="UploadImg">
              <div class="imgurls"><i class="el-icon-plus"></i></div>
            </div>
          </div>
          <p style="color: #ccc">最多可上传5张，建议尺寸 640 x 640px。</p>
        </el-form-item>
        <el-form-item label="* 价格：" v-if="form.attrTable.val">
          <table class="layui-table" lay-skin="line">
            <colgroup>
              <col width="100" />
              <col width="100" />
              <col width="100" />
              <col width="100" />
              <col width="100" />
              <col width="100" />
            </colgroup>
            <thead>
              <tr>
                <th v-for="(items,indexs) in form.attrTable.name">{{items}}</th>
                <th>总部价格(元)</th>
                <th>价格(元)</th>
                <th>条形码</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(items,indexs) in form.attrTable.val">
                <td v-for="(item ,index) in items">{{item}}</td>
                <td>
                  <span v-text="items.archivePrice"></span>
                </td>
                <td>
                  <div
                    :class="priceInputOk(items.price,items.archivePrice,form.adjustPriceInfo,0)?'':'el-form-item is-error'"
                  >
                    <div class="el-input el-input--small">
                      <input
                        type="text"
                        @input="formEdit"
                        autocomplete="off"
                        maxlength="10"
                        class="el-input__inner"
                        v-model="items.price"
                      />
                    </div>
                  </div>
                </td>
                <td>{{items.barcode}}</td>
              </tr>
            </tbody>
          </table>
        </el-form-item>
        <!-- <el-form-item label="售价范围" v-if="form.attrTable.val">
          <el-col :span="24">
            <div v-if="form.adjustPriceInfo">
              <p>
                通过总部设置的规格单价按
                <span
                  style="font-weight: bolder"
                  v-text="form.adjustPriceInfo.typeName"
                ></span>
                调整
              </p>
              <p>
                上调:<span v-text="form.adjustPriceInfo.increase"></span
                ><span v-text="form.adjustPriceInfo.type==2?'%':'元'"></span>
              </p>
              <p>
                下调:<span v-text="form.adjustPriceInfo.decrease"></span
                ><span v-text="form.adjustPriceInfo.type==2?'%':'元'"></span>
              </p>
            </div>
            <div v-else>不限制</div>
          </el-col>
        </el-form-item> -->
        <el-form-item label="售价" v-if="form.attrTable.val">
          <el-col :span="12">
            <el-input
              placeholder="请输入售价"
              v-model="form.price"
              :disabled="true"
            >
              <template slot="prepend">￥</template>
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item
          label="售价"
          v-else
          required
          :class="priceInputOk(form.price,form.archivePrice,form.adjustPriceInfo,1)?'':'is-error'"
        >
          <el-col :span="12">
            <el-input placeholder="请输入售价" v-model="form.price">
              <template slot="prepend">￥</template>
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="会员价">
          <el-col :span="12">
            <el-input placeholder="请输入会员价" v-model="form.member_price">
              <template slot="prepend">￥</template>
            </el-input>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="售价范围" v-if="!form.attrTable.val">
          <el-col :span="24">
            <div v-if="form.adjustPriceInfo">
              <p>
                通过总部设置的单价<span
                  v-text="form.archivePrice"
                  style="font-weight: bolder"
                ></span
                >元按
                <span
                  style="font-weight: bolder"
                  v-text="form.adjustPriceInfo.typeName"
                ></span>
                调整
              </p>
              <p>
                上调:<span v-text="form.adjustPriceInfo.increase"></span
                ><span v-text="form.adjustPriceInfo.type==2?'%':'元'"></span>
              </p>
              <p>
                下调:<span v-text="form.adjustPriceInfo.decrease"></span
                ><span v-text="form.adjustPriceInfo.type==2?'%':'元'"></span>
              </p>
            </div>
            <div v-else>不限制</div>
          </el-col>
        </el-form-item> -->
        <el-form-item label="网店展示">
          <el-col :span="12">
            <el-radio-group v-model="form.shop_display">
              <el-radio
                v-for="item in exhibition"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item>
        <el-form-item label="展示销量">
          <el-col :span="12">
            <el-radio-group v-model="form.issellnum">
              <el-radio
                v-for="item in yesorno"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="上门服务">
          <el-col :span="12">
            <el-radio-group v-model="form.door_service">
              <el-radio
                v-for="item in door_services"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item> -->
        <el-form-item label="预约上门" v-if="form.door_service == 2">
          <el-col :span="12">
            <el-radio-group v-model="form.pre_door" @change="predoorChan">
              <el-radio
                v-for="item in pre_doors"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item>
        <el-form-item
          label="上门支付"
          v-if="form.door_service == 2 && form.pre_door == 2"
        >
          <el-radio-group v-model="form.pre_door_mode" @change="door">
            <el-radio
              v-for="item in pre_door_modes"
              :key="item.val"
              :label="item.val"
              :value="item.val"
              >{{item.name}}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="百分比"
          v-if="form.door_service == 2 && form.pre_door == 2 && form.pre_door_mode == 1"
        >
          <el-col :span="12">
            <el-input placeholder="请输入百分比" v-model="form.door_percentage">
              <template slot="append">%</template>
            </el-input>
            <p>注意预支付金为百分比时，值为0~100之间整数</p>
          </el-col>
        </el-form-item>
        <el-form-item
          label="固定金额"
          v-if="form.door_service == 2 && form.pre_door == 2 && form.pre_door_mode == 2"
        >
          <el-col :span="12">
            <el-input placeholder="请输入金额" v-model="form.door_fixed_amount">
              <template slot="prepend">￥</template>
            </el-input>
            <p>注意有规格时，固定金额不能大于服务金额最小值</p>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="预约到店">
          <el-radio-group v-model="form.pro_shop" @change="proShopchan">
            <el-radio
              v-for="item in pro_shops"
              :key="item.val"
              :label="item.val"
              :value="item.val"
              >{{item.name}}
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="到店支付" v-if="form.pro_shop == 3">
          <el-radio-group v-model="form.pro_shop_mode" @change="shop">
            <el-radio
              v-for="item in pro_shop_modes"
              :key="item.val"
              :label="item.val"
              :value="item.val"
              >{{item.name}}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="百分比"
          v-if="form.pro_shop == 3 && form.pro_shop_mode == 1"
        >
          <el-col :span="12">
            <el-input placeholder="请输入百分比" v-model="form.shop_percentage">
              <template slot="append">%</template>
            </el-input>
            <p>注意预支付金为百分比时，值为0~100之间整数</p>
          </el-col>
        </el-form-item>
        <el-form-item
          label="固定金额"
          v-if="form.pro_shop == 3 && form.pro_shop_mode == 2"
        >
          <el-col :span="12">
            <el-input placeholder="请输入金额" v-model="form.shop_fixed_amount">
              <template slot="prepend">￥</template>
            </el-input>
            <p>注意有规格时，固定金额不能大于服务金额最小值</p>
          </el-col>
        </el-form-item>
        <el-form-item label="条形码："> {{form.bar_code}} </el-form-item>
        <el-form-item label="分类"> {{form.classification}} </el-form-item>
        <!-- <el-form-item label="标签"> {{form.label}} </el-form-item> -->
        <el-form-item label="规格" v-if="form.sku"> {{form.sku}} </el-form-item>
        <el-form-item label="价格标签"> {{form.price_tag}} </el-form-item>
        <el-form-item label="时长"> {{form.duration}} </el-form-item>
        <el-form-item
          label="视频："
          v-for="(item,index) in form.radioUrl"
          :key="index"
        >
          <div v-if="form.radioUrl" class="radio_sh">
            <div
              class="bof"
              v-if="item['poster']"
              style="border: none; color: white"
            >
              <i class="el-icon-video-play"></i>
            </div>
            <el-image
              :src="item['poster']?item['poster']:''"
              fit="contain"
              style="width: 102px; height: 102px"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-video-play"></i>
              </div>
            </el-image>
            <!--<div class="bof" @click="play"><i class="el-icon-caret-right"></i></div>-->
            <!--<video :src="item.url" alt="" style="width: 100%;margin: auto;" @click=""></video>-->
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="stoAdd"
            >保存</el-button
          >
        </el-form-item>
        <div
          style="
            min-width: 200px;
            min-height: 200px;
            width: 400px;
            height: 400px;
            display: none;
          "
          v-for="(item,index) in form.radioUrl"
          id="vid"
          class=""
        >
          <video
            :src="item.url"
            alt=""
            autoplay="autoplay"
            controls="controls"
            style="width: 100%; height: 100%"
          ></video>
        </div>
      </el-form>
    </el-card>
  </div>
</div>
<script>
  //Demo
  layui.use(["form", "laydate", "upload"], function () {
    var form = layui.form;
    var laydate = layui.laydate;
    var $ = layui.$;
    var upload = layui.upload;
    var admin = layui.admin;

    new Vue({
      el: "#eletest",
      data() {
        return {
          form: {
            id: "{$id}",
            service_name: "",
            bar_code: "",
            price: "",
            member_price: "",
            price_tag: "",
            duration: "",
            imgUrl: [],
            radioUrl: [],
            shop_display: 1,
            issellnum: 1,
            door_service: 1,
            pre_door: 1,
            pre_door_mode: 1,
            door_percentage: "",
            door_fixed_amount: "",
            pro_shop: 1,
            pro_shop_mode: 1,
            shop_percentage: "",
            shop_fixed_amount: "",
            attrTable: [],
          },
          attrTable: [],
          door_services: [
            {
              name: "关闭",
              val: 1,
            },
            {
              name: "开启",
              val: 2,
            },
          ],
          exhibition: [
            {
              name: "展示",
              val: 1,
            },
            {
              name: "不展示",
              val: 2,
            },
          ],
          yesorno: [
            {
              name: "是",
              val: 1,
            },
            {
              name: "否",
              val: 2,
            },
          ],
          pre_doors: [
            {
              name: "付全款",
              val: 1,
            },
            {
              name: "付定金",
              val: 2,
            },
          ],
          pre_door_modes: [
            {
              name: "百分比支付",
              val: 1,
            },
            {
              name: "固定金额支付",
              val: 2,
            },
          ],
          pro_shops: [
            {
              name: "付全款",
              val: 1,
            },
            {
              name: "到店付",
              val: 2,
            },
            {
              name: "付定金",
              val: 3,
            },
          ],
          pro_shop_modes: [
            {
              name: "百分比支付",
              val: 1,
            },
            {
              name: "固定金额支付",
              val: 2,
            },
          ],
          priceInputSuccess: true,
        };
      },
      methods: {
        priceInputOk(p, ap, s, num) {
          console.log(p, ap, s, num);
          if (isNaN(Number(p))) {
            this.priceInputSuccess = false;
            return false;
          }
          if (!s) {
            if (num == 1) {
              this.priceInputSuccess = true;
            }
            return true;
          } else {
            if (s.type == 2) {
              var maxPrice = Math.round(ap * (100 + s.increase * 1));
              var minPrice = Math.round(ap * (100 - s.decrease * 1));
              var price = Math.round(p * 100);
              if (price - maxPrice > 0) {
                this.priceInputSuccess = false;
                return false;
              } else if (price - minPrice < 0) {
                this.priceInputSuccess = false;
                return false;
              }
            } else if (s.type == 1) {
              var maxPrice = Math.round(ap * 100 + s.increase * 100);
              var minPrice = Math.round(ap * 100 - s.decrease * 100);
              var price = Math.round(p * 100);
              if (price - maxPrice > 0) {
                this.priceInputSuccess = false;
                return false;
              } else if (price - minPrice < 0) {
                this.priceInputSuccess = false;
                return false;
              }
            }
          }
          if (num == 1) {
            this.priceInputSuccess = true;
          }
          return true;
        },
        doorChan(val) {
          var that = this;
          if (val == 1) {
            that.form.pre_door = "";
            that.form.pre_door_mode = "";
            that.form.door_percentage = "";
            that.form.door_fixed_amount = "";
          }
        },
        predoorChan(val) {
          var that = this;
          if (val == 1) {
            that.form.pre_door_mode = "";
            that.form.door_percentage = "";
            that.form.door_fixed_amount = "";
          }
        },
        door(val) {
          var that = this;
          if (val == 1) {
            that.form.door_percentage = "";
          } else {
            that.form.door_fixed_amount = "";
          }
        },
        proShopchan(val) {
          var that = this;
          if (val == 1 || val == 2) {
            that.form.pro_shop_mode = "";
            that.form.shop_percentage = "";
            that.form.shop_fixed_amount = "";
          }
        },
        shop(val) {
          var that = this;
          if (val == 1) {
            that.form.shop_percentage = "";
          } else {
            that.form.shop_fixed_amount = "";
          }
        },
        //删除上传的图片
        delImg(val) {
          var that = this;
          var imgUrl = that.form.imgUrl;
          imgUrl.splice(val, 1);
        },
        //上传图片
        UploadImg() {
          var that = this;
          var num = that.form.imgUrl.length;
          var nums = 5 - num ? 5 - num : "0";
          if (nums < 1) {
            return layer.msg("最多只能上传5张图片");
          }
          top.layui.vueCommon.chooseImg(nums, function (arr) {
            var img = that.form.imgUrl;
            that.form.imgUrl = img.concat(arr);
          });
        },
        //查看大图
        See(index) {
          layer.photos({
            photos: "#eletest",
          });
        },
        //放大播放
        play() {
          layer.open({
            type: 1,
            title: false,
            shade: 0,
            area: ["405px", "405px"],
            content: $("#vid"),
            success: function (layero) {
              // hack处理layer层中video播放器全屏样式错乱问题
              setTimeout(function () {
                // $(layero).removeClass('layer-anim');
              }, 0);
            },
          });
        },
        stoAdd() {
          var that = this;
          if (!this.priceInputSuccess) {
            return that.$message({
              type: "info",
              message: "售价设置不正确",
            });
          }
          var price = that.form.price;
          if (price <= 0 && price != "") {
            return that.$message({
              type: "info",
              message: "售价不能小于0",
            });
          }
          var shop_display = that.form.shop_display;
          var door_service = that.form.door_service;
          var pre_door = that.form.pre_door;
          var pre_door_mode = that.form.pre_door_mode;
          var door_percentage = that.form.door_percentage;
          var door_fixed_amount = that.form.door_fixed_amount;
          if (door_service == 2) {
            if (pre_door == 2) {
              if (pre_door_mode == 1) {
                if (door_percentage <= 0 || door_percentage >= 100) {
                  return that.$message({
                    type: "info",
                    message: "百分比只能为0-100之间，不能为0,100",
                  });
                }
              }
              if (pre_door_mode == 2) {
                if (door_fixed_amount == "" || door_fixed_amount == 0) {
                  return that.$message({
                    type: "info",
                    message: "固定金额不能为空且不能为零",
                  });
                }
                if (door_fixed_amount > price && price != "") {
                  return that.$message({
                    type: "info",
                    message: "固定金额不能大于售价",
                  });
                }
              }
            }
          }
          var pro_shop = that.form.pro_shop;
          var pro_shop_mode = that.form.pro_shop_mode;
          var shop_percentage = that.form.shop_percentage;
          var shop_fixed_amount = that.form.shop_fixed_amount;
          if (pro_shop == 3) {
            if (pro_shop_mode == 1) {
              if (shop_percentage <= 0 || shop_percentage >= 100) {
                return that.$message({
                  type: "info",
                  message: "百分比只能为0-100之间，不能为0,100",
                });
              }
            }
            if (pro_shop_mode == 2) {
              if (shop_fixed_amount == "" || shop_fixed_amount == 0) {
                return that.$message({
                  type: "info",
                  message: "固定金额不能为空且不能为零",
                });
              }
              if (shop_fixed_amount > price && price != "") {
                return that.$message({
                  type: "info",
                  message: "固定金额不能大于售价",
                });
              }
            }
          }
          var attrTableVal = that.form.attrTable.val;
          if (attrTableVal) {
            var sxv = [];
            var prices = [];
            var barcode = [];
            for (var i = 0; i < attrTableVal.length; i++) {
              var sku = "";
              for (var j = 0; j < attrTableVal[i].length; j++) {
                sku += attrTableVal[i][j] + ",";
              }
              if (attrTableVal[i]["price"] <= 0) {
                return that.$message({
                  type: "info",
                  message: "规格售价不能小于0",
                });
              }
              if (
                door_fixed_amount != "" &&
                door_fixed_amount > attrTableVal[i]["price"]
              ) {
                return that.$message({
                  type: "info",
                  message: "固定预约金额不能大于规格最低价",
                });
              }
              if (
                shop_fixed_amount != "" &&
                shop_fixed_amount > attrTableVal[i]["price"]
              ) {
                return that.$message({
                  type: "info",
                  message: "固定预约金额不能大于规格最低价",
                });
              }
              sxv.push(sku);
              prices.push(attrTableVal[i]["price"]);
              barcode.push(attrTableVal[i]["barcode"]);
            }
          }
          $.ajax({
            url: "{:url('EdStoSer')}",
            type: "post",
            data: {
              id: that.form.id,
              serviceid: that.form.service_id,
              name: that.form.service_name,
              price: price,
              member_price: that.form.member_price || price,
              imgUrl: that.form.imgUrl,
              sxv: sxv,
              prices: prices,
              barcode: barcode,
              shop_display: shop_display,
              door_service: door_service,
              pre_door: pre_door,
              pre_door_mode: pre_door_mode,
              door_percentage: door_percentage,
              door_fixed_amount: door_fixed_amount,
              pro_shop: pro_shop,
              pro_shop_mode: pro_shop_mode,
              shop_percentage: shop_percentage,
              shop_fixed_amount: shop_fixed_amount,
              issellnum: that.form.issellnum,
            },
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                  onClose: function () {
                    // admin.events.back();
                    CloseWebPage();
                  },
                });
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        // EelStoService(id) {
        //     var that = this;
        //     this.$confirm('此操作将删除该服务, 是否继续?', '提示', {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         type: 'warning'
        //     }).then(() => {
        //         $.ajax({
        //             url: "{:url('DelStoSer')}",
        //             type: "post",
        //             data: {
        //                 id: id
        //             },
        //             success: function (res) {
        //                 if (res.code == 1) {
        //                     that.$message({
        //                         type: 'success',
        //                         message: res.msg,
        //                         onClose: function () {
        //                             admin.events.back();
        //                         }
        //                     });
        //                 } else {
        //                     that.$message({
        //                         type: 'info',
        //                         message: res.msg
        //                     });
        //                 }
        //             }
        //         })
        //     }).catch(() => {
        //         this.$message({
        //             type: 'info',
        //             message: '已取消删除'
        //         });
        //     });
        // },
        getData() {
          var vm = this;
          var id = vm.form.id;
          if (id != "") {
            $.ajax({
              url: "{:url('getStoSerFind')}",
              data: {
                id: id,
              },
              type: "post",
              success: function (res) {
                if (res.status == 1) {
                  vm.form = res.data;
                  var attrTable = res.data.attrTable;
                  if (attrTable["val"]) {
                    var prices = res.data.prices;
                    var barcode = res.data.barcode;
                    var archivePriceArr = res.data.archivePriceArr;
                    for (var i = 0; i < attrTable["val"].length; i++) {
                      attrTable["val"][i]["price"] = prices[i];
                      attrTable["val"][i]["barcode"] = barcode[i];
                      attrTable["val"][i]["archivePrice"] = archivePriceArr[i];
                    }
                    vm.form.attrTable = attrTable;
                  }
                }
              },
            });
          }
        },
        formEdit() {
          this.priceInputSuccess = true;
          this.$forceUpdate();
        },
      },
      created: function () {
        this.getData();
      },
      watch: {
        form: {
          deep: true,
          handler(n) {
            this.priceInputSuccess = true;
            console.log(n);
            this.$forceUpdate();
          },
        },
      },
    });
  });
</script>
<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #3e63dd;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .img_bor {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .imgurls {
    width: 30px;
    height: 30px;
    font-size: 30px;
    color: rgb(204, 204, 204);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .radio_bor {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .radiourls {
    width: 30px;
    height: 30px;
    font-size: 30px;
    color: rgb(204, 204, 204);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .Simg {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
  }

  .Simg:hover > .dlt {
    display: block;
  }

  .dlt {
    position: absolute;
    right: 0;
    width: 20px;
    height: 20px;
    display: none;
    top: -7px;
    right: 3px;
    color: #ccc;
    font-size: 20px;
    z-index: 10;
  }

  .radio_sh {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
  }

  .radio_sh:hover > .dlt {
    display: block;
  }

  .bof {
    width: 18px;
    height: 18px;
    display: flex;
    font-size: 18px;
    border: 1px solid #909399;
    color: #909399;
    border-radius: 50%;
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 10;
  }

  /*规格*/
  .G_boy {
    /*width: 90%;*/
    padding: 10px 1.5%;
    border: 1px solid #e4e7ed;
  }

  .G_top {
    /*width: 97%;*/
    padding: 7px 10px;
    background-color: #f5f7fa;
  }

  .G_bot {
    width: 97%;
    min-height: 20px;
    padding: 10px 1.5%;
  }

  .G_but {
    margin: 0 0 10px;
    display: flex;
    flex-wrap: wrap;
  }

  .G_val {
    width: 80px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #ccc;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 10px;
  }

  .G_ad {
    height: 24px;
    line-height: 24px;
    color: #3e63dd;
    display: flex;
    align-items: center;
  }

  .G_Img {
    width: 80px;
    height: 80px;
    border: 1px solid #ccc;
    margin-top: 10px;
    border-radius: 6px;
  }

  .triangle {
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .triangle:before {
    position: absolute;
    content: "";
    top: -10px;
    left: 30px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #ccc;
  }

  /* 白色覆盖*/
  .triangle:after {
    position: absolute;
    content: "";
    /*减少两像素*/
    top: -9px;
    left: 30px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #fff;
  }

  .G_size {
    height: 26px;
    line-height: 26px;
  }

  .G_One1 {
    position: relative;
    margin-bottom: 10px;
  }

  .G_delval {
    width: 12px;
    height: 12px;
    line-height: 12px;
    position: absolute;
    top: -5px;
    right: 8px;
    cursor: pointer;
  }

  .G_one_del {
    width: 18px;
    height: 18px;
    line-height: 18px;
    position: absolute;
    font-size: 18px;
    color: #999;
    right: 4px;
    top: 18px;
    cursor: pointer;
  }

  .G_Eject {
    width: 220px;
    padding: 10px;
    background-color: rgb(255, 255, 255);
    z-index: 10;
    position: absolute;
  }
</style>
