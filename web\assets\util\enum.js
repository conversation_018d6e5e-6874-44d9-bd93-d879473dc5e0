(function () {
  const deviceTypeMapArray = [
    {
      device_type: "XYJ",
      device_name: "血压血糖检测仪",
      device_color: "o-tag-rose",
    },
  ];
  window.MyEnums = {
    AfterSaleType: {
      reject: 0, // 0：驳回
      review: 1, // 1:待审核
      waitingSendToFactory: 2, // 2:待寄回工厂
      sendingToFactory: 3, // 3:寄回工厂中
      fixing: 4, // 4:处理中
      sendingToStore: 5, // 5:寄回门店中
      done: 6, // 6:已结束
    },
    getDeviceTypeName(device_type) {
      return (
        deviceTypeMapArray.find((item) => item.device_type === device_type)
          ?.device_name || ""
      );
    },
    getDeviceTypeColor(device_type) {
      return (
        deviceTypeMapArray.find((item) => item.device_type === device_type)
          ?.device_color || ""
      );
    },
  };
})();
