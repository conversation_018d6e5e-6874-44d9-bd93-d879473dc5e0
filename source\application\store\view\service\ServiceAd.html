<!--[meiye_08_17]-->
<div class="layui-fluid" id="serviceAdd" v-cloak>
  <div v-loading="loading">
    <el-card>
      <div slot="header" class="clearfix">
        <el-steps
          :active="active"
          finish-status="success"
          simple
          style="margin-top: 20px"
        >
          <el-step title="基本信息"></el-step>
          <el-step title="图文信息"></el-step>
        </el-steps>
      </div>
      <div v-show="active == 1">
        <el-form
          ref="form"
          :rules="rules"
          :model="form"
          label-width="80px"
          style="width: 100%"
          size="small"
        >
          <el-form-item label="名称" prop="service_name">
            <el-col :span="12">
              <el-input
                placeholder="请输入名称"
                v-model="form.service_name"
              ></el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="条形码" prop="bar_code">
            <el-col :span="12">
              <el-input
                maxlength="14"
                placeholder="支持14位的数字英文组合"
                v-model="form.bar_code"
              >
                <el-button
                  slot="append"
                  @click="automatic(1,form,'bar_code')"
                  :loading="isAutomatic"
                >
                  自动生成
                </el-button>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="分类" prop="classification_id">
            <el-select
              v-model="form.classification_id"
              placeholder="请选择分类"
              filterable
            >
              <el-option
                v-for="item in classificationlist"
                :key="item.id"
                :label="item.classification_name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-button type="text" @click="dialog">管理分类</el-button>
          </el-form-item>
          <!-- <el-form-item label="标签" required>
            <el-select
              v-model="form.label_id"
              multiple
              placeholder="请选择标签"
              filterable
            >
              <el-option
                v-for="item in labellist"
                :key="item.id"
                :label="item.label_name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-button type="text" @click="labelMa">管理标签</el-button>
          </el-form-item> -->
          <el-form-item label="调价方案">
            <el-select
              v-model="form.adjust_price_id"
              placeholder="请选择调价方案"
            >
              <el-option :key="0" label="不限制" :value="0"> </el-option>
              <template v-for="item in adjustPriceAllData">
                <el-tooltip class="item" effect="light" placement="right-end">
                  <div slot="content" style="width: 200px">
                    <p style="font-weight: bolder" v-text="item.typeName"></p>
                    <p>
                      上调:<span v-text="item.increase"></span
                      ><span v-text="item.type==2?'%':'元'"></span>
                    </p>
                    <p>
                      下调:<span v-text="item.decrease"></span
                      ><span v-text="item.type==2?'%':'元'"></span>
                    </p>
                  </div>
                  <el-option :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-tooltip>
              </template>
            </el-select>
            <el-button type="text" @click="adjustPriceDialog=true"
              >调价方案管理</el-button
            >
          </el-form-item>
          <el-form-item label="调价范围">
            <template v-for="item in adjustPriceAllData">
              <div v-if="item.id==form.adjust_price_id">
                <p>
                  通过总部设置的单价按
                  <span
                    style="font-weight: bolder"
                    v-text="item.typeName"
                  ></span>
                  调整
                </p>
                <p>
                  上调:<span v-text="item.increase"></span
                  ><span v-text="item.type==2?'%':'元'"></span>
                </p>
                <p>
                  下调:<span v-text="item.decrease"></span
                  ><span v-text="item.type==2?'%':'元'"></span>
                </p>
              </div>
            </template>
          </el-form-item>
          <!-- <el-form-item label="规格" v-if="form.id == ''">
            <div class="G_boy">
              <template v-for="(items,indexs) in attribute_s">
                <div style="position: relative">
                  <div class="G_one_del" @click="DelG(indexs)">
                    <i class="el-icon-circle-close-outline"></i>
                  </div>
                  <div class="G_top" style="display: flex">
                    <div>
                      <template>
                        <el-select
                          style="width: 140px; height: 40px"
                          v-model="items.attribute"
                          filterable
                          allow-create
                          default-first-option
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="item in attribute_v"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </template>
                    </div>
                    <div style="margin-left: 10px" v-if="indexs == 0">
                      <el-checkbox-group
                        v-model="form.AddGImgs"
                        @change="AddGImg(form.AddGImgs)"
                      >
                        <el-checkbox
                          label="添加规格图片"
                          name="type"
                        ></el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                  <div class="G_bot">
                    <div class="G_but" v-if="items.attribute != ''">
                      <div class="G_One1" v-for="(item,index) in items.SpecVal">
                        <div
                          class="G_delval"
                          @click="DelAttAtVal(indexs,index)"
                        >
                          <i class="el-icon-circle-close-outline"></i>
                        </div>
                        <div class="G_val">
                          <span>{{item.text}}</span>
                        </div>
                        <div
                          class="triangle"
                          v-if="indexs == 0 && form.AddGImgs"
                          @click="GGimg(indexs,index)"
                        >
                          <div
                            v-if="!item.img.url"
                            style="font-size: 24px; color: #ccc"
                          >
                            <i class="el-icon-plus"></i>
                          </div>
                          <div v-else>
                            <img
                              :src="item.img.url"
                              alt=""
                              style="width: 100%"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="G_ad">
                        <div>
                          <div>
                            <i class="el-icon-plus"></i>
                            <el-button type="text" @click="ShwEject(indexs)"
                              >添加
                            </el-button>
                          </div>
                          <div class="G_Eject" v-if="items.labclo == true">
                            <el-form :model="lab" label-width="">
                              <el-form-item label="">
                                <el-col :span="24">
                                  <el-select
                                    v-model="form.attributeVal"
                                    filterable
                                    allow-create
                                    default-first-option
                                    placeholder="请选择"
                                  >
                                    <el-option
                                      v-for="item in attribute_val"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    >
                                    </el-option>
                                  </el-select>
                                </el-col>
                              </el-form-item>
                              <el-form-item
                                style="display: flex; justify-content: flex-end"
                              >
                                <el-button
                                  type="primary"
                                  size="mini"
                                  @click="AddSpecVal(indexs)"
                                  >确定
                                </el-button>
                                <el-button
                                  type="primary"
                                  size="mini"
                                  @click="items.labclo = false"
                                  >取消
                                </el-button>
                              </el-form-item>
                            </el-form>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      style="padding: 10px"
                      v-if="indexs == 0 && form.AddGImgs"
                    >
                      <p class="G_size">
                        目前只支持为第一个规格设置不同的规格图片
                      </p>
                      <p class="G_size">
                        设置后，用户选择不同规格会显示不同图片
                      </p>
                      <p class="G_size">建议尺寸：640 x 640像素</p>
                    </div>
                  </div>
                </div>
              </template>
              <div class="G_top">
                <el-button size="small" @click="AddG">添加规格项目</el-button>
                <span style="color: #ccc"
                  >先添加规格分类，如:级别、颜色等，然后再添加规格值，如:初级、中级、高级。</span
                >
              </div>
            </div>
            <p style="color: red">
              提示：服务加入规格后，由于服务添加门店问题，规格信息禁止修改
            </p>
          </el-form-item> -->
          <el-form-item label="规格" v-if="attribute_s != '' && form.id !=''">
            <div class="G_boy">
              <template v-for="(items,indexs) in attribute_s">
                <div style="position: relative">
                  <div class="G_top" style="display: flex">
                    <div>
                      <template>
                        <el-input
                          disabled
                          size="small"
                          style="width: 140px; height: 40px"
                          maxlength="10"
                          v-model="items.attribute"
                        ></el-input>
                      </template>
                    </div>
                  </div>
                  <div class="G_bot">
                    <div class="G_but" v-if="items.attribute != ''">
                      <div class="G_One1" v-for="(item,index) in items.SpecVal">
                        <div class="G_val">
                          <span>{{item.text}}</span>
                        </div>
                        <div
                          class="triangle"
                          v-if="indexs == 0  && form.AddGImgs"
                          @click="GGimg(indexs,index)"
                        >
                          <div
                            v-if="!item.img.url"
                            style="font-size: 24px; color: #ccc"
                          >
                            <i class="el-icon-plus"></i>
                          </div>
                          <div v-else>
                            <img
                              :src="item.img.url"
                              alt=""
                              style="width: 100%"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      style="padding: 10px"
                      v-if="indexs == 0 && form.AddGImgs"
                    >
                      <p class="G_size">
                        目前只支持为第一个规格设置不同的规格图片
                      </p>
                      <p class="G_size">
                        设置后，用户选择不同规格会显示不同图片
                      </p>
                      <p class="G_size">建议尺寸：640 x 640像素</p>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </el-form-item>
          <el-form-item label="价格" required v-if="attrTable.val">
            <table class="layui-table" lay-skin="line">
              <colgroup>
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
              </colgroup>
              <thead>
                <tr>
                  <th v-for="(items,indexs) in attrTable.name">{{items}}</th>
                  <th>价格(元)</th>
                  <th colspan="2">条形码</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(items,indexs) in attrTable.val">
                  <td v-for="(item ,index) in items">{{item}}</td>
                  <td>
                    <el-input
                      @input="formEdit"
                      v-model="items.price"
                      @change="prc(items.price)"
                    >
                    </el-input>
                  </td>
                  <td v-if="form.id == ''" colspan="2">
                    <div style="max-width: 300px">
                      <el-input @input="formEdit" v-model="items.barcode">
                        <el-button
                          slot="append"
                          @click="automatic(1,items,'barcode')"
                          :loading="isAutomatic"
                          >自动生成
                        </el-button>
                      </el-input>
                    </div>
                  </td>
                  <td v-else colspan="2">{{items.barcode}}</td>
                </tr>
              </tbody>
            </table>
          </el-form-item>
          <el-form-item label="售价" v-if="attrTable.val">
            <el-col :span="12">
              <el-input
                placeholder="请输入内容"
                v-model="form.price"
                :disabled="true"
              >
                <template slot="prepend">￥</template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="售价" v-else required>
            <el-col :span="12">
              <el-input placeholder="请输入内容" v-model="form.price">
                <template slot="prepend">￥</template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="会员价">
            <el-col :span="12">
              <el-input placeholder="请输入会员价" v-model="form.member_price">
                <template slot="prepend">￥</template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="价格标签">
            <el-col :span="12">
              <el-input placeholder="原价：￥100" v-model="form.price_tag">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="时长" prop="duration">
            <el-select v-model="form.duration" placeholder="请选择时长">
              <el-option
                v-for="item in durationlist"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网店展示">
            <el-col :span="12">
              <el-radio-group v-model="form.shop_display">
                <el-radio
                  v-for="item in exhibition"
                  :key="item.val"
                  :label="item.val"
                  :value="item.val"
                  >{{item.name}}
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-form-item>
          <!--           <el-form-item label="上门服务">
            <el-col :span="12">
              <el-radio-group v-model="form.door_service" @change="doorChan">
                <el-radio
                  v-for="item in door_services"
                  :key="item.val"
                  :label="item.val"
                  :value="item.val"
                  >{{item.name}}
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-form-item> -->
          <el-form-item label="预约上门" v-if="form.door_service == 2">
            <el-col :span="12">
              <el-radio-group v-model="form.pre_door" @change="predoorChan">
                <el-radio
                  :disabled="item.disabled==1"
                  v-for="item in pre_doors"
                  :key="item.val"
                  :label="item.val"
                  :value="item.val"
                  >{{item.name}}
                </el-radio>
              </el-radio-group>
            </el-col>
            <el-alert
              title="因付全款和付定金(100%)没有差异，新版将关闭付全款设置，开启付定金100%设置，已设置为付全款的仍可生效"
              type="info"
              :closable="false"
            >
            </el-alert>
          </el-form-item>
          <el-form-item
            label="上门定金"
            v-if="form.door_service == 2 && form.pre_door == 2"
          >
            <el-radio-group v-model="form.pre_door_mode" @change="door">
              <el-radio
                v-for="item in pre_door_modes"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            v-if="form.door_service == 2 && form.pre_door == 2 && form.pre_door_mode == 1"
          >
            <el-col :span="12">
              <el-input
                placeholder="请输入百分比"
                v-model="form.door_percentage"
              >
                <template slot="append">%</template>
              </el-input>
              <p style="color: #ccc">
                注意预支付金为百分比时，值为0~100之间整数
              </p>
            </el-col>
          </el-form-item>
          <el-form-item
            label=""
            v-if="form.door_service == 2 && form.pre_door == 2 && form.pre_door_mode == 2"
          >
            <el-col :span="12">
              <el-input
                placeholder="请输入金额"
                v-model="form.door_fixed_amount"
              >
                <template slot="prepend">￥</template>
              </el-input>
              <p style="color: #ccc">
                注意有规格时，固定金额不能大于服务金额最小值
              </p>
            </el-col>
          </el-form-item>
          <!-- <el-form-item label="预约到店">
            <el-col :span="12">
              <el-radio-group v-model="form.pro_shop" @change="proShopchan">
                <el-radio
                  :disabled="item.disabled==1"
                  v-for="item in pro_shops"
                  :key="item.val"
                  :label="item.val"
                  :value="item.val"
                  >{{item.name}}
                </el-radio>
              </el-radio-group>
            </el-col>
            <el-alert
              title="因付全款和付定金(100%)没有差异，新版将关闭付全款设置，开启付定金100%设置，已设置为付全款的仍可生效"
              type="info"
              :closable="false"
            >
            </el-alert>
          </el-form-item> -->
          <el-form-item label="到店定金" v-if="form.pro_shop == 3">
            <el-radio-group v-model="form.pro_shop_mode" @change="shop">
              <el-radio
                v-for="item in pro_shop_modes"
                :key="item.val"
                :label="item.val"
                :value="item.val"
                >{{item.name}}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            v-if="form.pro_shop == 3 && form.pro_shop_mode == 1"
          >
            <el-col :span="12">
              <el-input
                placeholder="请输入百分比"
                v-model="form.shop_percentage"
              >
                <template slot="append">%</template>
              </el-input>
              <p style="color: #ccc">
                注意预支付金为百分比时，值为0~100之间整数
              </p>
            </el-col>
          </el-form-item>
          <el-form-item
            label=""
            v-if="form.pro_shop == 3 && form.pro_shop_mode == 2"
          >
            <el-col :span="12">
              <el-input
                placeholder="请输入金额"
                v-model="form.shop_fixed_amount"
              >
                <template slot="prepend">￥</template>
              </el-input>
              <p style="color: #ccc">
                注意有规格时，固定金额不能大于服务金额最小值
              </p>
            </el-col>
          </el-form-item>
          <el-form-item label="图片" required>
            <div style="display: flex; flex-wrap: wrap">
              <div
                v-if="form.imgUrl"
                id="Simg"
                class="Simg"
                v-for="(item,index) in form.imgUrl"
              >
                <div class="dlt" @click="delImg(index)">
                  <i class="el-icon-circle-close-outline"></i>
                </div>
                <el-image
                  :preview-src-list="[item.file_path]"
                  :src="item.file_path"
                  fit="contain"
                  style="width: 102px; height: 102px"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="img_bor" @click="UploadImg">
                <div class="imgurls"><i class="el-icon-plus"></i></div>
              </div>
            </div>
            <p style="color: #ccc">最多可上传5张，建议尺寸 640 x 640px。</p>
          </el-form-item>
          <!-- <el-form-item label="视频">
            <div
              v-if="form.radioUrl"
              v-for="(item,index) in form.radioUrl"
              class="radio_sh"
            >
              <div class="dlt" @click="Delradio(index)">
                <i class="el-icon-circle-close-outline"></i>
              </div>
              <div
                class="bof"
                v-if="item['poster']"
                style="border: none; color: white"
              >
                <i class="el-icon-video-play"></i>
              </div>
              <el-image
                :src="item['poster']?item['poster']:''"
                fit="contain"
                style="width: 102px; height: 102px"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-video-play"></i>
                </div>
              </el-image>
            </div>
            <div
              v-if="form.radioUrl == ''"
              class="radio_bor"
              @click="UploadRadio"
            >
              <div class="radiourls"><i class="el-icon-plus"></i></div>
            </div>
            <p style="color: #ccc">
              目前仅支持在预览页面进行视频观看，建议上传视频时长为9-30秒，视频宽高比为16:9
            </p>
          </el-form-item> -->
          <el-input type="hidden" v-model="form.id"></el-input>
          <el-form-item>
            <el-button @click="next(1,'form')">下一步</el-button>
          </el-form-item>
          <div
            style="
              min-width: 200px;
              min-height: 200px;
              width: 400px;
              height: 400px;
              display: none;
            "
            v-for="(item,index) in form.radioUrl"
            id="vid"
            class=""
          >
            <video
              :src="item.url"
              alt=""
              autoplay="autoplay"
              controls="controls"
              style="width: 100%; height: 100%"
            ></video>
          </div>
        </el-form>
      </div>
      <div v-show="active == 2">
        <el-alert
          title="可以点击空白处查看编译效果"
          type="info"
          :closable="false"
        >
        </el-alert>
        <el-row type="flex">
          <div
            style="width: 500px; box-sizing: border-box; padding: 10px 90px"
            class="html5-editor-show"
          >
            <div
              class="goods-details-block"
              style="width: 320px; border: 1px solid rgba(0, 0, 0, 0.1)"
            >
              <h4>基本信息区</h4>
              <p>固定样式，显示商品主图、价格等信息</p>
            </div>
            <div
              v-if="!content"
              class="goods-details-block"
              style="
                width: 320px;
                background-color: #fff;
                border: 1px solid rgba(0, 0, 0, 0.1);
              "
            >
              <h4>服务详情区</h4>
              <p>暂无，立即编辑</p>
            </div>
            <div
              v-if="content"
              v-html="content"
              style="
                width: 320px;
                height: 500px;
                overflow-y: auto;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-sizing: border-box;
                padding: 10px;
              "
            ></div>
          </div>
          <div
            style="
              position: relative;
              min-height: 500px;
              width: 450px;
              padding-top: 10px;
            "
          >
            <div
              type="text/plain"
              ref="ueditorBox"
              style="min-height: 500px; width: 100%"
              id="ueditorBox"
            ></div>
          </div>
        </el-row>
        <el-form label-width="100px" size="small">
          <el-form-item>
            <el-button @click="onSubmit">确定</el-button>
            <el-button @click="next(2)">上一页</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-dialog
      title="调价管理"
      :visible.sync="adjustPriceDialog"
      append-to-body
      width="900px"
    >
      <el-popover
        placement="right"
        width="300"
        v-model="adjustPriceEdit"
        trigger="click"
      >
        <el-form
          ref="adjustPriceForm"
          :model="adjustPriceData"
          :rules="adjustPriceRules"
          size="small"
          label-width="60px"
        >
          <el-form-item label="">
            <el-col :span="24">
              <span style="font-weight: bolder">方案设置</span>
            </el-col>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-col :span="24">
              <el-input placeholder="方案名称" v-model="adjustPriceData.name">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-col :span="24">
              <el-radio
                v-model="adjustPriceData.type"
                :label="1"
                :disabled="adjustPriceData.id>0"
                >数值调整
              </el-radio>
              <el-radio
                v-model="adjustPriceData.type"
                :label="2"
                :disabled="adjustPriceData.id>0"
                >比例调整
              </el-radio>
            </el-col>
          </el-form-item>
          <el-form-item label="上调" prop="increase">
            <el-col :span="24">
              <el-input v-model="adjustPriceData.increase">
                <template slot="prepend">上调</template>
                <template slot="append"
                  ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
                </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="下调" prop="decrease">
            <el-col :span="24">
              <el-input v-model="adjustPriceData.decrease">
                <template slot="prepend">下调</template>
                <template slot="append"
                  ><span v-text="adjustPriceData.type==1?'元':'%'"></span>
                </template>
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              :loading="addnow"
              @click="confirmAdjustPrice"
              >确定
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="adjustPriceEdit = false"
              >取消</el-button
            >
          </el-form-item>
        </el-form>
        <el-button
          @click="toAddAdjustPrice"
          type="primary"
          size="small"
          slot="reference"
        >
          <span v-text="'设置调价方案'"></span>
        </el-button>
      </el-popover>
      <el-table
        :data="adjustPriceTableData"
        style="width: 100%; margin-top: 15px"
      >
        <el-table-column show-overflow-tooltip prop="name" label="名称">
        </el-table-column>
        <el-table-column prop="typeName" label="类型"> </el-table-column>
        <el-table-column prop="increase" label="上调"> </el-table-column>
        <el-table-column prop="decrease" label="下调"> </el-table-column>
        <el-table-column label="使用次数">
          <el-table-column prop="productNum" label="产品"> </el-table-column>
          <el-table-column prop="serviceNum" label="服务"> </el-table-column>
        </el-table-column>
        <el-table-column prop="status" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="delAdjustPrice(scope.row.id)"
              >删除
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="adjustPriceData=scope.row;adjustPriceEdit=true;"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="overflow: hidden">
        <el-pagination
          style="margin-top: 10px; float: right"
          v-if="adjustPricePageObj.total>0"
          background
          :current-page="adjustPricePageObj.page"
          @current-change="adjustPriceCurrentChange"
          :page-size="adjustPricePageObj.limit"
          layout="total, prev, pager, next"
          :total="adjustPricePageObj.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog
      title="分类管理"
      :visible.sync="dialogTableVisible"
      @close="getifis"
      :modal-append-to-body="false"
    >
      <div class="layui-form-item">
        <div class="layui-inline">
          <el-popover
            placement="right"
            width="200"
            trigger="click"
            v-model="clo"
          >
            <el-form ref="form" :model="ifi" label-width="" size="small">
              <el-form-item label="">
                <el-col :span="24">
                  <el-input placeholder="输入名称" v-model="ifi.name">
                  </el-input>
                </el-col>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="Adifi"
                  >确定</el-button
                >
                <el-button type="primary" size="small" @click="clo = false"
                  >取消
                </el-button>
              </el-form-item>
            </el-form>
            <el-button type="primary" size="small" slot="reference"
              >添加分类</el-button
            >
          </el-popover>
          <el-button v-if="shw" type="primary" size="small" @click="hid"
            >完成</el-button
          >
          <el-button v-else type="primary" size="small" @click="Shows"
            >编辑</el-button
          >
        </div>
      </div>
      <div>
        <div class="layui-row layui-col-space15">
          <div class="layui-col-md12">
            <div class="layui-card">
              <div class="layui-card-body" pad15>
                <el-table :data="ifiData" style="width: 100%">
                  <el-table-column type="index" label="序号" width="50">
                  </el-table-column>
                  <el-table-column prop="classification_name" label="分类">
                    <template slot-scope="scope">
                      <el-input
                        v-if="shw"
                        v-model="scope.row.classification_name"
                        size="medium"
                      ></el-input>
                      <span v-else>{{scope.row.classification_name}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="addtime" label="创建时间">
                  </el-table-column>
                  <el-table-column prop="status" label="操作">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="mini"
                        @click="Delifi(scope.row.id)"
                        >删除
                      </el-button>
                      <el-button
                        v-if="shw"
                        type="text"
                        size="mini"
                        @click="Editifi(scope.row.classification_name,scope.row.id)"
                      >
                        修改
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="ifi_size_change"
                  @current-change="ifi_current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="ifitotal"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="标签管理"
      :visible.sync="dialogTablelabel"
      @close="getLabs"
      :modal-append-to-body="false"
    >
      <div class="layui-form-item">
        <div class="layui-inline">
          <el-popover
            placement="right"
            width="200"
            trigger="click"
            v-model="labclo"
          >
            <el-form ref="form" :model="lab" label-width="" size="small">
              <el-form-item label="">
                <el-col :span="24">
                  <el-input placeholder="输入名称" v-model="lab.name">
                  </el-input>
                </el-col>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="Adlab"
                  >确定</el-button
                >
                <el-button type="primary" size="small" @click="labclo = false"
                  >取消
                </el-button>
              </el-form-item>
            </el-form>
            <el-button type="primary" size="small" slot="reference"
              >添加标签</el-button
            >
          </el-popover>
          <el-button v-if="labshw" type="primary" size="small" @click="labhid"
            >完成</el-button
          >
          <el-button v-else type="primary" size="small" @click="labShows"
            >编辑</el-button
          >
        </div>
      </div>
      <div>
        <div class="layui-row layui-col-space15">
          <div class="layui-col-md12">
            <div class="layui-card">
              <div class="layui-card-body" pad15>
                <el-table :data="labelData" style="width: 100%">
                  <el-table-column type="index" label="序号" width="50">
                  </el-table-column>
                  <el-table-column prop="label_name" label="标签">
                    <template slot-scope="scope">
                      <el-input
                        v-if="labshw"
                        v-model="scope.row.label_name"
                        size="medium"
                      ></el-input>
                      <span v-else>{{scope.row.label_name}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="addtime" label="创建时间">
                  </el-table-column>
                  <el-table-column prop="status" label="操作">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="mini"
                        @click="Dellab(scope.row.id)"
                        >删除
                      </el-button>
                      <el-button
                        v-if="labshw"
                        type="text"
                        size="mini"
                        @click="Editlab(scope.row.label_name,scope.row.id)"
                      >
                        修改
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: 10px; float: right"
                  background
                  @size-change="lab_size_change"
                  @current-change="lab_current_change"
                  :pager-count="5"
                  :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="labtotal"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</div>
<!--百度编译器-->
<script>
  window.UEDITOR_HOME_URL = "/assets/ueditor/";
  window.UEDITOR_UPLOAD_IMAGE_URL = "";
</script>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/ueditor/ueditor.config.js"
></script>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/ueditor/ueditor.all.js"
></script>
<script
  type="text/javascript"
  charset="utf-8"
  src="/assets/ueditor/lang/zh-cn/zh-cn.js"
></script>
<script>
  UE.Editor.prototype._bkGetActionUrl = UE.Editor.prototype.getActionUrl;
  UE.Editor.prototype.getActionUrl = function (action) {
    if (action == "uploadimage" || action == "uploadscrawl") {
      return (
        window.location.protocol +
        "//" +
        window.location.host +
        "/" +
        "{:url('pageCommon/Upload/image')}"
      ); //这就是自定义的上传地址
    } else if (action == "uploadvideo") {
      return (
        window.location.protocol +
        "//" +
        window.location.host +
        "/" +
        "{:url('pageCommon/Upload/image')}"
      );
    } else if (action == "listimage") {
      return (
        window.location.protocol +
        "//" +
        window.location.host +
        "/" +
        "{:url('pageCommon/material/getUEEditorImageData')}"
      );
    } else if (action == "catchimage") {
      return (
        window.location.protocol +
        "//" +
        window.location.host +
        "/" +
        "{:url('pageCommon/Upload/catchimage')}"
      );
    } else {
      return this._bkGetActionUrl.call(this, action);
    }
  };
</script>
<script>
  //Demo
  layui.use(["index"], function () {
    var $ = layui.$;
    new Vue({
      el: "#serviceAdd",
      data() {
        var money = (rule, value, callback) => {
          if (value === "") {
            callback();
          } else if (isNaN(Number(value))) {
            callback(new Error("必须是数字"));
          } else {
            if (
              /^(([1-9]\d*)(\.\d{1,2})?)$|^(0\.0([1-9]))$|^(0\.([1-9])\d?)$/.test(
                value
              ) ||
              value == 0
            ) {
              callback();
            } else {
              callback(new Error("数值最多保留2位小数"));
            }
          }
        };
        return {
          fromStore: "{$fromStore}" === "1",
          editor: "",
          isAutomatic: false,
          // 调价管理
          adjustPriceDialog: false,
          adjustPriceData: {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          },
          adjustPriceRules: {
            increase: [
              {required: true, message: "上调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            decrease: [
              {required: true, message: "下调设置不能为空", trigger: "blur"},
              {max: 6, message: "长度在6个字符以内", trigger: "blur"},
              {validator: money, trigger: ["change", "blur"]},
            ],
            name: [
              {required: true, message: "方案名称不能为空", trigger: "blur"},
              {max: 20, message: "长度在20个字符以内", trigger: "blur"},
            ],
          },
          adjustPriceSet: true,
          adjustPriceEdit: false,
          adjustPriceTableData: [],
          adjustPricePageObj: {
            page: 1,
            limit: 10,
            total: 0,
          },
          addnow: false,
          adjustPriceAllData: [],
          // 调价管理
          loading: false,
          editorImg: [],
          active: 1,
          content: "",
          form: {
            id: "{$id}",
            service_name: "",
            bar_code: "",
            price: "",
            price_tag: "",
            member_price: "", // 添加会员价字段
            duration: "",
            classification_id: "",
            label_id: "",
            imgUrl: [],
            radioUrl: [],
            AddGImgs: false,
            attributeVal: "",

            shop_display: 1,
            door_service: 1,
            pre_door: 1,
            pre_door_mode: 1,
            door_percentage: "",
            door_fixed_amount: "",
            pro_shop: 1,
            pro_shop_mode: 1,
            shop_percentage: "",
            shop_fixed_amount: "",
            adjust_price_id: 0, //调价管理
          },
          labclo: false,
          lab: {
            name: "",
          },
          attribute_s: [], //判断是否添加属性
          attribute_v: [], //属性
          attribute_val: [], //属性值
          attrTable: [], //属性表格

          classificationlist: "",
          labellist: "",
          durationlist: [
            {id: 15, name: "15分钟"},
            {id: 30, name: "30分钟"},
            {id: 45, name: "45分钟"},
            {id: 60, name: "60分钟"},
            {id: 75, name: "75分钟"},
            {id: 90, name: "90分钟"},
            {id: 115, name: "115分钟"},
            {id: 130, name: "130分钟"},
            {id: 145, name: "145分钟"},
            {id: 160, name: "160分钟"},
            {id: 180, name: "180分钟"},
          ],
          door_services: [
            {
              name: "关闭",
              val: 1,
            },
            {
              name: "开启",
              val: 2,
            },
          ],
          exhibition: [
            {
              name: "展示",
              val: 1,
            },
            {
              name: "不展示",
              val: 2,
            },
          ],
          pre_doors: [
            {
              name: "付全款",
              val: 1,
              disabled: 1,
            },
            {
              name: "付定金",
              val: 2,
            },
          ],
          pre_door_modes: [
            {
              name: "百分比支付",
              val: 1,
            },
            {
              name: "固定金额支付",
              val: 2,
            },
          ],
          pro_shops: [
            {
              name: "付全款",
              val: 1,
              disabled: 1,
            },
            {
              name: "到店付",
              val: 2,
            },
            {
              name: "付定金",
              val: 3,
            },
          ],
          pro_shop_modes: [
            {
              name: "百分比支付",
              val: 1,
            },
            {
              name: "固定金额支付",
              val: 2,
            },
          ],
          rules: {
            service_name: [
              {required: true, message: "请输入名称", trigger: "blur"},
              // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
            ],
            bar_code: [
              {required: true, message: "请输入条形码", trigger: "blur"},
            ],
            classification_id: [
              {required: true, message: "请选择分类", trigger: "change"},
            ],
            label_id: [
              {
                type: "array",
                required: true,
                message: "请至少选择一种标签",
                trigger: "change",
              },
            ],
            duration: [
              {required: true, message: "请选择时长", trigger: "change"},
            ],
          },
          dialogTableVisible: false,
          dialogTablelabel: false,
          ifiData: [], //分类信息
          ifipage: 1,
          ifilimit: 10,
          ifitotal: 0, //分类数据
          ifi: {
            name: "",
          },
          shw: false,
          clo: false,

          labelData: [],
          labpage: 1,
          lablimit: 10,
          labtotal: 0, //分类数据
          lab: {
            name: "",
          },
          labshw: false,
          labclo: false,
        };
      },
      methods: {
        // 调价管理
        confirmAdjustPrice() {
          var vm = this;
          this.$refs["adjustPriceForm"].validate(function (bol) {
            if (!bol) {
              vm.$message({
                showClose: true,
                message: "请完善您的设置信息，带*号为必填或者必选",
                type: "warning",
                duration: 3000,
              });
            } else {
              $.ajax({
                url: "{:url('Adjustprice/save')}",
                data: vm.adjustPriceData,
                type: "POST",
                success(res) {
                  console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.getAdjustPriceAllData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            }
          });
        },
        toAddAdjustPrice() {
          this.adjustPriceData = {
            id: 0,
            name: "",
            type: 1,
            increase: "",
            decrease: "",
          };
        },
        getAdjustPriceData() {
          var vm = this;
          $.ajax({
            url: "{:url('Adjustprice/getPage')}",
            data: {
              page: vm.adjustPricePageObj.page,
              limit: vm.adjustPricePageObj.limit,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.adjustPriceTableData = res.data;
                vm.adjustPricePageObj.total = res.count;
              } else {
                vm.adjustPriceTableData = [];
              }
            },
          });
        },
        adjustPriceCurrentChange(val) {
          this.adjustPricePageObj.page = val;
          this.getAdjustPriceData();
        },
        delAdjustPrice(id) {
          var vm = this;
          this.$confirm("确定要删除调价方案?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              $.ajax({
                url: "{:url('Adjustprice/del')}",
                data: {id: id},
                type: "POST",
                success(res) {
                  // console.log(res);
                  if (res.code == 1) {
                    vm.$message({
                      type: "success",
                      message: res.msg,
                    });
                    vm.getAdjustPriceData();
                    vm.getAdjustPriceAllData();
                    vm.adjustPriceEdit = false;
                  } else {
                    vm.$message({
                      type: "error",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        getAdjustPriceAllData() {
          var vm = this;
          $.ajax({
            url: "{:url('Adjustprice/getAll')}",
            data: {},
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.adjustPriceAllData = res.data;
              } else {
                vm.adjustPriceAllData = [];
              }
            },
          });
        },
        // 调价管理
        getifis() {
          var that = this;
          that.getifiList();
        },
        getLabs() {
          var that = this;
          that.geLabList();
        },
        //获取分类信息
        getifiList() {
          var that = this;
          $.ajax({
            url: "{:url('getifiList')}",
            type: "post",
            data: {},
            success: function (res) {
              if (res.code == 1) {
                that.classificationlist = res.data;
              }
            },
          });
        },
        //获取标签信息
        geLabList() {
          var that = this;
          $.ajax({
            url: "{:url('geLabList')}",
            type: "post",
            data: {},
            success: function (res) {
              if (res.code == 1) {
                that.labellist = res.data;
              }
            },
          });
        },
        // 添加分类
        Adifi() {
          var that = this;
          var name = that.ifi.name;
          if ($.trim(name) == "") {
            return that.$message({
              type: "info",
              message: "请输入名称",
            });
          }
          that.ifi.name = "";
          var id = "";
          that.Addifi(name, id);
          that.clo = false;
        },
        //分类编辑
        Shows() {
          var that = this;
          that.shw = true;
        },
        //分类编辑完成
        hid() {
          var that = this;
          that.shw = false;
        },
        //修改分类
        Editifi(name, id) {
          var that = this;
          var name = name;
          var id = id;
          that.Addifi(name, id);
        },
        //删除分类
        Delifi(id) {
          var that = this;
          that
            .$confirm("是否删除该分类", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('Delifi')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                      onClose: function () {
                        that.dialogTablelabel = false;
                      },
                    });
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        //打开管理分类页面
        dialog() {
          var that = this;
          that.Getifi();
          that.dialogTableVisible = true;
        },
        //打开标签管理
        labelMa() {
          var that = this;
          that.Getlab();
          that.dialogTablelabel = true;
        },
        //分类管理分页
        ifi_size_change(val) {
          var that = this;
          that.ifilimit = val;
          that.ifipage = 1;
          that.Getifi();
        },
        /* 切换页数 */
        ifi_current_change(val) {
          var that = this;
          that.ifipage = val;
          that.Getifi();
        },

        //添加标签
        Adlab() {
          var that = this;
          var name = that.lab.name;
          if ($.trim(name) == "") {
            return that.$message({
              type: "info",
              message: "请输入名称",
            });
          }
          that.lab.name = "";
          var id = "";
          that.Addlab(name, id);
          that.labclo = false;
        },
        //编辑标签
        labShows() {
          var that = this;
          that.labshw = true;
        },
        //完成编辑
        labhid() {
          var that = this;
          that.labshw = false;
        },
        //修改分类
        Editlab(name, id) {
          var that = this;
          var name = name;
          var id = id;
          that.Addlab(name, id);
        },
        //删除分类
        Dellab(id) {
          var that = this;
          that
            .$confirm("是否删除该标签", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(function () {
              $.ajax({
                url: "{:url('Dellab')}",
                type: "post",
                data: {id: id},
                success: function (res) {
                  if (res.status == 1) {
                    that.$message({
                      type: "success",
                      message: res.msg,
                      onClose: function () {
                        that.dialogTablelabel = false;
                      },
                    });
                  } else {
                    that.$message({
                      type: "info",
                      message: res.msg,
                    });
                  }
                },
              });
            })
            .catch(function () {
              that.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        },
        //执行添加分类
        Addlab: function (name, id) {
          var that = this;
          var id = id;
          $.ajax({
            url: "{:url('Adlab')}",
            data: {
              name: name,
              id: id,
            },
            type: "post",
            success: function (res) {
              that.ifi.name = "";
              if (res.status == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.Getlab();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //获取标签信息
        Getlab: function () {
          var vm = this;
          vm.promotionloading = true;
          $.ajax({
            url: "{:url('Getlab')}",
            data: {
              page: vm.labpage,
              limit: vm.lablimit,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.labelData = res.data;
                vm.labtotal = res.count;
              }
              vm.promotionloading = false;
            },
          });
        },
        //标签管理分页
        lab_size_change(val) {
          var that = this;
          that.lablimit = val;
          that.labpage = 1;
          that.Getlab();
        },
        /* 切换页数 */
        lab_current_change(val) {
          var that = this;
          that.labpage = val;
          that.Getlab();
        },
        //执行添加分类
        Addifi: function (name, id) {
          var that = this;
          var id = id;
          $.ajax({
            url: "{:url('Adifi')}",
            data: {
              name: name,
              id: id,
            },
            type: "post",
            success: function (res) {
              that.ifi.name = "";
              if (res.status == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                });
                that.Getifi();
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                });
              }
            },
          });
        },
        //获取分类信息
        Getifi: function () {
          var vm = this;
          vm.promotionloading = true;
          $.ajax({
            url: "{:url('Getifi')}",
            data: {
              page: vm.ifipage,
              limit: vm.ifilimit,
            },
            type: "post",
            success: function (res) {
              if (res.code == 0) {
                vm.ifiData = res.data;
                vm.ifitotal = res.count;
              }
              vm.promotionloading = false;
            },
          });
        },

        updateData(c) {
          this.content = c;
        },
        editorUploadImg() {
          var that = this;
          top.layui.vueCommon.chooseImg(1, function (arr) {
            that.editorImg = arr;
            that.$forceUpdate();
          });
        },
        copyLink() {
          if (this.editorImg.length == 0) {
            this.$message({
              showClose: false,
              message: "请先选择图片",
              type: "warning",
              duration: 1000,
            });
          } else {
            var urlresult = $(this.$refs.editorImginput)[0];
            urlresult.select(); // 选择对象
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
              showClose: false,
              message: "复制成功",
              type: "success",
              duration: 1000,
            });
          }
        },
        next(num, formName) {
          var that = this;
          if (num == 1) {
            that.$refs[formName].validate((valid) => {
              if (valid) {
                // if (that.form.label_id != "") {
                if (that.form.imgUrl != "") {
                  that.active++;
                } else {
                  return that.$message({
                    type: "info",
                    message: "请至少上传一张图片",
                  });
                }
                // } else {
                //   return that.$message({
                //     type: "info",
                //     message: "请选择标签",
                //   });
                // }
              } else {
                return that.$message({
                  type: "info",
                  message: "请填写必填项",
                });
              }
            });
          } else {
            that.active = 1;
          }
        },
        doorChan(val) {
          var that = this;
          if (val == 1) {
            that.form.pre_door = 2;
            that.form.pre_door_mode = 1;
            that.form.door_percentage = "";
            that.form.door_fixed_amount = "";
          }
        },
        predoorChan(val) {
          var that = this;
          if (val == 1) {
            that.form.pre_door_mode = "";
            that.form.door_percentage = "";
            that.form.door_fixed_amount = "";
          }
        },
        door(val) {
          var that = this;
          if (val == 1) {
            that.form.door_percentage = "";
          } else {
            that.form.door_fixed_amount = "";
          }
        },
        proShopchan(val) {
          var that = this;
          if (val == 1 || val == 2) {
            that.form.pro_shop_mode = 1;
            that.form.shop_percentage = "";
            that.form.shop_fixed_amount = "";
          }
        },
        shop(val) {
          var that = this;
          if (val == 1) {
            that.form.shop_percentage = "";
          } else {
            that.form.shop_fixed_amount = "";
          }
        },
        prc(val) {
          // console.log(val)
        },
        //添加规格
        AddG() {
          var that = this;
          var key = that.attribute_s.length;
          if (key >= 3) {
            return that.$message({
              type: "info",
              message: "最多支持3组规格！",
            });
          }
          that.attribute_s.push({SpecVal: [], attribute: "", labclo: false});
        },
        //删除规格
        DelG(indexs) {
          var that = this;
          var attribute_s = that.attribute_s;
          attribute_s.splice(indexs, 1);
          that.attrTable = that.attrTableAdd(that.attribute_s, (o) => []);
        },
        //规格图片
        AddGImg(a) {
          var that = this;
          if (a == true) {
            that.form.AddGImgs = true;
          } else {
            that.form.AddGImgs = false;
          }
        },
        //添加属性值
        AddSpecVal(indexs) {
          var that = this;
          var attribute_s = that.attribute_s;
          // for (var i = 0; i < that.form.attributeVal.length; i++) {
          //     attribute_s[index]['SpecVal'] = attribute_s[index]['SpecVal'].concat(that.form.attributeVal[i]);
          //     that.form.attributeVal.splice(i, 1);
          // }
          if ($.trim(that.form.attributeVal) == "") {
            return that.$message({
              type: "info",
              message: "属性值不能为空",
            });
          }
          var pattern = new RegExp(
            "[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]"
          );
          if (pattern.test(that.form.attributeVal)) {
            return that.$message({
              type: "info",
              message: "禁止输入特殊字符",
            });
          }
          for (var i = 0; i < attribute_s[indexs]["SpecVal"].length; i++) {
            var text = attribute_s[indexs]["SpecVal"][i]["text"];
            if (text == that.form.attributeVal) {
              return that.$message({
                type: "info",
                message: "该属性已存在",
              });
            }
          }
          that.form.price = "";
          var data = {text: that.form.attributeVal, img: ""};
          attribute_s[indexs]["SpecVal"].push(data);
          attribute_s[indexs]["labclo"] = false;
          that.form.attributeVal = "";
          that.attrTable = that.attrTableAdd(that.attribute_s, (o) => []);
        },
        attrTableAdd(n, o) {
          var that = this;
          var Table = [];
          if (n.length == 0) {
            return Table;
          }
          var a = [];
          var name = [];
          for (var i = 0; i < n.length; i++) {
            var b = [];
            for (var j = 0; j < n[i]["SpecVal"].length; j++) {
              b.push(n[i]["SpecVal"][j]["text"]);
            }
            name.push(n[i]["attribute"]);
            a.push(b);
          }
          Table["name"] = name;
          var newarr;
          if (a.length == 1) {
            for (var n = 0; n < a[0].length; n++) {
              a[0][n] = a[0][n].split(",");
            }
            Table["val"] = a[0];
            return Table;
          } else {
            for (var m = 0; m < a.length; m++) {
              if (m == 0) {
                newarr = a[0];
              } else {
                newarr = that.createSku(newarr, a[m]);
              }
            }
          }
          for (var n = 0; n < newarr.length; n++) {
            newarr[n] = newarr[n].split(",");
          }
          Table["val"] = newarr;
          return Table;
        },
        //删除属性值
        DelAttAtVal(indexs, index) {
          // console.log(Indexs)
          var that = this;
          var attribute_s = that.attribute_s;
          attribute_s[indexs]["SpecVal"].splice(index, 1);
          that.attrTable = that.attrTableAdd(that.attribute_s, (o) => []);
        },
        //弹出框显示
        ShwEject(index) {
          var that = this;
          var attribute_s = that.attribute_s;
          attribute_s[index]["labclo"] = true;
        },
        GGimg(indexs, index) {
          var that = this;
          that.attribute_s[indexs]["SpecVal"][index]["img"];
          top.layui.vueCommon.chooseImg(1, function (arr) {
            that.attribute_s[indexs]["SpecVal"][index]["img"] = arr[0];
          });
        },
        createSku(arr1, arr2) {
          var newarr = [];
          for (var i = 0; i < arr1.length; i++) {
            if (arr2.length == 0) {
              return arr1;
            } else {
              for (var j = 0; j < arr2.length; j++) {
                newarr.push(arr1[i] + "," + arr2[j]);
              }
            }
          }
          return newarr;
        },

        //查看大图
        See(index) {
          layer.photos({
            photos: "#serviceAdd",
          });
        },
        //删除上传的图片
        delImg(val) {
          var that = this;
          var imgUrl = that.form.imgUrl;
          imgUrl.splice(val, 1);
        },
        //上传图片
        UploadImg() {
          var that = this;
          var num = that.form.imgUrl.length;
          var nums = 5 - num ? 5 - num : "0";
          if (nums < 1) {
            return layer.msg("最多只能上传5张图片");
          }
          top.layui.vueCommon.chooseImg(nums, function (arr) {
            var img = that.form.imgUrl;
            that.form.imgUrl = img.concat(arr);
          });
        },
        //上传视频
        UploadRadio() {
          var that = this;
          top.layui.vueCommon.chooseAudio(1, function (arr) {
            // console.log(arr);
            that.form.radioUrl = arr;
          });
        },
        //删除视频
        Delradio(val) {
          var that = this;
          var radioUrl = that.form.radioUrl;
          radioUrl.splice(val, 1);
        },
        //放大播放
        play() {
          layer.open({
            type: 1,
            title: false,
            area: ["405px", "405px"],
            content: $("#vid"),
            success: function (layero) {
              // hack处理layer层中video播放器全屏样式错乱问题
              setTimeout(function () {
                // $(layero).removeClass('layer-anim');
              }, 0);
            },
          });
        },
        //提交
        onSubmit() {
          var that = this;
          that.loading = true;
          var id = that.form.id;
          var service_name = that.form.service_name;
          var bar_code = that.form.bar_code;
          var price = Number(that.form.price);
          if (price != "" && price <= 0) {
            return that.$message({
              type: "info",
              message: "售价不能小于0",
              onClose: function () {
                that.loading = false;
              },
            });
          }
          var price_tag = that.form.price_tag;
          var mode = that.form.mode;
          var shop_display = that.form.shop_display;
          var door_service = that.form.door_service;
          var pre_door = that.form.pre_door;
          var pre_door_mode = that.form.pre_door_mode;
          var door_percentage = that.form.door_percentage;
          var door_fixed_amount = Number(that.form.door_fixed_amount);
          if (door_service == 2) {
            if (pre_door == 2) {
              if (pre_door_mode == 1) {
                if (door_percentage <= 0 || door_percentage > 100) {
                  return that.$message({
                    type: "info",
                    message: "百分比只能为0-100之间，不能为0，可以是100",
                    onClose: function () {
                      that.loading = false;
                    },
                  });
                }
              }
              if (pre_door_mode == 2) {
                if (door_fixed_amount == "" || door_fixed_amount == 0) {
                  return that.$message({
                    type: "info",
                    message: "固定金额不能为空且不能为零",
                    onClose: function () {
                      that.loading = false;
                    },
                  });
                }
                if (door_fixed_amount > price && price != "") {
                  return that.$message({
                    type: "info",
                    message: "固定金额不能大于售价",
                    onClose: function () {
                      that.loading = false;
                    },
                  });
                }
              }
            }
          }
          var pro_shop = that.form.pro_shop;
          var pro_shop_mode = that.form.pro_shop_mode;
          var shop_percentage = that.form.shop_percentage;
          var shop_fixed_amount = Number(that.form.shop_fixed_amount);
          if (pro_shop == 3) {
            if (pro_shop_mode == 1) {
              if (shop_percentage <= 0 || shop_percentage > 100) {
                return that.$message({
                  type: "info",
                  message: "百分比只能为0-100之间，不能为0，可以是100",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
            }
            if (pro_shop_mode == 2) {
              if (shop_fixed_amount == "" || shop_fixed_amount == 0) {
                return that.$message({
                  type: "info",
                  message: "固定金额不能为空且不能为零",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (shop_fixed_amount > price && price != "") {
                return that.$message({
                  type: "info",
                  message: "固定金额不能大于售价",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
            }
          }
          var classification_id = that.form.classification_id;
          var label_id = that.form.label_id;
          var duration = that.form.duration;
          var imgUrl = that.form.imgUrl;
          var radioUrl = that.form.radioUrl ? that.form.radioUrl : "";
          var attribute_s = that.attribute_s;
          var attrTableVal = that.attrTable.val;
          if (attrTableVal) {
            var sxv = [];
            var prices = [];
            var barcode = [];
            var skuattrid = [];
            for (var i = 0; i < attrTableVal.length; i++) {
              var sku = "";
              for (var j = 0; j < attrTableVal[i].length; j++) {
                sku += attrTableVal[i][j] + ",";
              }
              if (!attrTableVal[i]["price"]) {
                return that.$message({
                  type: "info",
                  message: "规格售价不能空",
                  onClose: function () {
                    that.loading = true;
                  },
                });
              }
              if (attrTableVal[i]["price"] <= 0) {
                return that.$message({
                  type: "info",
                  message: "规格售价不能小于0",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (
                door_percentage > 0 &&
                door_percentage * attrTableVal[i]["price"] < 0.01
              ) {
                return that.$message({
                  type: "info",
                  message: "预上门百分比付款，价格必须大于0.01",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (
                door_fixed_amount != "" &&
                door_fixed_amount > attrTableVal[i]["price"]
              ) {
                return that.$message({
                  type: "info",
                  message: "固定预约金额不能大于规格最低价",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (
                shop_fixed_amount != "" &&
                shop_fixed_amount > attrTableVal[i]["price"]
              ) {
                return that.$message({
                  type: "info",
                  message: "固定预约金额不能大于规格最低价",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (!attrTableVal[i]["barcode"]) {
                return that.$message({
                  type: "info",
                  message: "服务规格条码不能为空",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (
                (attrTableVal[i]["price"] * door_percentage) / 100 < 0.01 &&
                door_percentage > 0
              ) {
                return that.$message({
                  type: "info",
                  message: "预上门服务，百分比支付不能少于0.01元！",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              if (
                (attrTableVal[i]["price"] * shop_percentage) / 100 < 0.01 &&
                shop_percentage > 0
              ) {
                return that.$message({
                  type: "info",
                  message: "预到店服务，百分比支付不能少于0.01元！",
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
              sxv.push(sku);
              prices.push(attrTableVal[i]["price"]);
              barcode.push(attrTableVal[i]["barcode"]);
              if (that.form.id != "") {
                skuattrid.push(attrTableVal[i]["id"]);
              }
            }
          }
          if (attrTableVal == undefined && price == "") {
            return that.$message({
              type: "info",
              message: "请输入价格",
              onClose: function () {
                that.loading = false;
              },
            });
          }
          if (
            price != "" &&
            (price * door_percentage) / 100 < 0.01 &&
            door_percentage > 0
          ) {
            return that.$message({
              type: "info",
              message: "预上门服务，百分比支付不能少于0.01元！",
              onClose: function () {
                that.loading = false;
              },
            });
          }
          if (
            price != "" &&
            (price * shop_percentage) / 100 < 0.01 &&
            shop_percentage > 0
          ) {
            return that.$message({
              type: "info",
              message: "预到店服务，百分比支付不能少于0.01元！",
              onClose: function () {
                that.loading = false;
              },
            });
          }
          $.ajax({
            url: "{:url('AdService')}",
            type: "post",
            data: {
              id: id,
              service_name: service_name,
              bar_code: bar_code,
              price: price,
              price_tag: price_tag,
              member_price: that.form.member_price || price,
              classification_id: classification_id,
              label_id: label_id,
              duration: duration,
              imgUrl: imgUrl,
              radioUrl: radioUrl,
              attribute_s: attribute_s,
              sxv: sxv,
              prices: prices,
              skuattrid: skuattrid,
              barcode: barcode,
              shop_display: shop_display,
              door_service: door_service,
              pre_door: pre_door,
              pre_door_mode: pre_door_mode,
              door_percentage: door_percentage,
              door_fixed_amount: door_fixed_amount,
              pro_shop: pro_shop,
              pro_shop_mode: pro_shop_mode,
              shop_percentage: shop_percentage,
              shop_fixed_amount: shop_fixed_amount,
              text: that.content,
              AddGImgs: that.form.AddGImgs,
              adjust_price_id: that.form.adjust_price_id,
            },
            success: function (res) {
              if (res.code == 1) {
                that.$message({
                  type: "success",
                  message: res.msg,
                  onClose: function () {
                    // admin.events.back();
                    that.loading = false;
                    location.hash = "/Service/index";
                  },
                });
              } else {
                that.$message({
                  type: "info",
                  message: res.msg,
                  onClose: function () {
                    that.loading = false;
                  },
                });
              }
            },
          });
        },
        getData(fn) {
          var vm = this;
          var id = vm.form.id;
          if (id != "") {
            vm.loading = true;
            const url = vm.fromStore
              ? "{:url('getstoserfind')}"
              : "{:url('getFind')}";
            $.ajax({
              url: url,
              data: {id: id},
              type: "post",
              success: function (res) {
                if (res.status == 1) {
                  vm.form = res.data;
                  if (res.data.is_sku_img == 1) {
                    vm.form.AddGImgs = true;
                  } else {
                    vm.form.AddGImgs = false;
                  }
                  vm.content = res.data.content;
                  typeof fn == "function" && fn();
                  if (res.data.attribute_s != "") {
                    vm.attribute_s = res.data.attribute_s;
                    // for (var i = 0; i < vm.attribute_s[0]['SpecVal'].length; i++) {
                    //     if (vm.attribute_s[0]['SpecVal'][i]['img']) {
                    //         vm.form.AddGImgs = true;
                    //     }
                    // }
                  }
                  var attrTable = res.data.attrTable;
                  if (attrTable["val"]) {
                    var prices = res.data.prices;
                    var barcode = res.data.barcode;
                    var skuattrid = res.data.skuattrid;
                    for (var i = 0; i < attrTable["val"].length; i++) {
                      attrTable["val"][i]["price"] = prices[i];
                      attrTable["val"][i]["barcode"] = barcode[i];
                      attrTable["val"][i]["id"] = skuattrid[i];
                    }
                    vm.attrTable = attrTable;
                  }
                } else {
                  vm.form.id = "";
                  return vm.$message.error("未找到商品信息，请确认");
                }
                vm.loading = false;
              },
            });
          }
        },
        formEdit() {
          this.$forceUpdate();
        },
        automatic(type, obj, str) {
          var vm = this;
          this.isAutomatic = true;
          $.ajax({
            url: "{:url('Randombarcode/getRandomBarCode')}",
            data: {
              type: type,
            },
            type: "post",
            success(res) {
              if (res.code == 1) {
                obj[str] = res.data.str;
              } else {
                vm.$message({
                  type: "error",
                  message: res.msg,
                });
              }
              vm.isAutomatic = false;
            },
          });
        },
      },
      created: function () {
        this.getifiList();
        this.geLabList();
        this.getAdjustPriceAllData();
        // this.getData();
        var _this = this;
        this.$nextTick(function () {
          _this.editor = UE.getEditor(_this.$refs["ueditorBox"], {
            autoHeight: false,
            autoHeightEnabled: false,
            autoFloatEnabled: false,
          });
          _this.editor.ready(function () {
            _this.getData(function () {
              _this.editor.setContent(_this.content);
            });
            _this.editor.addListener("blur", function () {
              var editorHtml = _this.editor.getContent();
              _this.content = editorHtml;
            });
          });
        });
      },
      watch: {
        // 调价管理
        adjustPriceDialog(n) {
          if (n) {
            this.getAdjustPriceData();
          }
        },
      },
    });
  });
</script>
<style>
  #serviceAdd .el-input {
    max-width: 300px;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #3e63dd;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .img_bor {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .imgurls {
    width: 30px;
    height: 30px;
    font-size: 30px;
    color: rgb(204, 204, 204);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .radio_bor {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .radiourls {
    width: 30px;
    height: 30px;
    font-size: 30px;
    color: rgb(204, 204, 204);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .Simg {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    display: flex;
    /* justify-content: center; */
    align-items: center;
  }

  .Simg:hover > .dlt {
    display: block;
  }

  .dlt {
    position: absolute;
    right: 0;
    width: 20px;
    height: 20px;
    display: none;
    top: -7px;
    right: 3px;
    color: #ccc;
    font-size: 20px;
    z-index: 10;
  }

  .radio_sh {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
  }

  .radio_sh:hover > .dlt {
    display: block;
  }

  .bof {
    width: 18px;
    height: 18px;
    display: flex;
    font-size: 18px;
    border: 1px solid #909399;
    color: #909399;
    border-radius: 50%;
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 10;
  }

  /*规格*/
  .G_boy {
    /*width: 90%;*/
    padding: 10px 1.5%;
    border: 1px solid #e4e7ed;
  }

  .G_top {
    /*width: 97%;*/
    padding: 7px 10px;
    background-color: #f5f7fa;
  }

  .G_bot {
    width: 97%;
    min-height: 20px;
    padding: 10px 1.5%;
  }

  .G_but {
    margin: 0 0 10px;
    display: flex;
    flex-wrap: wrap;
  }

  .G_val {
    width: 80px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #ccc;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 10px;
  }

  .G_ad {
    height: 24px;
    line-height: 24px;
    color: #3e63dd;
    display: flex;
    align-items: center;
  }

  .G_Img {
    width: 80px;
    height: 80px;
    border: 1px solid #ccc;
    margin-top: 10px;
    border-radius: 6px;
  }

  .triangle {
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .triangle:before {
    position: absolute;
    content: "";
    top: -10px;
    left: 30px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #ccc;
  }

  /* 白色覆盖*/
  .triangle:after {
    position: absolute;
    content: "";
    /*减少两像素*/
    top: -9px;
    left: 30px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #fff;
  }

  .G_size {
    height: 26px;
    line-height: 26px;
  }

  .G_One1 {
    position: relative;
    margin-bottom: 10px;
  }

  .G_delval {
    width: 12px;
    height: 12px;
    line-height: 12px;
    position: absolute;
    top: -5px;
    right: 8px;
    cursor: pointer;
  }

  .G_one_del {
    width: 18px;
    height: 18px;
    line-height: 18px;
    position: absolute;
    font-size: 18px;
    color: #999;
    right: 4px;
    top: 18px;
    cursor: pointer;
  }

  .G_Eject {
    width: 220px;
    padding: 10px;
    background-color: rgb(255, 255, 255);
    z-index: 10;
    position: absolute;
  }

  [v-cloak] {
    display: none;
  }

  #addProduct .layui-colla-title {
    height: auto;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .Simg {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
  }

  .Simg:hover > .dlt {
    display: block;
  }

  .dlt {
    position: absolute;
    right: 0;
    width: 20px;
    height: 20px;
    display: none;
    top: -7px;
    right: 3px;
    color: #ccc;
    font-size: 20px;
    z-index: 10;
  }

  .img_bor {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .imgurls {
    width: 30px;
    height: 30px;
    font-size: 30px;
    color: rgb(204, 204, 204);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .radio_sh {
    width: 100px;
    height: 100px;
    cursor: pointer;
    margin: 0px 10px 10px 0px;
    border: 1px dashed rgb(204, 204, 204);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
  }

  .radio_sh:hover > .dlt {
    display: block;
  }

  .bof {
    width: 18px;
    height: 18px;
    display: flex;
    font-size: 18px;
    border: 1px solid #909399;
    color: #909399;
    border-radius: 50%;
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 10;
  }

  .ck-content {
    min-height: 500px;
  }

  .html5-editor-show img {
    max-width: 100%;
  }

  .vue-html5-editor .content img {
    max-width: 100%;
  }

  .goods-details-block {
    padding: 30px 0;
    background: #e5e5e5;
    text-align: center;
    color: #666;
  }

  .goods-details-block h4 {
    margin: 0;
    font-size: 16px;
    line-height: 24px;
  }

  .goods-details-block p {
    margin: 0;
    font-size: 14px;
    line-height: 24px;
  }
</style>
