<style>
  .f-incomesummary-table {
    border-collapse: collapse;
    text-align: center;
    vertical-align: middle;
  }
  .f-incomesummary-table td {
    border: 1px solid rgba(0, 0, 0, 0.3);
    padding: 6px 18px;
  }
  /* 添加金额列右对齐样式 */
  .f-incomesummary-table td[data-is-money="true"] {
    text-align: right;
  }
</style>
<div class="layui-fluid">
  <div id="incomesummary" v-cloak style="height: 100%">
    <el-card class="box-card" shadow="never">
      <div class="flex items-center space-x-4">
        <template v-if="isFactory">
          <div>统计门店</div>
          <el-select
            v-model="storeid"
            filterable
            placeholder="请选择统计门店"
            @change="handleStoreChange"
            size="small"
          >
            <el-option :key="0" label="请选择门店" :value="0"> </el-option>
            <el-option
              v-for="item in storeListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <div class="flex space-x-2 items-center">
          <div>统计月份</div>
          <el-date-picker
            v-model="statisticalMonth"
            type="month"
            size="small"
            placeholder="选择月"
            :clearable="false"
          ></el-date-picker>
        </div>
        <el-button type="primary" size="small" @click="exportData"
          >导出</el-button
        >
      </div>

      <div>
        <div class="p-4 mt-4 h-fit" v-loading="loading">
          <table
            class="f-incomesummary-table"
            v-if="tableData?.channels?.length"
          >
            <colgroup>
              <col />
              <col />
              <col />
              <col v-if="tableData.channels.length===0" />
              <col v-for="(item, index) in tableData.channels" :key="index" />
              <col />
            </colgroup>
            <tbody>
              <tr class="bg-gray-200">
                <td rowspan="2">类别</td>
                <td rowspan="2">名称</td>
                <td rowspan="2">数量</td>
                <td
                  style="text-align: center"
                  :colspan="tableData.channels.length || 1"
                >
                  收款渠道
                </td>
                <td rowspan="2">备注</td>
              </tr>
              <tr class="bg-gray-200">
                <td v-if="tableData.channels.length===0">-</td>
                <td
                  v-for="(item,index) in tableData.channels"
                  :key="'channel-header-'+index"
                >
                  {{item}}
                </td>
              </tr>
              <tr>
                <td :rowspan="tableData.products?.lists.length || 1">
                  仪器销售类
                </td>
                <td>{{tableData.products?.lists[0]?.title || '-'}}</td>
                <td>{{tableData.products?.lists[0]?.quantity || 0}}</td>
                <td v-if="!tableData.products?.lists[0]?.moneyList?.length">
                  -
                </td>
                <td
                  v-for="(item,index) in tableData.products?.lists[0]?.moneyList"
                  :key="'product-first-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td>{{tableData.products?.lists[0]?.remark || '-'}}</td>
              </tr>
              <tr
                v-for="(item,index) in tableData.products?.lists.slice(1)"
                :key="'product-list-'+index"
              >
                <td>{{item.title || '-'}}</td>
                <td>{{item.quantity || 0}}</td>
                <td v-if="!item.moneyList?.length">-</td>
                <td
                  v-for="(money,index) in item.moneyList"
                  :key="'product-list-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(money)}}
                </td>
                <td>{{item.remark || '-'}}</td>
              </tr>
              <tr class="bg-gray-200">
                <td>仪器小计</td>
                <td colspan="2">小计</td>
                <td v-if="!tableData.products?.totals?.length">0</td>
                <td
                  v-for="(item,index) in tableData.products?.totals"
                  :key="'product-total-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td></td>
              </tr>
              <tr>
                <td :rowspan="tableData.services?.lists.length || 1">
                  服务项目卡项类
                </td>
                <td>{{tableData.services?.lists[0]?.title || '-'}}</td>
                <td>{{tableData.services?.lists[0]?.quantity || 0}}</td>
                <td v-if="!tableData.services?.lists[0]?.moneyList?.length">
                  -
                </td>
                <td
                  v-for="(item,index) in tableData.services?.lists[0]?.moneyList"
                  :key="'service-first-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td>{{tableData.services?.lists[0]?.remark || '-'}}</td>
              </tr>
              <tr
                v-for="(item,index) in tableData.services?.lists.slice(1)"
                :key="'service-list-'+index"
              >
                <td>{{item.title || '-'}}</td>
                <td>{{item.quantity || 0}}</td>
                <td v-if="!item.moneyList?.length">-</td>
                <td
                  v-for="(money,index) in item.moneyList"
                  :key="'service-list-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(money)}}
                </td>
                <td>{{item.remark || '-'}}</td>
              </tr>
              <tr class="bg-gray-200">
                <td>服务卡项小计</td>
                <td colspan="2">小计</td>
                <td v-if="!tableData.services?.totals?.length">0</td>
                <td
                  v-for="(item,index) in tableData.services?.totals"
                  :key="'service-total-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td></td>
              </tr>
              <tr>
                <td :rowspan="tableData.selfServices?.lists.length || 1">
                  理疗卡
                </td>
                <td>{{tableData.selfServices?.lists[0]?.title || '-'}}</td>
                <td>{{tableData.selfServices?.lists[0]?.quantity || 0}}</td>
                <td v-if="!tableData.selfServices?.lists[0]?.moneyList?.length">
                  -
                </td>
                <td
                  v-for="(item,index) in tableData.selfServices?.lists[0]?.moneyList"
                  :key="'self-service-first-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td>{{tableData.selfServices?.lists[0]?.remark || '-'}}</td>
              </tr>
              <tr
                v-for="(item,index) in tableData.selfServices?.lists.slice(1)"
                :key="'self-service-list-'+index"
              >
                <td>{{item.title || '-'}}</td>
                <td>{{item.quantity || 0}}</td>
                <td v-if="!item.moneyList?.length">-</td>
                <td
                  v-for="(money,index) in item.moneyList"
                  :key="'self-service-list-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(money)}}
                </td>
                <td>{{item.remark || '-'}}</td>
              </tr>
              <tr class="bg-gray-200">
                <td>理疗卡小计</td>
                <td colspan="2">小计</td>
                <td v-if="!tableData.selfServices?.totals?.length">0</td>
                <td
                  v-for="(item,index) in tableData.selfServices?.totals"
                  :key="'self-service-total-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td></td>
              </tr>
              <tr>
                <td :rowspan="tableData.notCardServices?.lists.length || 1">
                  非卡服务类
                </td>
                <td>{{tableData.notCardServices?.lists[0]?.title || '-'}}</td>
                <td>{{tableData.notCardServices?.lists[0]?.quantity || 0}}</td>
                <td
                  v-if="!tableData.notCardServices?.lists[0]?.moneyList?.length"
                >
                  -
                </td>
                <td
                  v-for="(item,index) in tableData.notCardServices?.lists[0]?.moneyList"
                  :key="'not-card-service-first-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td>{{tableData.notCardServices?.lists[0]?.remark || '-'}}</td>
              </tr>
              <tr
                v-for="(item,index) in tableData.notCardServices?.lists.slice(1)"
                :key="'not-card-service-list-'+index"
              >
                <td>{{item.title || '-'}}</td>
                <td>{{item.quantity || 0}}</td>
                <td v-if="!item.moneyList?.length">-</td>
                <td
                  v-for="(money,index) in item.moneyList"
                  :key="'not-card-service-list-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(money)}}
                </td>
                <td>{{item.remark || '-'}}</td>
              </tr>
              <tr class="bg-gray-200">
                <td>非卡服务类小计</td>
                <td colspan="2">小计</td>
                <td v-if="!tableData.notCardServices?.totals?.length">0</td>
                <td
                  v-for="(item,index) in tableData.notCardServices?.totals"
                  :key="'not-card-service-total-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td></td>
              </tr>
              <tr>
                <td :rowspan="tableData.others?.lists.length || 1">其他类</td>
                <td>{{tableData.others?.lists[0]?.title || '-'}}</td>
                <td>{{tableData.others?.lists[0]?.quantity || 0}}</td>
                <td v-if="!tableData.others?.lists[0]?.moneyList?.length">-</td>
                <td
                  v-for="(item,index) in tableData.others?.lists[0]?.moneyList"
                  :key="'other-first-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td>{{tableData.others?.lists[0]?.remark || '-'}}</td>
              </tr>
              <tr
                v-for="(item,index) in tableData.others?.lists.slice(1)"
                :key="'other-list-'+index"
              >
                <td>{{item.title || '-'}}</td>
                <td>{{item.quantity || 0}}</td>
                <td v-if="!item.moneyList?.length">-</td>
                <td
                  v-for="(money,index) in item.moneyList"
                  :key="'other-list-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(money)}}
                </td>
                <td>{{item.remark || '-'}}</td>
              </tr>
              <tr class="bg-gray-200">
                <td>其他类小计</td>
                <td colspan="2">小计</td>
                <td v-if="!tableData.others?.totals?.length">0</td>
                <td
                  v-for="(item,index) in tableData.others?.totals"
                  :key="'other-total-'+index"
                  data-is-money="true"
                >
                  {{formatMoney(item)}}
                </td>
                <td></td>
              </tr>
              <tr class="font-bold bg-gray-200">
                <td>汇总合计</td>
                <td colspan="2">总计</td>
                <td v-if="tableData.channels.length===0">0</td>
                <td :colspan="tableData.channels.length">
                  {{formatMoney(tableData.total)}}
                </td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-card>
  </div>
</div>
<script type="text/javascript" src="js/xlsx.full.min.js"></script>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#incomesummary",
      data() {
        return {
          // 默认上个月
          statisticalMonth: new Date(
            new Date().setMonth(new Date().getMonth() - 1)
          ),
          loading: false,
          tableData: {
            channels: [],
            total: 0,
            products: {
              lists: [],
              totals: [],
            },
            services: {
              lists: [],
              totals: [],
            },
            selfServices: {
              lists: [],
              totals: [],
            },
            others: {
              lists: [],
              totals: [],
            },
          },
          isFactory: false,
          storeid: 0,
          storeList: [],
        };
      },
      computed: {
        storeListOptions() {
          return this.storeList.map((item) => ({
            value: item.id,
            label: item.storetag,
          }));
        },
      },
      methods: {
        getStoreList() {
          var vm = this;
          $.ajax({
            url: "{:url('/store/tab_store/getstorelist')}",
            type: "POST",
            success: function (res) {
              if (res.code == 0) {
                vm.storeList = res.data;
              }
            },
          });
        },
        clearTableData() {
          this.tableData = {
            channels: [],
            total: 0,
            products: {
              lists: [],
              totals: [],
            },
            services: {
              lists: [],
              totals: [],
            },
            selfServices: {
              lists: [],
              totals: [],
            },
            others: {
              lists: [],
              totals: [],
            },
          };
        },
        getTableData() {
          var vm = this;
          vm.clearTableData();
          if (!vm.storeid) return;
          vm.loading = true;
          $.ajax({
            url: "{:url('Financialstatements/getIncomeSummaryData')}",
            data: {
              storeid: vm.storeid,
              year: vm.statisticalMonth.getFullYear(),
              month: vm.statisticalMonth.getMonth() + 1,
            },
            type: "POST",
            success: function (res) {
              if (res.code == 1) {
                vm.tableData = res.data;
              } else {
                vm.$message.error(res.msg);
              }
            },
            error: function () {
              vm.$message.error("获取数据失败");
            },
            complete: function () {
              vm.loading = false;
            },
          });
        },
        // 格式化金额
        formatMoney(money) {
          const num = Number(money);
          const isNegative = num < 0;
          const absNum = Math.abs(num);
          const formatted = absNum.toLocaleString("en-US", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          });
          return (isNegative ? "-￥" : "￥") + formatted;
        },
        exportData() {
          // 获取当前月份作为文件名的一部分
          const year = this.statisticalMonth.getFullYear();
          const month = String(this.statisticalMonth.getMonth() + 1).padStart(
            2,
            "0"
          );
          const fileName = `收入汇总表_${year}年${month}月.xlsx`;

          // 获取表格DOM元素
          const table = document.querySelector(".f-incomesummary-table");

          // 从表格DOM创建工作表
          const ws = XLSX.utils.table_to_sheet(table);

          // 创建工作簿
          const wb = XLSX.utils.book_new();

          // 将工作表添加到工作簿
          XLSX.utils.book_append_sheet(wb, ws, `${year}年${month}月收入汇总`);

          // 导出工作簿为Excel文件
          XLSX.writeFile(wb, fileName);
        },
        handleStoreChange(value) {
          this.storeid = value;
          this.getTableData();
        },
      },
      watch: {
        statisticalMonth(newVal) {
          // 格式化日期为 YYYY-MM 格式
          const year = newVal.getFullYear();
          const month = String(newVal.getMonth() + 1).padStart(2, "0");
          this.getTableData();
        },
      },
      created: function () {
        this.storeid = layui.S_USER_TOKEN.storeid;
        this.isFactory = !this.storeid;
        if (this.isFactory) {
          this.getStoreList();
        } else {
          this.getTableData();
        }
      },
    });

    // $("*[]")
  });
</script>
