<!--[meiye_0824]-->
<style type="text/css">
    .audbox {
        box-sizing: border-box;
        padding-top: 100px;
    }
    .div_a {
        width: 70%;
        height: 8px;
        background: gainsboro;
        border-radius: 10px;
        margin: auto;
        position: relative;
    }
    .div_a_bg{
        width: 100%;
        height: 100%;
        background: #1ab394;
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 10px;
    }
    .div_a01{
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 1px solid #1AB394;
        position: absolute;
        left: 16%;
        top: -21px;
        box-sizing: border-box;
        padding: 5px;
    }
    .div_a_inner{
        width: 100%;
        height: 100%;
        color: white;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #1AB394;
        border-radius: 50%;
        font-weight: bold;
    }
    .div_a02{
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 1px solid #1AB394;
        position: absolute;
        left: 47%;
        top: -21px;
        box-sizing: border-box;
        padding: 5px;
    }
    .div_a03{
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 1px solid #1AB394;
        position: absolute;
        left: 80%;
        top: -21px;
        box-sizing: border-box;
        padding: 5px;
    }
    .div_a_txt{
        width: 50px;
        height: 50px;
        position: absolute;
        left: -3%;
        top: 42px;
        text-align: center;
        line-height: 50px;
    }
    .div_b{
        width: 100%;
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        padding-top: 90px;
    }
    .p{
        text-align: center;
    }
    .div_c {
        box-sizing: border-box;
        padding: 20px 0;
        display: flex;
        justify-content: center;
    }
</style>
<div id="box" class="audbox" v-loading="xcxconfigloading">
    <div class="div_a">
        <div class="div_a_bg"></div>
        <div class="div_a01">
            <div class="div_a_inner">1</div>
            <div class="div_a_txt">申请</div>
        </div>
        <div class="div_a02">
            <div class="div_a_inner">2</div>
            <div class="div_a_txt">审核</div>
        </div>
        <div class="div_a03">
            <div class="div_a_inner">3</div>
            <div class="div_a_txt">发布</div>
        </div>
    </div>
    <div class="div_b">
        <img src="/assets/img/lb.png"/>
    </div>
    {if condition="$cache['fb_status'] eq 1"}
        <p class="p">{$info["nick_name"]}小程序已发布成功，您的所有门店均可使用小程序功能</p>
    {else /}
        <p class="p">{$info["nick_name"]}小程序已成功审核，请点击发布进行全网发布操作</p>
    {/if}
    <div class="div_c">
        <el-radio-group v-model="xcxType" size="small">
            <el-radio-button :label="0">普通版</el-radio-button>
            <el-tooltip class="item" effect="dark" placement="top">
                <div slot="content">
                    <p>直播版需要小程序开通直播功能</p>
                </div>
                <el-radio-button :label="1">直播版</el-radio-button>
            </el-tooltip>
        </el-radio-group>
    </div>
    <div class="div_c">
        <el-button type="primary" @click="review()"  id="preview">预览二维码</el-button>
        <el-button type="primary" id="reload" @click="url()">重新授权</el-button>
        {if condition="$cache['fb_status'] eq 0"}
            <el-button type="warning" id="Release" @click="Release()">发布</el-button>
        {/if}
        {if condition="$newVersion"}
        <el-button type="primary" id="setprive" @click="setprive"
              >设置隐私指引</el-button
            >
            <el-button type="primary" id="getprive" @click="getprive"
              >查看隐私指引</el-button
            >
            <el-button type="warning" @click="aduit" id="newVer">提交新版本</el-button>
        {/if}
    </div>
    <template>
        <el-dialog width="250px"  :modal-append-to-body="false" title="预览小程序" :visible.sync="reviewOpen">
            <img :src="img_src" style="width: 100%">

            <div slot="footer" class="dialog-footer">
                <el-button @click="reviewOpen = false">取 消</el-button>
                <el-button type="primary" @click="innerVisible = true">添加预览者</el-button>
            </div>
        </el-dialog>
        <el-dialog
                :modal-append-to-body="false"
                width="30%"
                title="请填写体验者微信号"
                :visible.sync="innerVisible">
            <el-input
                    placeholder="请输入体验者的微信号"
                    v-model="we_id"
                    clearable>
            </el-input>
            <div slot="footer" class="dialog-footer">
                <el-button @click="innerVisible = false">取 消</el-button>
                <el-button type="primary" @click="bindUser">提交</el-button>
            </div>
        </el-dialog>
    </template>
</div>
</html>
{include file="xcx_config/xcxVue" /}