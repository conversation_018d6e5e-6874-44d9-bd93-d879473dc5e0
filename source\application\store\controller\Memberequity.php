<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019-06-17
 * Time: 10:28
 */

namespace app\store\controller;

use app\store\model\Couponrecord as CouponrecordModel;
use app\store\model\MemberEquityRecord as MemEqRecordModel;
use app\store\model\Member as MemberModel;
use app\api\model\UserBindStore as UserBindStoreModel;

use app\store\model\Staff as StaffModel;
use think\Cache;
class Memberequity extends Controller
{
    public function index(){
        $userInfo = $this->userinfo;
        $storeid = $userInfo['storeid'];
        $this->assign('storeid', $storeid);
        if ($storeid == 0) {
            $isStore = 0;
        } else {
            $isStore = 1;
        }
        $this->assign('isStore', $isStore);
        $this->outPut('memberequity/index');
    }

    public function getMemberEquityDetailData(){
        try {
            $post = input('post.');
            $startTime = $post['startTime'];
            $endTime = $post['endTime'];
            if ($startTime == 0 || $endTime == 0) {
                switch ($post['type']) {
                    case 0:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400;
                        break;
                    case 1:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00');
                        $startTime = $endTime - 86400;
                        break;
                    case 2:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = $endTime - 86400 * 7;
                        break;
                    case 3:
                        $endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
                        $startTime = strtotime(date('Y-m') . '-1 00:00:00');
                        break;
                }
            } else {
                $startTime = strtotime($startTime . ' 00:00:00');
                $endTime = strtotime($endTime . ' 23:59:59') + 1;
            }
            $userInfo = $this->userinfo;
            $where = array(
                'addtime' => array('between', array($startTime, $endTime)),
                'merchantid' => $userInfo['merchantid'],
                'storeid' => $post['storeId']
            );
            if(isset($post['checkedType']) && $post['checkedType']){
                $where['type'] = $post['checkedType'];
            }
            if(isset($post['plan_type']) && $post['plan_type']){
                $where['plan_type'] = $post['plan_type'];
            }

            if (isset($post['keyword'])&&$post['keyword'] != '') {
                $uwhere['member_name|remarks_name|member_number|phone'] = array('like', '%' . $post['keyword'] . '%');
                if ($post['storeId'] != 0) {
                    $ubswhere['storeid']=$post['storeId'];
                    $ubsfield="userid";
                    $UserBindStoreModel=new UserBindStoreModel;
                    $userlist=$UserBindStoreModel->getfieldList($ubswhere, $ubsfield);
                    $uwhere['id'] = ['in', $userlist];//
                }
            
                $ubsfield="id";
                $MemberModel =new MemberModel;
                $memberlist=$MemberModel->getfieldList($uwhere, $ubsfield);
                $where['member_id'] = ['in', $memberlist];//
            }
                

            $MemEqRecordModel = new MemEqRecordModel;
            $page = $post['page'];
            $limit = $post['limit'];
            $field = array(
                'id',
                'goodsinfo as goodsInfo',
                'member_id as memberInfo',
                'member_card_id as cardInfo',
                'order_id',
                'order_id as orderInfo',
                'promotion_name as promotionName',
                'promotion_price as promotionPrice',
                'storeid as storeInfo',
                'type',
                'type as typeName',
                'addtime'
            );
            $append = array(

            );
            $data = $MemEqRecordModel->getListByPage($where, $page, $limit, $field, $append);
            $count = $MemEqRecordModel->getCount($where);
            return $this->ajaxTable($data, $count);
        } catch (\Exception $e) {
            $error = $e->getMessage();
            return $this->ajaxTable(array(), 0, $error);
        }
    }

	public function guestplan(){
		$userInfo = $this->userinfo;
		$storeid = $userInfo['storeid'];
		$this->assign('storeid', $storeid);
		if ($storeid == 0) {
			$isStore = 0;
		} else {
			$isStore = 1;
		}
		$this->assign('isStore', $isStore);
		$this->outPut('memberequity/guestplan');
	}

	public function getReceivedInfo(){
		$user = $this->userinfo;
		$CouponrecordModel = new CouponrecordModel;
		$where['merchantid'] = $user['merchantid'];
		$post = input('post.');
		$startTime = $post['startTime'];
		$endTime = $post['endTime'];
		if ($startTime == 0 || $endTime == 0) {
			switch ($post['type']) {
				case 0:
					$endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
					$startTime = $endTime - 86400;
					break;
				case 1:
					$endTime = strtotime(date('Y-m-d') . ' 00:00:00');
					$startTime = $endTime - 86400;
					break;
				case 2:
					$endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
					$startTime = $endTime - 86400 * 7;
					break;
				case 3:
					$endTime = strtotime(date('Y-m-d') . ' 00:00:00')+86400;
					$startTime = strtotime(date('Y-m') . '-1 00:00:00');
					break;
			}
		} else {
			$startTime = strtotime($startTime . ' 00:00:00');
			$endTime = strtotime($endTime . ' 23:59:59') + 1;
		}
		$field = array(
			'*',
			'order_id as orderInfo'
		);
		$order = 'get_time desc';
		$where['use_time'] = array('between', array($startTime, $endTime));
		$where['status'] = 3;
		if(isset($post['storeId']) && $post['storeId']){
			$where['used_store_id'] = $post['storeId'];
		}
		if(isset($post['plan_type']) && $post['plan_type']){
			$where['plan_type'] = $post['plan_type'];
			$order = 'use_time desc';
		}
		$count = $CouponrecordModel->getCount($where);
		$where['page'] = $post['page'];
		$where['limit'] = $post['limit'];
		$append = array(
			'expireTime',
			'memberInfo'
		);
		$list = $CouponrecordModel->getAll($where, $field, $order, $append);
		foreach ($list as $k=>&$v) {
			$v['price'] = number_format($v['price']/100, 2,'.', '');
			if ($v['status'] != 3 && $v['endtime'] < time()){
				$v['status'] = 5;
			}
		}
		if ($list!==false) {
			return $this->ajaxTable($list, $count);
		} else {
			return $this->ajaxFail('查询失败');
		}
	}
}