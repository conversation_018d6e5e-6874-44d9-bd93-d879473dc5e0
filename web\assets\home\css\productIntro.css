/* banner */

.banner-box {
  width: 100%;
  position: relative;
  display: none;
}

.txt-white {
  color: #fff !important;
}

.productTitle {
  font-size: 50px;
  line-height: 50px;
  border-bottom: 5px solid #fff;
  padding-bottom: 15px;
}

.banner-p {
  font-size: 18px;
  line-height: 28px;
  margin-top: 40px;
}

.banner-txt {
  position: absolute;
  width: 30%;
  top: 35%;
  left: 15%;
}

/* 导航栏 */

.checked {
  background-color: #b35dcc !important;
  color: #fff !important;
}

.nav-pills > li {
  height: 80px;
  color: #333;
  border-radius: 4px;
  position: relative;
  font-size: 20px;
  line-height: 80px;
  text-align: center;
}

.nav-icon {
  position: absolute;
  top: 80px;
  left: 45%;
  width: 0;
  height: 0;
  border: 20px solid;
  border-color: #b35dcc transparent transparent transparent;
  display: none;
}

/* 线上商城-玉玄宫门店 */

.smartBeauty {
  background: #f6f6f6;
  padding: 3% 0;
}

.f1-left-list {
  width: 100%;
  margin-bottom: 11%;
}

.f1-icon {
  width: 100px;
  height: 100px;
}

.f1-img {
  width: 80%;
}

.f2-img {
  width: 100%;
}

.f1-txt {
  width: 75%;
  margin-top: 2%;
  margin-left: 3%;
}

.f1-txt1 {
  font-size: 22px;
  color: #333;
}

.f1-txt2 {
  font-size: 18px;
  color: #999;
}

/* 线上商城-小程序 */

.f2-box {
  background-image: url(../imgs/pro-intro-imgs/pro-li1/f2bg.png);
  background-size: 100% 100%;
  padding: 3% 0;
}

/*线上商城- 精准定位 */

.position-p1 {
  font-size: 30px;
  color: #333;
  margin-top: 35%;
}

.position-p2 {
  font-size: 18px;
  color: #999;
}

/*  */

/* 线上商城-内容分享 */

.share-box {
  background-image: url(../imgs/pro-intro-imgs/pro-li1/share.png);
  background-size: 100% 100%;
  padding: 3% 0;
}

.img-share {
  width: 75%;
}

/* 线上商城-预约模式 */

.order-col {
  margin: 20px 0;
}

/* 线上商城-预约模式 */

.getGuest {
  background: #f6f6f6;
  padding: 5% 0;
}

/* 门店收银- 玉玄宫门店 线下收银*/

.cash-img-f1 {
  width: 80%;
}

.memeber-img1 {
  width: 100%;
}

/* 门店收银-开单/充卡*/

.cash-f2-img {
  width: 100%;
}

.f2-col-txt {
  margin-top: 4%;
  box-sizing: border-box;
  padding: 0 5%;
}

/* 门店收银-f4 */

.cash-f4-img {
  position: relative;
  width: 100%;
}

.cash-f4-p1,
.cash-f4-p2,
.cash-f4-p3,
.cash-f4-p4,
.cash-f4-p5,
.cash-f4-p6 {
  position: absolute;
  width: 20%;
  height: 40px;
}

.cash-f4-p1 {
  top: 2%;
  left: 7%;
}

.cash-f4-p2 {
  top: 44%;
  left: 4%;
}

.cash-f4-p3 {
  top: 87%;
  left: 7%;
}

.cash-f4-p4 {
  top: 4%;
  left: 71%;
}

.cash-f4-p5 {
  top: 44%;
  left: 75%;
}

.cash-f4-p6 {
  top: 88%;
  left: 71%;
}

/* cash-f5 */

.cash-f5 {
  background-image: url(../imgs/pro-intro-imgs/cash-li2/f5bg.png);
  background-size: 100% 100%;
  padding: 3% 0;
}

.cash-f5-img {
  width: 100%;
  position: relative;
}

.cash-f5-txt {
  position: absolute;
  top: 32%;
  left: 35%;
}

.cash-f5-icon {
  width: 110px;
}

.cash-f5-p {
  margin-top: 20px;
}

.cash-f5-tranlate {
  transform: translateY(-140px);
}

/* cash-f6 */

.cash-f6-p1 {
  font-size: 20px;
  color: #333;
}

.cash-f6-p2 {
  font-size: 16px;
  color: #666;
}

.cash-f6 {
  margin: 3% 0;
}

.cash-f6-img1 {
  width: 100%;
  margin-top: 3%;
}

.cash-f6-img2 {
  box-sizing: border-box;
  padding-left: 3%;
  padding-top: 6%;
}

.cash-f6-list1 {
  box-sizing: border-box;
  padding-left: 3%;
  padding-top: 16%;
}

.cash-f6-list2 {
  box-sizing: border-box;
  padding-left: 3%;
  padding-top: 10%;
}

.cash-f6-list3 {
  box-sizing: border-box;
  padding-left: 3%;
  padding-top: 9%;
}

/* 兼容市面收银设备  cash-f7 让钱向你飞来 */

.swiper-bg {
  width: 85%;
  background-color: #fff;
  border-radius: 15px;
  box-sizing: border-box;
  padding: 2% 0;
}

.f7-img {
  width: 35%;
}

.cash-f7 {
  background-image: url(../imgs/pro-intro-imgs/cash-li2/f7bg.png);
  background-size: 100% 100%;
  padding: 3% 0;
}

/* 后台管理 */

.backf1-icon {
  width: 100px;
  margin: 10% 0;
}

.backf1-icon:hover {
  margin-top: -10px;
  transition: all 0.6s linear;
}

.backstage-f2-img {
  width: 100%;
}

.ptb {
  padding: 3% 0;
}

.mt {
  margin-top: 30%;
}

.pl {
  padding-left: 3%;
}

.mt2 {
  margin-top: 20%;
}

/* 我们的目标 */

.ourt1 {
  font-size: 28px;
  color: #fff;
}

.ourt2 {
  font-size: 20px;
  color: #fff;
  margin-bottom: 35px;
}

.ourAim {
  background-image: url(../imgs/news-imgs/aimBg.png);
  background-size: 100% 100%;
  padding: 3% 0;
}

.caseshowbtn {
  width: 200px;
  height: 50px;
  background-color: #fff;
  color: #b35dcc;
  box-sizing: border-box;
  padding: 0 35px;
  font-size: 16px;
  line-height: 50px;
}

.ourt1phone {
  font-size: 20px;
  color: #fff;
}

.ourt2phone {
  font-size: 16px;
  color: #fff;
}

.caseshowbtn2 {
  display: inline-block;
  width: 200px;
  height: 40px;
  background-color: #fff;
  color: #b35dcc;
  box-sizing: border-box;
  padding: 0 35px;
  font-size: 16px;
  line-height: 40px;
  margin-bottom: 7px;
}

/* click-before */

.li-cont1 {
  display: block;
}

.li-content {
  display: none;
}

.nav > li > a:focus {
  background-color: #b35dcc;
  color: #fff;
}

@media screen and (max-width: 767px) {
  .productTitle {
    font-size: 20px;
    line-height: 20px;
    border-bottom: 3px solid #fff;
    padding-bottom: 10px;
  }

  /* daohanglan */
  .nav-pills > li {
    float: left;
    width: 24.5%;
    color: #fff;
    border-radius: 4px;
    position: relative;
    height: 58px;
  }

  .nav-pills > li > a {
    font-size: 14px;
    color: #333;
    line-height: 45px;
    padding: 7px 10px;
  }

  h2 {
    font-size: 20px;
  }

  .f1-icon {
    width: 60px;
    height: 60px;
  }

  .f1-txt2 {
    font-size: 14px;
  }

  .f1-txt1 {
    font-size: 20px;
  }

  .f2-img {
    margin-bottom: 10px;
  }

  .position-p1 {
    font-size: 20px;
    text-align: center;
    margin-top: 5%;
  }

  .position-p2 {
    font-size: 14px;
    text-align: center;
  }

  .order-img {
    margin-bottom: 15px;
  }

  .cash-f3-h2 {
    text-align: center;
  }

  .backf1-icon {
    width: 70px;
  }

  .backstage-f2-img {
    width: 95%;
    margin-bottom: 15px;
  }

  .mt {
    margin-top: 2%;
    text-align: center;
  }

  .mt2 {
    margin-top: 2%;
    text-align: center;
  }

  .txt-left {
    text-align: left;
  }
}
